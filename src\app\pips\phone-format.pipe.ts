import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'phoneFormat'
})
export class PhoneFormatPipe implements PipeTransform {
  transform(value: string): string {
    if (!value) return '';

    // Clean value: keep + and digits only
    const cleaned = value.replace(/[^\d+]/g, '');

    if (cleaned.startsWith('0')) {
      // Format: 0x xx xx xx xx
      const digits = cleaned.replace(/\D/g, '');
      return digits
        .replace(/^(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})$/, '$1 $2 $3 $4 $5')
        .trim();
    }

    if (cleaned.startsWith('+')) {
      // Extract + and digits
      const digitsOnly = cleaned.replace(/\D/g, '');

      // Try to extract 3-digit or 2-digit country code
      let countryCode = '';
      let rest = '';

      if (digitsOnly.length >= 12) {
        countryCode = digitsOnly.slice(0, 3); // +xxx
        rest = digitsOnly.slice(3);
      } else {
        countryCode = digitsOnly.slice(0, 2); // +xx
        rest = digitsOnly.slice(2);
      }

      const restFormatted = rest
        .replace(/^(\d{1})(\d{2})(\d{2})(\d{2})(\d{2})$/, '$1 $2 $3 $4 $5')
        .trim();

      return `+${countryCode} ${restFormatted}`;
    }

    return value;
  }
}
