import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { HttpClient } from '@angular/common/http';
import { Controller } from '@app/core/models/controller';
import { Capteur } from '@app/core/models/capteur';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class CapteurApiService extends ApiService<Capteur> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('capteur');
  }

  getCapteursByLocalIdAndControllerID(
    localId: string,
    controllerId: string
  ): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}capteur/local/${localId}/controllerId/${controllerId}`,
      {}
    );
  }

  getUnusedCapteurs(controllerId : string) {
    return this.http.get<any>(
      `${this.baseUrl}capteur/unused-sensors/${controllerId}`,
      {}
    );
  }
}
