<!-- Updated rule-form.component.html - No Tailwind Classes -->
<div class="dialog-container">
    <!-- <PERSON><PERSON> -->
    <div class="dialog-header">
        <h1 class="main-title">
            <svg class="main-title-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                </path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            Générateur de Règles IoT
        </h1>

        <!-- Close button -->
        <button mat-icon-button (click)="onCancel()" class="close-button">
            <mat-icon>close</mat-icon>
        </button>
    </div>

    <!-- Dialog Content -->
    <div class="dialog-content" [class.pointer-events-none]="isBusy">
        <!-- Loading State -->
        <ngx-ui-loader *ngIf="!isLoading"></ngx-ui-loader>

        <!-- Error State -->
        <div *ngIf="loadingError && !isLoading" class="error-banner">
            <div class="error-banner-content">
                <svg class="error-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clip-rule="evenodd"></path>
                </svg>
                <div>
                    <h3 class="error-title">Erreur de chargement</h3>
                    <p class="error-message">{{ loadingError }}</p>
                </div>
            </div>
            <div class="error-actions">
                <button (click)="retryLoadData()" class="retry-button">
                    Réessayer
                </button>
            </div>
        </div>

        <!-- Main Content (only show when not loading and no error) -->
        <div *ngIf="!isLoading && !loadingError">
            <!-- Configuration de base -->
            <div class="config-grid config-section">
                <div class="form-field">
                    <label class="field-label">
                        Nom de la règle
                    </label>
                    <input type="text" [(ngModel)]="rule.rule_name" (ngModelChange)="updateJsonPreview()"
                        class="text-input" placeholder="Ex: Cube Side 1 and Temperature high" />
                </div>
                <div class="form-field">
                    <label class="field-label">
                        Topics utilisés
                    </label>
                    <div class="topics-display">
                        <div *ngIf="rule.topic_pattern.length === 0" class="topics-placeholder">
                            Les topics seront ajoutés automatiquement selon les appareils sélectionnés
                        </div>
                        <div *ngFor="let topic of rule.topic_pattern" class="topic-item">
                            {{ topic }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="config-grid config-section">
                <div class="form-field">
                    <label class="field-label">
                        Priorité
                    </label>
                    <select [(ngModel)]="rule.priority" (ngModelChange)="updateJsonPreview()" class="select-input">
                        <option *ngFor="let p of priorities" [value]="p">Priorité {{ p }}</option>
                    </select>
                </div>
                <div class="checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" [(ngModel)]="rule.enabled" (ngModelChange)="updateJsonPreview()" />
                        <span class="checkbox-text">Règle activée</span>
                    </label>
                </div>
            </div>

            <!-- Conditions with Drag & Drop -->
            <div class="section">
                <div class="section-header">
                    <h2 class="section-title">
                        <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        Conditions
                    </h2>
                    <button (click)="addConditionGroup()" class="add-button">
                        <svg class="button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Groupe
                    </button>
                </div>

                <!-- Global operator selector -->
                <div class="global-operator">
                    <label class="operator-label">Opérateur global :</label>
                    <select [(ngModel)]="rule.conditions.operator" class="operator-select">
                        <option *ngFor="let op of operator" [value]="op">{{ op }}</option>
                    </select>
                    <span class="operator-hint">(Tous les groupes seront reliés par cet opérateur)</span>
                </div>

                <!-- Draggable Condition Groups -->
                <div cdkDropList [id]="'condition-groups-list-' + dialogRef.id"
                    [cdkDropListData]="rule.conditions.groups" (cdkDropListDropped)="onGroupDrop($event)"
                    class="condition-groups-list">

                    <div *ngFor="let group of rule.conditions.groups; let groupIndex = index; trackBy: trackByGroupIndex"
                        cdkDrag [cdkDragData]="group" [id]="'condition-group-' + groupIndex + '-' + dialogRef.id"
                        class="condition-group">

                        <!-- Drag Handle for Group -->
                        <div cdkDragHandle class="drag-handle group-drag-handle"
                            title="Glisser pour réorganiser les groupes">
                            <svg class="drag-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>

                        <!-- Group Header -->
                        <div class="group-header">
                            <div class="group-info">
                                <span class="group-label">Groupe {{ groupIndex + 1 }}</span>
                                <select [(ngModel)]="group.operator" class="group-operator-select">
                                    <option *ngFor="let op of operator" [value]="op">{{ op }}</option>
                                </select>
                            </div>
                            <div class="group-actions">
                                <button (click)="addCondition(groupIndex)" class="add-button small">
                                    <svg class="button-icon small" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Condition
                                </button>
                                <button (click)="addTimeCondition(groupIndex)" class="add-button small">
                                    <svg class="button-icon small" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Horaire
                                </button>
                                <button *ngIf="rule.conditions.groups.length > 1"
                                    (click)="removeConditionGroup(groupIndex)" class="delete-button">
                                    <svg class="button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Draggable Conditions in Group -->
                        <div cdkDropList [id]="'conditions-list-' + groupIndex + '-' + dialogRef.id"
                            [cdkDropListData]="group.conditions"
                            (cdkDropListDropped)="onConditionDrop($event, groupIndex)" class="conditions-list">

                            <div *ngFor="let condition of group.conditions; let conditionIndex = index; trackBy: trackByConditionIndex"
                                cdkDrag [cdkDragData]="condition"
                                [id]="'condition-' + groupIndex + '-' + conditionIndex + '-' + dialogRef.id"
                                class="condition-item">

                                <!-- Drag Handle for Condition -->
                                <div cdkDragHandle class="drag-handle condition-drag-handle"
                                    title="Glisser pour réorganiser les conditions">
                                    <svg class="drag-icon small" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                                    </svg>
                                </div>

                                <!-- Sensor Data Condition -->
                                <div *ngIf="condition.type === 'payload'" class="condition-grid">
                                    <div class="form-field">
                                        <label class="field-label small">Capteur</label>
                                        <select [(ngModel)]="condition.device"
                                            (ngModelChange)="onConditionDeviceChange(condition, 'sensors', $event)"
                                            class="select-input small">
                                            <option *ngFor="let deviceKey of getSensorDeviceNames()"
                                                [value]="deviceKey">
                                                {{ deviceTypes.sensors[deviceKey].DisplayName ||
                                                deviceTypes.sensors[deviceKey].device_name }}
                                            </option>
                                        </select>
                                    </div>
                                    <div class="form-field">
                                        <label class="field-label small">Propriété</label>
                                        <select [(ngModel)]="condition.key"
                                            (ngModelChange)="onConditionKeyChange(condition)"
                                            class="select-input small">
                                            <option
                                                *ngFor="let prop of getPropertiesForDevice('sensors', condition.device)"
                                                [value]="prop.key">
                                                {{ prop.key }} ({{ prop.type }})
                                            </option>
                                        </select>
                                    </div>
                                    <div class="form-field">
                                        <label class="field-label small">Opérateur</label>
                                        <select [(ngModel)]="condition.operator"
                                            (ngModelChange)="onConditionOperatorChange(condition)"
                                            class="select-input small">
                                            <option value="">Choisir...</option>
                                            <option *ngFor="let operator of getAvailableOperators(condition)"
                                                [value]="operator.value">
                                                {{ operator.label }}
                                            </option>
                                        </select>
                                        <div *ngIf="getSelectedProperty(condition)" class="type-indicator">
                                            Type: {{ getSelectedProperty(condition)?.type }}
                                        </div>
                                    </div>
                                    <div class="form-field">
                                        <label class="field-label small">Valeur</label>
                                        <ng-container *ngIf="getSelectedProperty(condition); let selectedProp">
                                            <div class="value-input-container">
                                                <!-- Number input for numeric types -->
                                                <input type="number" [(ngModel)]="condition.value"
                                                    (ngModelChange)="onConditionValueChange(condition, $event)"
                                                    [placeholder]="getValuePlaceholder(condition)"
                                                    class="number-input small"
                                                    [class.invalid]="condition.value && !isValidValue(condition, condition.value)"
                                                    autocomplete="off" spellcheck="false"
                                                    [id]="'input-' + groupIndex + '-' + conditionIndex"
                                                    *ngIf="getSelectedProperty(condition)?.type == 'number'">

                                                <!-- Enhanced Select for non-numeric types -->
                                                <select [(ngModel)]="condition.value"
                                                    (ngModelChange)="onConditionValueChange(condition, $event)"
                                                    [id]="'select-' + groupIndex + '-' + conditionIndex"
                                                    class="select-input small"
                                                    *ngIf="getSelectedProperty(condition)?.type != 'number' && hasSuggestedValues(condition)">
                                                    <option
                                                        *ngFor="let suggestion of getSuggestedValues(condition); trackBy: trackBySuggestion"
                                                        [value]="suggestion">
                                                        {{ suggestion }}
                                                    </option>
                                                </select>

                                                <!-- Fallback text input -->
                                                <input type="text" [(ngModel)]="condition.value"
                                                    (ngModelChange)="onConditionValueChange(condition, $event)"
                                                    [placeholder]="getValuePlaceholder(condition)"
                                                    class="text-input small"
                                                    [class.invalid]="condition.value && !isValidValue(condition, condition.value)"
                                                    autocomplete="off" spellcheck="false"
                                                    [id]="'input-text-' + groupIndex + '-' + conditionIndex"
                                                    *ngIf="getSelectedProperty(condition)?.type != 'number' && !hasSuggestedValues(condition)">

                                                <!-- Validation indicator -->
                                                <div *ngIf="condition.value && !isValidValue(condition, condition.value)"
                                                    class="validation-error">
                                                    <svg class="validation-icon" fill="currentColor"
                                                        viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd"
                                                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                                            clip-rule="evenodd"></path>
                                                    </svg>
                                                    Valeur invalide pour le type {{ selectedProp.type }}
                                                </div>

                                                <!-- Info indicator for suggested values -->
                                                <div *ngIf="hasSuggestedValues(condition)" class="validation-success">
                                                    <svg class="validation-icon" fill="currentColor"
                                                        viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd"
                                                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                                                            clip-rule="evenodd"></path>
                                                    </svg>
                                                    {{ getSuggestedValues(condition).length }} valeur(s)
                                                    pré-configurée(s)
                                                </div>
                                            </div>
                                        </ng-container>

                                        <!-- Disabled state when no property is selected -->
                                        <input *ngIf="!getSelectedProperty(condition)" type="text"
                                            [(ngModel)]="condition.value"
                                            (ngModelChange)="onConditionValueChange(condition, $event)"
                                            placeholder="Sélectionner d'abord une propriété"
                                            class="text-input small disabled" disabled>
                                    </div>
                                    <div class="condition-actions">
                                        <button (click)="removeCondition(groupIndex, conditionIndex)"
                                            class="delete-button">
                                            <svg class="button-icon" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                                </path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <!-- Time Condition -->
                                <div *ngIf="condition.type === 'time'" class="time-condition-grid">
                                    <div class="form-field">
                                        <label class="field-label small">Heure début</label>
                                        <input type="time" [(ngModel)]="condition.start_time"
                                            (ngModelChange)="onTimeConditionChange(condition)" class="time-input" />
                                    </div>
                                    <div class="form-field">
                                        <label class="field-label small">Heure fin</label>
                                        <input type="time" [(ngModel)]="condition.end_time"
                                            (ngModelChange)="onTimeConditionChange(condition)" class="time-input" />
                                    </div>
                                    <div class="condition-actions">
                                        <button (click)="removeCondition(groupIndex, conditionIndex)"
                                            class="delete-button">
                                            <svg class="button-icon" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                                </path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions with Drag & Drop -->
            <div class="section">
                <div class="section-header">
                    <h2 class="section-title">Actions</h2>
                    <div class="section-actions">
                        <button (click)="addAction()" class="add-button">
                            <svg class="button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Action
                        </button>
                        <button (click)="addLogAction()" class="add-button">
                            <svg class="button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Log
                        </button>
                    </div>
                </div>

                <!-- Draggable Actions -->
                <div cdkDropList [cdkDropListData]="rule.actions" (cdkDropListDropped)="onActionDrop($event)"
                    class="actions-list">

                    <div *ngFor="let action of rule.actions; let i = index; trackBy: trackByActionIndex" cdkDrag
                        [cdkDragData]="action" class="action-item">

                        <!-- Drag Handle for Action -->
                        <div cdkDragHandle class="drag-handle action-drag-handle"
                            title="Glisser pour réorganiser les actions">
                            <svg class="drag-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                            </svg>
                        </div>

                        <div *ngIf="action.type !== 'log'" class="action-grid">
                            <div class="form-field">
                                <label class="field-label small">Actionneur</label>
                                <select [(ngModel)]="action.topic"
                                    (ngModelChange)="onActionDeviceChange(action, $event)" class="select-input small">
                                    <option *ngFor="let deviceKey of getActuatorDeviceNames()"
                                        [value]="deviceTypes.actuators[deviceKey].topic">
                                        {{ deviceTypes.actuators[deviceKey].DisplayName ||
                                        deviceTypes.actuators[deviceKey].device_name }}
                                    </option>
                                </select>
                            </div>
                            <div class="form-field">
                                <label class="field-label small">Type d'action</label>
                                <select [(ngModel)]="action.type" (ngModelChange)="onActionTypeChange(action, $event)"
                                    class="select-input small">
                                    <ng-container
                                        *ngIf="getDeviceNameKeyByTopic(action.topic, 'actuators'); let deviceKey">
                                        <option *ngFor="let act of getActionsForDevice('actuators', deviceKey)"
                                            [value]="act.type">
                                            {{ capitalizeFirst(act.type) }}
                                        </option>
                                    </ng-container>
                                </select>
                            </div>
                            <div class="action-actions">
                                <button (click)="removeAction(i)" class="delete-button">
                                    <svg class="button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <div *ngIf="action.type && action.topic && action.type !== 'log'" class="payload-section">
                            <label class="field-label small">Valeur du Payload</label>
                            <ng-container *ngIf="getSelectedActionType(action); let selectedAction">
                                <select *ngIf="selectedAction.options && selectedAction.options.length > 0"
                                    [(ngModel)]="action.payload[action.type]"
                                    (ngModelChange)="onActionPayloadChange(action)" class="select-input">
                                    <option *ngFor="let opt of selectedAction.options" [value]="opt.value">
                                        {{ opt.display }}
                                    </option>
                                </select>
                                <input *ngIf="!selectedAction.options || selectedAction.options.length === 0"
                                    type="text" [(ngModel)]="action.payload[action.type]"
                                    (ngModelChange)="onActionPayloadChange(action)" placeholder="Entrer une valeur"
                                    class="text-input">
                            </ng-container>
                        </div>

                        <div *ngIf="action.type === 'log'" class="log-action-grid">
                            <div class="form-field">
                                <label class="field-label small">Message de log</label>
                                <input type="text" [(ngModel)]="action.message"
                                    (ngModelChange)="onActionMessageChange(action)" class="text-input"
                                    placeholder="Message à enregistrer" />
                            </div>
                            <div class="action-actions">
                                <button (click)="removeAction(i)" class="delete-button">
                                    <svg class="button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- JSON Preview -->
            <div class="json-section">
                <h3 class="json-title">Aperçu de la règle (JSON)</h3>
                <pre class="json-preview">{{ jsonPreview }}</pre>
            </div>
        </div>
    </div>

    <!-- Dialog Footer -->
    <div class="dialog-footer">
        <button mat-button (click)="onCancel()" class="cancel-button" [disabled]="isBusy">
            <mat-icon>cancel</mat-icon>
            Annuler
        </button>

        <button mat-button (click)="downloadJSON()" class="download-button" [disabled]="isBusy">
            <mat-icon>download</mat-icon>
            Télécharger JSON
        </button>

        <button mat-raised-button (click)="onSave()" class="save-button" [disabled]="!rule.rule_name.trim() || isBusy">
            <mat-icon>save</mat-icon>
            Enregistrer la Règle
        </button>
    </div>

    <!-- Busy Indicator -->
    <div *ngIf="isBusy" class="busy-overlay">
        <div class="busy-content">
            <div class="spinner"></div>
            <span class="busy-message">{{ statusMessage }}</span>
            <div class="progress-bar">
                <div class="progress-bar-fill" [style.width.%]="operationProgress"></div>
            </div>
        </div>
    </div>

    <ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
</div>