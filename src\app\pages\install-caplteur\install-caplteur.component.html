<div class="plan-editor">
  <!-- Header avec animations améliorées -->
  <header class="editor-header">
    <div class="header-left">
      <h1 class="editor-title">
        <mat-icon class="title-icon">sensors</mat-icon>
        Installation des Capteurs
      </h1>

      <!-- Sélecteurs de contexte -->
      <div class="context-selectors">
        <!-- Sélecteur Client -->
        <div class="selector-group" [class.has-selection]="selectedClient">
          <label class="selector-label">Client</label>
          <div class="dropdown" [class.open]="isClientDropdownOpen">
            <button class="dropdown-trigger" (click)="toggleClientDropdown()">
              <span class="selected-text">
                {{
                  selectedClient
                    ? selectedClient.Name
                    : "Sélectionner un client"
                }}
              </span>
              <mat-icon class="dropdown-icon">{{
                isClientDropdownOpen ? "expand_less" : "expand_more"
              }}</mat-icon>
            </button>
            <div class="dropdown-menu" *ngIf="isClientDropdownOpen">
              <div class="search-box">
                <input
                  type="text"
                  placeholder="Rechercher un client..."
                  (ngModelChange)="onSearchChangeClient()"
                  [(ngModel)]="clientSearchTerm"
                  class="search-input"
                  (click)="$event.stopPropagation()"
                />
              </div>
              <div class="dropdown-options">
                <div
                  *ngFor="let client of filteredClients"
                  class="dropdown-option"
                  (click)="selectClient(client)"
                >
                  <div class="option-main">
                    <span class="option-name">{{ client.Name }}</span>
                  </div>
                  <div class="option-meta">{{ client.BusinessSector }}</div>
                </div>
                <div *ngIf="filteredClients.length === 0" class="no-results">
                  Aucun client trouvé
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sélecteur Site -->
        <div class="selector-group" [class.has-selection]="selectedSite">
          <label class="selector-label">Site</label>
          <div
            class="dropdown"
            [class.open]="isSiteDropdownOpen"
            [class.disabled]="!selectedClient"
          >
            <button
              class="dropdown-trigger"
              (click)="toggleSiteDropdown()"
              [disabled]="!selectedClient"
            >
              <span class="selected-text">
                {{
                  selectedSite
                    ? selectedSite.Name
                    : selectedClient
                    ? "Sélectionner un site"
                    : "Choisir un site
                d'abord"
                }}
              </span>
              <mat-icon class="dropdown-icon">{{
                isSiteDropdownOpen ? "expand_less" : "expand_more"
              }}</mat-icon>
            </button>
            <div
              class="dropdown-menu"
              *ngIf="isSiteDropdownOpen && selectedClient"
            >
              <div class="search-box">
                <input
                  type="text"
                  placeholder="Rechercher un site..."
                  (ngModelChange)="onSearchChangeSites()"
                  [(ngModel)]="siteSearchTerm"
                  class="search-input"
                  (click)="$event.stopPropagation()"
                />
              </div>
              <div class="dropdown-options">
                <div
                  *ngFor="let site of filteredSites"
                  class="dropdown-option"
                  (click)="selectSite(site)"
                >
                  <div class="option-main">
                    <span class="option-name">{{ site.Name }}</span>
                  </div>
                  <div class="option-meta">{{ site.Address }}</div>
                </div>
                <div *ngIf="filteredSites.length === 0" class="no-results">
                  Aucun site trouvé
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sélecteur Local -->
        <div class="selector-group" [class.has-selection]="selectedLocal">
          <label class="selector-label">Local</label>
          <div
            class="dropdown"
            [class.open]="isLocalDropdownOpen"
            [class.disabled]="!selectedSite"
          >
            <button
              class="dropdown-trigger"
              (click)="toggleLocalDropdown()"
              [disabled]="!selectedSite"
            >
              <span class="selected-text">
                {{
                  selectedLocal
                    ? selectedLocal.Name
                    : selectedSite
                    ? "Sélectionner un local"
                    : "Choisir un site
                d'abord"
                }}
              </span>
              <mat-icon class="dropdown-icon">{{
                isLocalDropdownOpen ? "expand_less" : "expand_more"
              }}</mat-icon>
            </button>
            <div
              class="dropdown-menu"
              *ngIf="isLocalDropdownOpen && selectedSite"
            >
              <div class="search-box">
                <input
                  type="text"
                  placeholder="Rechercher un local..."
                  (ngModelChange)="onSearchChangeLocals()"
                  [(ngModel)]="localSearchTerm"
                  class="search-input"
                  (click)="$event.stopPropagation()"
                />
              </div>
              <div class="dropdown-options">
                <div
                  *ngFor="let local of filteredLocals"
                  class="dropdown-option"
                  (click)="selectLocal(local)"
                >
                  <div class="option-main">
                    <span class="option-name">{{ local.Name }}</span>
                    <span class="option-floor">{{ local.Floor }}</span>
                  </div>
                  <div class="option-meta">
                    <span class="option-type">{{ local.TypeLocal }}</span>
                  </div>
                </div>
                <div *ngIf="filteredLocals.length === 0" class="no-results">
                  Aucun local trouvé
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sélecteur Controleur -->
        <div class="selector-group" [class.has-selection]="selectedControler">
          <label class="selector-label">Contrôleur</label>
          <div
            class="dropdown"
            [class.open]="isControlerDropdownOpen"
            [class.disabled]="!selectedLocal"
          >
            <button
              class="dropdown-trigger"
              (click)="toggleControlerDropdown()"
              [disabled]="!selectedLocal"
            >
              <span class="selected-text">
                {{
                  selectedControler
                    ? selectedControler.ControllerModel
                    : selectedLocal
                    ? "Sélectionner un contrôleur"
                    : "Choisir un local d'abord"
                }}
              </span>
              <mat-icon class="dropdown-icon">{{
                isControlerDropdownOpen ? "expand_less" : "expand_more"
              }}</mat-icon>
            </button>
            <div
              class="dropdown-menu"
              *ngIf="isControlerDropdownOpen && selectedLocal"
            >
              <div class="search-box">
                <input
                  type="text"
                  placeholder="Rechercher un contrôleur..."
                  (ngModelChange)="onSearchChangeController()"
                  [(ngModel)]="ControlerSearchTerm"
                  class="search-input"
                  (click)="$event.stopPropagation()"
                />
              </div>
              <div class="dropdown-options">
                <div
                  *ngFor="let Controler of filteredControler"
                  class="dropdown-option"
                  (click)="selectControler(Controler)"
                >
                  <div class="option-main">
                    <span class="option-name">{{
                      Controler.ControllerModel
                    }}</span>
                  </div>
                  <div class="option-meta">
                    <span class="option-area">{{
                      Controler.ControllerName
                    }}</span>
                  </div>
                </div>
                <div *ngIf="Controlers.length === 0" class="no-results">
                  Aucun contrôleur trouvé
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Section des actions avec animations d'appairage -->
    <div class="header-actions">
      <div class="buttons-container">
        <button
          class="export-btn primary"
          (click)="showConfirmationDialog()"
          [disabled]="!instaledCapteurs.length"
        >
          <mat-icon>save</mat-icon>
          Enregistrer
        </button>
        <div class="pairing-container">
          <!-- Bouton d'appairage amélioré -->
          <button
            class="pairing-btn"
            [class.active]="pairingMode"
            [class.animate-pulse]="pairingMode"
            (click)="togglePermitJoin()"
            [disabled]="!selectedControler || !selectedLocal"
          >
            <div class="btn-content">
              <mat-icon class="btn-icon">{{
                pairingMode ? "stop" : "sync"
              }}</mat-icon>
              <span class="btn-text">{{
                pairingMode ? "Arrêter l'appairage" : "Démarrer l'appairage"
              }}</span>
            </div>
            <div class="pairing-waves" *ngIf="pairingMode">
              <div class="wave wave-1"></div>
              <div class="wave wave-2"></div>
              <div class="wave wave-3"></div>
            </div>
          </button>

          <!-- Animation d'appairage-->
          <div
            class="pairing-status"
            *ngIf="
              pairingAnimation.isActive && pairingTimeLeft > 0 && !peredCapteurs
            "
          >
            <div class="status-content">
              <div class="progress-container">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    [style.width.%]="pairingAnimation.progress"
                  ></div>
                </div>
                <span class="progress-text">{{
                  pairingAnimation.currentStep
                }}</span>
              </div>
            </div>
          </div>
          <div class="devices-found" *ngIf="pairingAnimation.foundDevices > 0">
            <mat-icon>devices</mat-icon>
            <span
              >{{ pairingAnimation.foundDevices }} capteur(s) détecté(s)</span
            >
          </div>
          <!-- Compteur de temps restant -->
          <div
            class="time-remaining"
            *ngIf="pairingMode && pairingTimeLeft > 0"
          >
            <div class="countdown-circle">
              <svg class="countdown-svg" viewBox="0 0 36 36">
                <path
                  class="countdown-bg"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
                <path
                  class="countdown-progress"
                  [style.stroke-dasharray]="
                    (pairingTimeLeft / 240) * 100 + ', 100'
                  "
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
              </svg>
              <div class="countdown-text">{{ getFormattedTimeLeft() }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>

  <div class="editor-body">
    <!-- Palette d'outils améliorée -->
    <aside class="tools-palette">
      <!-- Section Nouveaux capteurs avec animations -->
      <!-- Update the "Nouveaux capteurs détectés" section -->
      <!-- Update the "Nouveaux capteurs détectés" section -->
      <div class="palette-section">
        <h3 class="section-title">
          <mat-icon>add_circle</mat-icon>
          Nouveaux capteurs détectés
          <div class="new-device-flash" *ngIf="pairingMode"></div>
        </h3>

        <!-- Animation container when pairing -->
        <div
          class="pairing-animation-container"
          *ngIf="pairingMode && !availablePeredCapteurs2.length"
        >
          <!-- Hub central -->
          <div class="pairing-hub">
            <mat-icon>hub</mat-icon>
            <!-- Ondes radio -->
            <div class="pairing-waves">
              <div class="radio-wave"></div>
              <div class="radio-wave"></div>
              <div class="radio-wave"></div>
            </div>
          </div>
          <!-- Particules flottantes -->
          <div class="pairing-particles">
            <div
              class="particle"
              *ngFor="let particle of particles"
              [style.left.px]="particle.x"
              [style.animation-delay.s]="particle.delay"
            ></div>
          </div>
        </div>

        <div class="capteurs-container">
          <div class="tool-group" cdkDropList>
            <!-- Use availablePeredCapteurs2 instead of peredCapteurs2 -->
            <div
              class="capteur-item"
              *ngFor="let capteur of availablePeredCapteurs2"
              cdkDrag
              [cdkDragData]="capteur"
              (cdkDragStarted)="onCapteurDragStart($event, capteur)"
              (cdkDragEnded)="onCapteurDragEnd($event, capteur)"
              [class.dragging]="isDraggingCapteur"
              [class.selected]="selectedCapteur?.Id === capteur.Id"
            >
              <div class="capteur-card">
                <div class="capteur-header">
                  <div class="capteur-icon-container">
                    <mat-icon
                      [style.color]="
                        getIconColorForCapteur(
                          capteur.TypeCapteur?.DisplayName || ''
                        )
                      "
                      class="capteur-icon"
                    >
                      {{
                        getIconForCapteur(
                          capteur.TypeCapteur?.DisplayName || ""
                        )
                      }}
                    </mat-icon>

                    <!-- Badge de statut avec animation -->
                    <div
                      class="status-badge"
                      [class.online]="getCapteurStatus(capteur) === 'online'"
                      [class.warning]="getCapteurStatus(capteur) === 'warning'"
                      [class.offline]="getCapteurStatus(capteur) === 'offline'"
                    >
                      <div class="status-dot"></div>
                    </div>
                  </div>

                  <div class="capteur-actions">
                    <button
                      class="action-btn info"
                      (click)="showCapteurDetailsAtPosition(capteur, 300, 200)"
                      title="Voir détails"
                    >
                      <mat-icon>info</mat-icon>
                    </button>
                  </div>
                </div>

                <div class="capteur-info">
                  <h4 class="capteur-name">{{ capteur.FriendlyName }}</h4>
                  <p class="capteur-type">
                    {{ capteur.TypeCapteur?.DisplayName || "Type inconnu" }}
                  </p>
                  <p class="capteur-model">{{ capteur.Model }}</p>
                  <div class="capteur-meta">
                    <span class="last-seen">
                      <mat-icon>schedule</mat-icon>
                      {{ capteur.LastSeen | date : "short" }}
                    </span>
                  </div>
                </div>

                <!-- Indicateur de drag -->
                <div class="drag-indicator">
                  <mat-icon>drag_indicator</mat-icon>
                  <span>Glisser vers le plan</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty state templates -->
        <ng-template #noNewCapteurs>
          <div class="empty-state" *ngIf="!pairingMode">
            <mat-icon>device_hub</mat-icon>
            <p>Démarrez l'appairage pour détecter de nouveaux capteurs</p>
          </div>
          <div class="searching-state" *ngIf="pairingMode">
            <div class="searching-animation">
              <mat-icon class="searching-icon">search</mat-icon>
              <div class="searching-waves">
                <div class="search-wave"></div>
                <div class="search-wave"></div>
                <div class="search-wave"></div>
              </div>
            </div>
            <p>Recherche de nouveaux capteurs...</p>
          </div>
        </ng-template>
      </div>

      <!-- Update the "Capteurs disponibles" section -->
      <div class="palette-section">
        <h3 class="section-title">
          <mat-icon>sensors</mat-icon>
          Capteurs disponibles
          <span class="capteur-count" *ngIf="availableCapteurs.length > 0"
            >({{ availableCapteurs.length }})</span
          >
        </h3>

        <div class="capteurs-container">
          <div class="tool-group" cdkDropList>
            <!-- Use availableCapteurs instead of Capteurs -->
            <div
              class="capteur-item"
              *ngFor="let capteur of availableCapteurs"
              cdkDrag
              [cdkDragData]="capteur"
              (cdkDragStarted)="onCapteurDragStart($event, capteur)"
              (cdkDragEnded)="onCapteurDragEnd($event, capteur)"
              [class.dragging]="isDraggingCapteur"
              [class.selected]="selectedCapteur?.Id === capteur.Id"
            >
              <div class="capteur-card">
                <div class="capteur-header">
                  <div class="capteur-icon-container">
                    <mat-icon
                      [style.color]="
                        getIconColorForCapteur(
                          capteur.TypeCapteur?.DisplayName || ''
                        )
                      "
                      class="capteur-icon"
                    >
                      {{
                        getIconForCapteur(
                          capteur.TypeCapteur?.DisplayName || ""
                        )
                      }}
                    </mat-icon>

                    <!-- Badge de statut avec animation -->
                    <div
                      class="status-badge"
                      [class.online]="getCapteurStatus(capteur) === 'online'"
                      [class.warning]="getCapteurStatus(capteur) === 'warning'"
                      [class.offline]="getCapteurStatus(capteur) === 'offline'"
                    >
                      <div class="status-dot"></div>
                    </div>
                  </div>

                  <div class="capteur-actions">
                    <button
                      class="action-btn info"
                      (click)="showCapteurDetailsAtPosition(capteur, 300, 200)"
                      title="Voir détails"
                    >
                      <mat-icon>info</mat-icon>
                    </button>
                  </div>
                </div>

                <div class="capteur-info">
                  <h4 class="capteur-name">{{ capteur.FriendlyName }}</h4>
                  <p class="capteur-type">
                    {{ capteur.TypeCapteur?.DisplayName || "Type inconnu" }}
                  </p>
                  <p class="capteur-model">{{ capteur.Model }}</p>
                  <div class="capteur-meta">
                    <span class="last-seen">
                      <mat-icon>schedule</mat-icon>
                      {{ capteur.LastSeen | date : "short" }}
                    </span>
                  </div>
                </div>

                <!-- Indicateur de drag -->
                <div class="drag-indicator">
                  <mat-icon>drag_indicator</mat-icon>
                  <span>Glisser vers le plan</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Show empty state when no available capteurs -->
        <div
          class="empty-state"
          *ngIf="availableCapteurs.length === 0 && Capteurs.length > 0"
        >
          <mat-icon>check_circle</mat-icon>
          <p>Tous les capteurs sont déjà installés sur le plan</p>
        </div>
      </div>
    </aside>

    <!-- Zone de canvas avec drop zone -->
    <main class="canvas-area">
      <div class="canvas-header">
        <div class="canvas-info">
          <span class="canvas-title">Plan 2D - Installation des capteurs</span>
          <div class="plan-info" *ngIf="selectedLocal">
            <span class="plan-details">{{ selectedLocal.Name }}</span>
            <span class="plan-meta"
              >{{ selectedClient?.Name }} - {{ selectedSite?.Name }}</span
            >
          </div>
        </div>
      </div>

      <div
        class="canvas-wrapper"
        cdkDropList
        [cdkDropListData]="canvasDropData"
        (cdkDropListDropped)="onCanvasDropped($event)"
        [class.drag-over]="isDraggingCapteur"
      >
        <!-- Indicateur de zone de drop -->
        <div class="drop-zone-indicator" *ngIf="isDraggingCapteur">
          <div class="drop-zone-content">
            <mat-icon>touch_app</mat-icon>
            <span>Déposez le capteur ici pour l'installer sur le plan</span>
          </div>
        </div>

        <canvas id="canvas" width="900" height="400"></canvas>

        <!-- Overlay d'information -->
        <div class="canvas-overlay" *ngIf="!selectedLocal">
          <div class="overlay-content">
            <mat-icon>info</mat-icon>
            <h3>Comment installer des capteurs</h3>
            <ol>
              <li>Sélectionnez un contrôleur</li>
              <li>Démarrez l'appairage pour détecter les capteurs</li>
              <li>Glissez-déposez les capteurs sur le plan</li>
              <li>Enregistrer</li>
            </ol>
          </div>
        </div>
      </div>
    </main>
    <!-- Section Capteurs installés -->
    <div class="palette-section-right" *ngIf="instaledCapteurs.length > 0">
      <h3 class="section-title">
        <mat-icon>check_circle</mat-icon>
        Capteurs installés ({{ instaledCapteurs.length }})
      </h3>

      <div class="installed-capteurs">
        <div class="installed-item" *ngFor="let capteur of instaledCapteurs">
          <div class="installed-info">
            <mat-icon
              [style.color]="
                getIconColorForCapteur(capteur.TypeCapteur?.DisplayName || '')
              "
            >
              {{ getIconForCapteur(capteur.TypeCapteur?.DisplayName || "") }}
            </mat-icon>
            <span class="name">{{ capteur.FriendlyName }}</span>
          </div>
          <div class="installed-actions">
            <button
              class="action-btn info"
              (click)="showCapteurDetailsAtPosition(capteur, 1400, 200)"
              title="Voir détails"
            >
              <mat-icon>info</mat-icon>
            </button>
            <!-- <button class="action-btn highlight" (click)="highlightCapteur(capteur)" title="Localiser">
                <mat-icon>my_location</mat-icon>
              </button>-->
            <button
              class="action-btn remove"
              (click)="removeCapteurFromCanvas(capteur)"
              title="Retirer"
            >
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Popup de détails du capteur -->
  <div
    class="capteur-details-popup"
    *ngIf="showCapteurDetails && selectedCapteurForDetails"
    [style.left.px]="capteurDetailsPosition.x"
    [style.top.px]="capteurDetailsPosition.y"
    [@slideIn]
  >
    <div class="popup-header">
      <div class="capteur-title">
        <mat-icon
          [style.color]="
            getIconColorForCapteur(
              selectedCapteurForDetails.TypeCapteur?.DisplayName || ''
            )
          "
        >
          {{
            getIconForCapteur(
              selectedCapteurForDetails.TypeCapteur?.DisplayName || ""
            )
          }}
        </mat-icon>
        <h3>{{ selectedCapteurForDetails.FriendlyName }}</h3>
      </div>
      <button class="close-btn" (click)="hideCapteurDetails()">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div class="popup-content">
      <div class="detail-section">
        <h4>Informations générales</h4>
        <div class="detail-grid">
          <div class="detail-item">
            <span class="label">Type :</span>
            <span class="value">{{
              selectedCapteurForDetails.TypeCapteur?.DisplayName || "Inconnu"
            }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Modèle :</span>
            <span class="value">{{ selectedCapteurForDetails.Model }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Fabricant :</span>
            <span class="value">{{
              selectedCapteurForDetails.Manufacturer
            }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Protocole :</span>
            <span class="value">{{ selectedCapteurForDetails.Protocol }}</span>
          </div>
        </div>
      </div>

      <div class="detail-section">
        <h4>État et connectivité</h4>
        <div class="detail-grid">
          <div class="detail-item">
            <span class="label">Statut :</span>
            <span
              class="value status"
              [class]="getCapteurStatus(selectedCapteurForDetails)"
            >
              <mat-icon>{{
                getCapteurStatus(selectedCapteurForDetails) === "online"
                  ? "wifi"
                  : "wifi_off"
              }}</mat-icon>
              {{
                getCapteurStatus(selectedCapteurForDetails) === "online"
                  ? "En ligne"
                  : "Hors ligne"
              }}
            </span>
          </div>
          <div class="detail-item">
            <span class="label">Dernière activité :</span>
            <span class="value">{{
              selectedCapteurForDetails.LastSeen | date : "medium"
            }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Adresse IEEE :</span>
            <span class="value mono">{{
              selectedCapteurForDetails.IeeeAddress
            }}</span>
          </div>
        </div>
      </div>

      <div
        class="detail-section"
        *ngIf="hasSensorReadings(selectedCapteurForDetails!)"
      >
        <h4>Dernières mesures</h4>
        <div class="sensor-readings">
          <div
            class="reading-item"
            *ngFor="
              let reading of getSafeSensorReadings(selectedCapteurForDetails!)
            "
          >
            <mat-icon>timeline</mat-icon>
            <span class="reading-value"
              >{{ reading.Value }} {{ reading.Unit }}</span
            >
            <span class="reading-time">{{
              reading.Timestamp | date : "short"
            }}</span>
          </div>
        </div>
      </div>

      <div class="detail-actions">
        <button
          class="action-btn primary"
          (click)="highlightCapteur(selectedCapteurForDetails)"
        >
          <mat-icon>my_location</mat-icon>
          Localiser
        </button>
        <button
          class="action-btn secondary"
          (click)="removeCapteurFromCanvas(selectedCapteurForDetails)"
        >
          <mat-icon>delete</mat-icon>
          Retirer
        </button>
      </div>
    </div>
  </div>

  <!-- Notifications de succès -->
  <div class="notifications-container">
    <!-- Les notifications seront créées dynamiquement via JavaScript -->
  </div>

  <!-- Overlay pour fermer les popups -->
  <div
    class="overlay-backdrop"
    *ngIf="showCapteurDetails"
    (click)="hideCapteurDetails()"
  ></div>
</div>

<!-- Popup de résultat d'installation - À ajouter à la fin du template HTML -->

<!-- Overlay backdrop -->
<div class="installation-overlay" *ngIf="showInstallationResult"></div>

<!-- Popup de résultat d'installation -->
<div
  class="installation-result-popup"
  *ngIf="showInstallationResult && installationResult"
  [@slideIn]
>
  <!-- Header avec statut -->
  <div
    class="popup-header"
    [class.success]="installationResult.success"
    [class.error]="!installationResult.success"
  >
    <div class="status-indicator">
      <mat-icon
        [style.color]="getInstallationStatusColor()"
        class="status-icon"
      >
        {{ getInstallationStatusIcon() }}
      </mat-icon>
      <div class="status-text">
        <h2>{{ getInstallationStatusText() }}</h2>
        <p class="timestamp">
          {{ installationResult.timestamp | date : "medium" }}
        </p>
      </div>
    </div>

    <button class="close-btn" (click)="closeInstallationResult()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Contenu principal -->
  <div class="popup-content">
    <!-- Résumé des statistiques -->
    <div class="installation-summary">
      <div class="summary-cards">
        <div class="summary-card total">
          <div class="card-icon">
            <mat-icon>devices</mat-icon>
          </div>
          <div class="card-content">
            <span class="card-number">{{
              installationResult.totalCapteurs
            }}</span>
            <span class="card-label">Capteurs total</span>
          </div>
        </div>

        <div
          class="summary-card success"
          *ngIf="installationResult.successfulInstalls > 0"
        >
          <div class="card-icon">
            <mat-icon>check_circle</mat-icon>
          </div>
          <div class="card-content">
            <span class="card-number">{{
              installationResult.successfulInstalls
            }}</span>
            <span class="card-label">Installés</span>
          </div>
        </div>

        <div
          class="summary-card error"
          *ngIf="installationResult.failedInstalls > 0"
        >
          <div class="card-icon">
            <mat-icon>error</mat-icon>
          </div>
          <div class="card-content">
            <span class="card-number">{{
              installationResult.failedInstalls
            }}</span>
            <span class="card-label">Échecs</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Barre de progression -->
    <div class="progress-section">
      <div class="progress-label">
        <span>Progression de l'installation</span>
        <span class="progress-percentage">
          {{
            installationResult.success
              ? 100
              : ((installationResult.successfulInstalls /
                  installationResult.totalCapteurs) *
                  100 | number : "1.0-0")
          }}%
        </span>
      </div>
      <div class="progress-bar-container">
        <div class="progress-bar">
          <div
            class="progress-fill"
            [class.success]="installationResult.success"
            [class.error]="!installationResult.success"
            [style.width.%]="
              installationResult.success
                ? 100
                : (installationResult.successfulInstalls /
                    installationResult.totalCapteurs) *
                  100
            "
          ></div>
        </div>
      </div>
    </div>

    <!-- Détails de l'installation -->
    <div class="installation-details">
      <h3>Détails de l'installation</h3>

      <!-- Informations du contexte -->
      <div class="context-info">
        <div class="info-item">
          <mat-icon>business</mat-icon>
          <span class="label">Client :</span>
          <span class="value">{{ selectedClient?.Name || "Non défini" }}</span>
        </div>
        <div class="info-item">
          <mat-icon>location_on</mat-icon>
          <span class="label">Site :</span>
          <span class="value">{{ selectedSite?.Name || "Non défini" }}</span>
        </div>
        <div class="info-item">
          <mat-icon>room</mat-icon>
          <span class="label">Local :</span>
          <span class="value">{{ selectedLocal?.Name || "Non défini" }}</span>
        </div>
        <div class="info-item">
          <mat-icon>hub</mat-icon>
          <span class="label">Contrôleur :</span>
          <span class="value">{{ selectedControler.Model || 'Non défini' }}</span>
        </div>
      </div>

      <!-- Liste des capteurs installés -->
      <div
        class="capteurs-list"
        *ngIf="installationResult.transactions.length > 0"
      >
        <h4>Capteurs installés</h4>
        <div class="capteur-items">
          <div
            class="capteur-item-result"
            *ngFor="
              let transaction of installationResult.transactions;
              let i = index
            "
          >
            <div class="capteur-info-confirmation">
              <div class="capteur-icon-small">
                <mat-icon [style.color]="getIconColorForCapteur(instaledCapteurs[i].TypeCapteur?.DisplayName || '')">
                  {{ getIconForCapteur(instaledCapteurs[i].TypeCapteur?.DisplayName || '') }}
                </mat-icon>
              </div>
              <div class="capteur-details">
                <span class="capteur-name">{{ instaledCapteurs[i] .FriendlyName || 'Capteur ' + (i + 1) }}</span>
                <span class="capteur-type">{{ instaledCapteurs[i].TypeCapteur?.DisplayName || 'Type inconnu' }}</span>
              </div>
            </div>

            <div class="installation-status">
              <mat-icon
                class="status-icon"
                [class.success]="installationResult.success"
                [class.error]="!installationResult.success"
              >
                {{ installationResult.success ? "check_circle" : "error" }}
              </mat-icon>
              <span class="status-text">
                {{ installationResult.success ? "Installé" : "Échec" }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Erreurs détaillées (si échec) -->
      <div
        class="error-details"
        *ngIf="
          !installationResult.success && installationResult.errors.length > 0
        "
      >
        <h4>Détails des erreurs</h4>
        <div class="error-list">
          <div
            class="error-item"
            *ngFor="let error of installationResult.errors"
          >
            <mat-icon>warning</mat-icon>
            <div class="error-content">
              <span class="error-title">Erreur de communication</span>
              <span class="error-message">{{
                error.message || error.error?.message || "Erreur inconnue"
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Message de succès détaillé -->
      <div class="success-message" *ngIf="installationResult.success">
        <div class="success-content">
          <mat-icon>celebration</mat-icon>
          <div class="message-text">
            <h4>Installation terminée avec succès !</h4>
            <p>
              Tous les capteurs ont été correctement installés et configurés.
              Les données sont maintenant enregistrées dans le système.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Actions de la popup -->
  <div class="popup-actions">
    <!-- Actions en cas de succès -->
    <div class="actions-success" *ngIf="installationResult.success">
      <button class="action-btn secondary" (click)="exportToJson()">
        <mat-icon>download</mat-icon>
        Exporter le plan
      </button>
      <button class="export-btn primary" (click)="closeInstallationResult()">
        <mat-icon>check</mat-icon>
        Terminer
      </button>
    </div>

    <!-- Actions en cas d'erreur -->
    <div class="actions-error" *ngIf="!installationResult.success">
      <button class="action-btn secondary" (click)="closeInstallationResult()">
        <mat-icon>close</mat-icon>
        Fermer
      </button>
      <button class="action-btn warning" (click)="retryInstallation()">
        <mat-icon>refresh</mat-icon>
        Réessayer
      </button>
      <button class="action-btn danger" (click)="resetInstallation()">
        <mat-icon>restart_alt</mat-icon>
        Recommencer
      </button>
    </div>
  </div>
</div>

<!-- Popup de confirmation d'installation - À ajouter AVANT la popup de résultats -->

<!-- Overlay backdrop pour confirmation -->
<div
  class="confirmation-overlay"
  *ngIf="showConfirmationPopup"
  (click)="cancelInstallation()"
></div>

<!-- Popup de confirmation -->
<div class="confirmation-popup" *ngIf="showConfirmationPopup" [@slideIn]>
  <!-- Header -->
  <div class="confirmation-header">
    <div class="header-icon">
      <mat-icon class="warning-icon">warning</mat-icon>
    </div>
    <div class="header-content">
      <h2>Confirmer l'installation</h2>
      <p>
        Vous êtes sur le point d'installer
        {{ instaledCapteurs.length }} capteur{{
          instaledCapteurs.length > 1 ? "s" : ""
        }}
        dans le système.
      </p>
    </div>
  </div>

  <!-- Contenu de confirmation -->
  <div class="confirmation-content">
    <!-- Résumé de l'installation -->
    <div class="installation-preview">
      <h3>Résumé de l'installation</h3>

      <!-- Informations contextuelles -->
      <div class="context-preview">
        <div class="context-item">
          <mat-icon>business</mat-icon>
          <div class="context-details">
            <span class="context-label">Client</span>
            <span class="context-value">{{
              selectedClient?.Name || "Non défini"
            }}</span>
          </div>
        </div>

        <div class="context-item">
          <mat-icon>location_on</mat-icon>
          <div class="context-details">
            <span class="context-label">Site</span>
            <span class="context-value">{{
              selectedSite?.Name || "Non défini"
            }}</span>
          </div>
        </div>

        <div class="context-item">
          <mat-icon>room</mat-icon>
          <div class="context-details">
            <span class="context-label">Local</span>
            <span class="context-value">{{
              selectedLocal?.Name || "Non défini"
            }}</span>
          </div>
        </div>

        <div class="context-item">
          <mat-icon>hub</mat-icon>
          <div class="context-details">
            <span class="context-label">Contrôleur</span>
            <span class="context-value">{{ selectedControler.Model || 'Non défini' }}</span>
          </div>
        </div>
      </div>

      <!-- Statistiques de l'installation -->
      <div class="installation-stats">
        <div class="stat-card">
          <div class="stat-icon">
            <mat-icon>devices</mat-icon>
          </div>
          <div class="stat-content">
            <span class="stat-number">{{ instaledCapteurs.length }}</span>
            <span class="stat-label"
              >Capteur{{ instaledCapteurs.length > 1 ? "s" : "" }} à
              installer</span
            >
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <mat-icon>schedule</mat-icon>
          </div>
          <div class="stat-content">
            <span class="stat-number">{{
              getEstimatedInstallationTime()
            }}</span>
            <span class="stat-label">Temps estimé</span>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <mat-icon>category</mat-icon>
          </div>
          <div class="stat-content">
            <span class="stat-number">{{
              getCapteurTypesSummary().length
            }}</span>
            <span class="stat-label"
              >Type{{
                getCapteurTypesSummary().length > 1 ? "s" : ""
              }}
              différent{{
                getCapteurTypesSummary().length > 1 ? "s" : ""
              }}</span
            >
          </div>
        </div>
      </div>

      <!-- Liste des types de capteurs -->
      <div
        class="capteur-types-summary"
        *ngIf="getCapteurTypesSummary().length > 0"
      >
        <h4>Types de capteurs</h4>
        <div class="type-tags">
          <span class="type-tag" *ngFor="let type of getCapteurTypesSummary()">
            <mat-icon>{{ getIconForCapteur(type) }}</mat-icon>
            {{ type }}
          </span>
        </div>
      </div>

      <!-- Liste détaillée des capteurs -->
      <div class="capteurs-preview">
        <h4>Capteurs à installer</h4>
        <div class="capteur-list">
          <div
            class="capteur-preview-item"
            *ngFor="let capteur of instaledCapteurs; let i = index"
          >
            <div class="capteur-preview-icon">
              <mat-icon
                [style.color]="
                  getIconColorForCapteur(capteur.TypeCapteur?.DisplayName || '')
                "
              >
                {{ getIconForCapteur(capteur.TypeCapteur?.DisplayName || "") }}
              </mat-icon>
            </div>
            <div class="capteur-preview-info">
              <span class="capteur-preview-name">{{
                capteur.FriendlyName
              }}</span>
              <span class="capteur-preview-type">{{
                capteur.TypeCapteur?.DisplayName || "Type inconnu"
              }}</span>
            </div>
            <div class="capteur-preview-status">
              <span class="status-ready">Prêt</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Avertissements et informations importantes -->
    <div class="installation-warnings">
      <div class="warning-item">
        <mat-icon>info</mat-icon>
        <span
          >Cette action va créer {{ instaledCapteurs.length }} transaction{{
            instaledCapteurs.length > 1 ? "s" : ""
          }}
          dans le système.</span
        >
      </div>
      <div class="warning-item">
        <mat-icon>schedule</mat-icon>
        <span
          >L'installation peut prendre quelques minutes selon le nombre de
          capteurs.</span
        >
      </div>
      <div class="warning-item important">
        <mat-icon>warning</mat-icon>
        <span
          >Assurez-vous que tous les capteurs sont correctement positionnés sur
          le plan avant de continuer.</span
        >
      </div>
    </div>
  </div>

  <!-- Actions de confirmation -->
  <div class="confirmation-actions">
    <button class="confirmation-btn cancel" (click)="cancelInstallation()">
      <mat-icon>close</mat-icon>
      Annuler
    </button>

    <button
      class="export-btn primary"
      (click)="confirmInstallation()"
      [disabled]="!canInstall()"
    >
      <mat-icon>check</mat-icon>
      Confirmer l'installation
    </button>
  </div>
</div>

<!-- Popup de chargement pendant l'installation -->
<div class="processing-overlay" *ngIf="isProcessingInstallation">
  <div class="processing-popup">
    <div class="processing-content">
      <div class="processing-animation">
        <div class="spinner-container">
          <div class="installation-spinner"></div>
          <mat-icon class="spinner-icon">settings</mat-icon>
        </div>
      </div>
      <h3>Installation en cours...</h3>
      <p>Veuillez patienter pendant que nous installons vos capteurs.</p>
      <div class="processing-progress">
        <div class="progress-step active">
          <mat-icon>check</mat-icon>
          <span>Validation des données</span>
        </div>
        <div class="progress-step active">
          <mat-icon>sync</mat-icon>
          <span>Création des transactions</span>
        </div>
        <div class="progress-step">
          <mat-icon>cloud_upload</mat-icon>
          <span>Sauvegarde dans le système</span>
        </div>
        <div class="progress-step">
          <mat-icon>done_all</mat-icon>
          <span>Finalisation</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modification du bouton "Enregistrer" dans le header pour appeler la nouvelle fonction -->
<!-- Remplacez l'ancien bouton par : -->
<!-- 
<button class="export-btn primary" (click)="showConfirmationDialog()">
  <mat-icon>save</mat-icon>
  Enregistrer
</button>
-->
