.organisation-details-container {
  padding: 40px;
}

/* Breadcrumb navigation */
.breadcrumb-nav {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.back-button {
  background: #f5f5f5;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Add these new styles for the current images display */
.current-images-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  margin-top: 8px;
}

.current-image-item {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.current-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image-btn {
  top: 105px;
  border: none;
  max-width: 45px;
  max-height: 45px;
  cursor: pointer;
  transition: all 0.2s ease;
  color:red;
  padding: 0;
}
.equipment-container {
  margin: 15px 0;
}
.equipment-title {
  text-align: center;
  font-size: 1rem;
  font-weight: 600;
  color: #555;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
}
.stats-grid {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  max-width: 760px;
  flex-wrap: wrap;
  gap: 8px;
}

.stat-card {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 12px 5px;
  text-align: center;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  border: 1px solid #eee;
}

.stat-card-site-status{
  width: 50% !important;
  flex: 0 0 calc(50% - 8px) !important; /* Two per row with gap compensation */
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 4px;
}
.stat-type {
  font-size: 0.75rem;
  color: #666;
  text-transform: uppercase;
  font-weight: 500;
}
/* Couleurs spécifiques */
.stat-card:nth-child(1) .stat-number { color: var(--primary); } /* Actifs */
.stat-card:nth-child(2) .stat-number { color: #ef4444; } /* Inactifs */
.stat-card:nth-child(3) .stat-number { color: #2561A9; } /* Total */

.remove-image-btn i {
  font-size: 16px;
  color: #ef5350;
}

.file-names {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.file-name {
  background: #f0f8f0;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  color: #2e7d32;
}

.back-button:hover {
  background: #e0e0e0;
  transform: translateX(-3px);
}

.back-button .material-icons {
  color: #555;
  font-size: 24px;
}

.breadcrumb-text {
  font-size: 16px;
  color: #666;
}

/* Loading spinner */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  animation: fadeIn 0.5s ease-out;
}

.spinner {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid rgba(76, 175, 80, 0.1);
  border-top-color: var(--primary);
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-container p {
  color: #718096;
  font-size: 16px;
  font-style: italic;
}

/* Organization info section */
.info-section {
  display: flex;
  margin-bottom: 30px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 25px;
  animation: fadeIn 0.5s ease-out;
}

.org-images-container {
  flex: 0 0 200px;
  margin-right: 30px;
}

.logo-container {
  width: 250px;
  height: 250px;
  border-radius: 18px;
  margin-right: 22px;
  background: white;
  /* display: flex;
  align-items: center;
  justify-content: center; */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* Style for the home button in logo container */
.logo-container button {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 28px;
  height: 28px;
  min-width: 28px;
  padding: 0;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 2;
  backdrop-filter: blur(4px);
}

.logo-container button:hover {
  background: white;
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Style the material icon inside the button */
.logo-container button mat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
  color: #4a5567;
}

.org-logo {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: white;
  border-radius: 10px;
  transition: transform 0.3s ease;
  z-index: 1;
}

/* Optional: Add hover effect */
/* .logo-container:hover .org-logo {
  transform: scale(1.05);
} */

.no-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f0f8f0;
}

.no-logo .material-icons {
  font-size: 80px;
  color: #c5c5c5;
}

.org-info-container {
  flex: 1;
}

.org-name {
  margin: 0 0 20px;
  font-size: 28px;
  font-weight: 700;
  color: var(--primary);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 24px;
  align-items: start;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-height: 0;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  height: 24px;
}

.info-label .material-icons {
  font-size: 20px;
  color: var(--primary);
  line-height: 1;
}

.info-value {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  margin-top: 4px;
}

/* Site form */
.site-form-container {
  margin-bottom: 30px;
  animation: slideIn 0.5s ease-out;
}

.form-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 25px;
}

.form-title {
  margin: 0 0 20px;
  font-size: 22px;
  font-weight: 600;
  color: #2E7D32;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-size: 14px;
  color: #4a5568;
  margin-bottom: 6px;
  font-weight: 500;
}

.form-group button {
  width: 160px;
}

.form-control {
  height: 40px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  outline: none;
}

.form-error {
  font-size: 12px;
  color: #e53e3e;
  margin-top: 4px;
}

.file-input-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 6px;
}

.file-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background-color: #edf2f7;
  color: #4a5568;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
}

.file-button:hover {
  background-color: #e2e8f0;
}

.file-info {
  font-size: 14px;
  color: #718096;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.tabs-container {
  display: flex;
  border-bottom: 2px solid #ccc;
  margin-bottom: 1rem;
}

.tab-button {
  padding: 0.5rem 1rem;
  cursor: pointer;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  /* font-weight: bold; */
  transition: all 0.3s;
}

.tab-button.active {
  border-bottom: 2px solid var(--primary);
  color: var(--primary);
}


.actions {
  display: flex;
  justify-content: center; /* 👈 centers horizontally */
  align-items: center;     /* optional: centers vertically */
  gap: 15px;
  margin: 20px 0;
}

.create-button {
  background: linear-gradient(45deg, var(--primary), #81C784);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.create-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.5);
  background: linear-gradient(45deg, #81C784, var(--primary));
}

.btn-cancel {
  padding: 10px 20px;
  background: transparent;
  border: 1px solid #cbd5e0;
  color: #718096;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.btn-cancel:hover {
  background-color: #f7fafc;
}

.btn-submit {
  padding: 10px 20px;
  background: linear-gradient(45deg, var(--primary), #81C784);
  color: white;
  border: none;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.btn-submit:hover {
  background: linear-gradient(45deg, #81C784, var(--primary));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.btn-submit:disabled {
  background: #cbd5e0;
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
}

/* Sites section */
.sites-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 25px;
  animation: fadeIn 0.5s ease-out;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.view-controls {
  display: flex;
  gap: 8px;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 4px;
}

.view-toggle-btn {
  background: none;
  border: none;
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: all 0.2s ease;
}

.view-toggle-btn.active {
  background: #ffffff;
  color: #2E7D32;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.view-toggle-btn:hover:not(.active) {
  background: rgba(255, 255, 255, 0.5);
}

.add-site-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #2E7D32;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.add-site-btn:hover {
  background: #1b5e20;
}

.table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 30px;
}

.section-title {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  color: #2d3748;
}

.sites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 25px;
}

.no-sites-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  text-align: center;
  color: #718096;
}

.no-sites-message .material-icons {
  font-size: 48px;
  color: #cbd5e0;
  margin-bottom: 15px;
}

.no-sites-message p {
  margin: 0 0 20px;
  font-size: 16px;
}

/* Error container */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  text-align: center;
  color: #718096;
  animation: fadeIn 0.5s ease-out;
}

.error-icon {
  font-size: 48px;
  color: #e53e3e;
  margin-bottom: 15px;
}

.error-container p {
  margin: 0 0 20px;
  font-size: 16px;
}

.btn-primary {
  padding: 10px 20px;
  background: linear-gradient(45deg, var(--primary), #81C784);
  color: white;
  border: none;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary:hover {
  background: linear-gradient(45deg, #81C784, var(--primary));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

/* Pagination Controls */
.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  margin: 30px 0 10px 0;
  flex-wrap: wrap;
}

.pagination-controls button {
  min-width: 36px;
  height: 36px;
  padding: 0 10px;
  border: none;
  background: #f5f5f5;
  color: #333;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  box-shadow: 0 1px 3px rgba(44, 62, 80, 0.04);
  outline: none;
}

.pagination-controls button.active,
.pagination-controls button:focus {
  background: linear-gradient(45deg, var(--primary), #81C784);
  color: #fff;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.15);
}

.pagination-controls button:disabled {
  background: #e0e0e0;
  color: #bdbdbd;
  cursor: not-allowed;
  box-shadow: none;
}

.pagination-controls button:hover:not(:disabled):not(.active) {
  background: #e8f5e9;
  color: #388e3c;
}

/* Card Pagination Styles - Make it more prominent */
.card-pagination-controls {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  width: 100% !important;
  margin-top: 20px !important;
  margin-bottom: 20px !important;
  padding: 15px !important;
  background-color: #f9f9f9 !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e0e0e0 !important;
}

.pagination-info {
  font-size: 14px !important;
  color: #333 !important;
  font-weight: 500 !important;
}

.pagination-buttons {
  display: flex !important;
  gap: 10px !important;
}

.pagination-button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  border: 1px solid #ddd !important;
  background-color: white !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important;
}

.pagination-button:hover:not([disabled]) {
  background-color: #f5f5f5 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.pagination-button[disabled] {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

.pagination-button i {
  font-size: 24px !important;
  color: #2E7D32 !important;
}

/* Card Pagination Container */
.card-pagination-container {
  margin-top: 20px;
  margin-bottom: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Style the Material paginator to match your theme */
::ng-deep .card-pagination-container .mat-mdc-paginator {
  background-color: white;
}

::ng-deep .card-pagination-container .mat-mdc-paginator-container {
  padding: 8px 16px;
}

::ng-deep .card-pagination-container .mat-mdc-icon-button {
  color: #2E7D32;
}

::ng-deep .card-pagination-container .mat-mdc-select-value {
  color: #333;
}

::ng-deep .card-pagination-container .mat-mdc-paginator-range-label {
  color: #666;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .info-section {
    flex-direction: column;
  }

  .org-images-container {
    margin-right: 0;
    margin-bottom: 20px;
    align-self: center;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .sites-grid {
    grid-template-columns: 1fr;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .section-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .btn-add {
    width: 100%;
    justify-content: center;
  }

  .logo-container {
    width: 120px;
    height: 120px;
    color: var(--primary);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .view-controls {
    width: 100%;
    justify-content: center;
  }

  .add-site-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Notification Snackbar Styles */
/* Notification Snackbar Styles */
::ng-deep .success-snackbar {
  background: linear-gradient(135deg, var(--primary), #81C784) !important;
  color: white !important;
}

::ng-deep .success-snackbar .mat-mdc-snack-bar-label,
::ng-deep .success-snackbar .mat-mdc-button {
  color: white !important;
}

::ng-deep .error-snackbar {
  background: linear-gradient(135deg, #f44336, #ef5350) !important;
  color: white !important;
}

::ng-deep .error-snackbar .mat-mdc-snack-bar-label,
::ng-deep .error-snackbar .mat-mdc-button {
  color: white !important;
}

::ng-deep .info-snackbar {
  background: linear-gradient(135deg, #2196F3, #64B5F6) !important;
  color: white !important;
}

::ng-deep .info-snackbar .mat-mdc-snack-bar-label,
::ng-deep .info-snackbar .mat-mdc-button {
  color: white !important;
}

/* Enhanced snackbar styling */
::ng-deep .mat-mdc-snack-bar-container {
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  min-width: 300px !important;

  position: fixed !important;
  top: 100px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;

  animation: slideFromTop 0.3s ease-out !important;
}

::ng-deep .mat-mdc-snack-bar-label {
  font-weight: 500 !important;
  font-size: 14px !important;
}

::ng-deep .mat-mdc-snack-bar-action {
  font-weight: 600 !important;
}

/* Animation for snackbar */
@keyframes slideFromTop {
  from {
    transform: translate(-50%, -100%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0);
    opacity: 1;
  }
}
/* Version responsive */
@media (max-width: 768px) {
  .stats-grid {
    flex-direction: column;
    gap: 6px;
  }
  
  .stat-card {
    padding: 10px;
  }
}
/* Pagination container styles */
.card-pagination-container, 
.pagination-container {
  margin-top: 24px;
  margin-bottom: 24px;
}

.pagination-container {
  padding: 24px;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.card-pagination-container {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 16px 24px;
  border: 1px solid #f1f5f9;
}

/* Force override Material paginator background - Multiple selectors for specificity */
.pagination-container mat-paginator,
.card-pagination-container mat-paginator,
mat-paginator.mat-mdc-paginator,
.mat-mdc-paginator {
  background: transparent !important;
  background-color: transparent !important;
}

/* Target the specific Material classes with higher specificity */
.pagination-container .mat-mdc-paginator,
.card-pagination-container .mat-mdc-paginator {
  background: none !important;
  background-color: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Style the paginator wrapper div */
.pagination-container .mat-mdc-paginator > div,
.card-pagination-container .mat-mdc-paginator > div {
  background: transparent !important;
}

/* Navigation buttons styling */
.pagination-container .mat-mdc-icon-button,
.card-pagination-container .mat-mdc-icon-button {
  background: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 6px !important;
  margin: 0 4px !important;
  width: 36px !important;
  height: 36px !important;
  transition: all 0.2s ease !important;
}

.pagination-container .mat-mdc-icon-button:hover,
.card-pagination-container .mat-mdc-icon-button:hover {
  background: #e2e8f0 !important;
  border-color: #cbd5e1 !important;
  transform: translateY(-1px);
}

.pagination-container .mat-mdc-icon-button:disabled,
.card-pagination-container .mat-mdc-icon-button:disabled {
  background: #f1f5f9 !important;
  border-color: #e5e7eb !important;
  opacity: 0.5;
  transform: none;
}

/* Page size dropdown */
.pagination-container .mat-mdc-select,
.card-pagination-container .mat-mdc-select {
  background: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 6px !important;
  margin: 0 8px !important;
}

.pagination-container .mat-mdc-select-trigger,
.card-pagination-container .mat-mdc-select-trigger {
  background: transparent !important;
}

/* Range label text */
.pagination-container .mat-mdc-paginator-range-label,
.card-pagination-container .mat-mdc-paginator-range-label {
  color: #475569 !important;
  font-weight: 500 !important;
  margin: 0 16px !important;
}

/* Page size label */
.pagination-container .mat-mdc-paginator-page-size-label,
.card-pagination-container .mat-mdc-paginator-page-size-label {
  color: #64748b !important;
  font-weight: 400 !important;
}

/* Hide form field underlines */
.pagination-container .mat-mdc-form-field-subscript-wrapper,
.card-pagination-container .mat-mdc-form-field-subscript-wrapper {
  display: none !important;
}

/* Alternative: If the above doesn't work, use this global override */
mat-paginator {
  background: transparent !important;
}

mat-paginator .mat-mdc-paginator {
  background: transparent !important;
  background-color: transparent !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pagination-container {
    padding: 16px;
  }
  
  .card-pagination-container {
    padding: 12px 16px;
  }
  
  .pagination-container .mat-mdc-paginator,
  .card-pagination-container .mat-mdc-paginator {
    flex-wrap: wrap;
    gap: 8px;
  }
}
/* Popup Form Styles */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.popup-form {
  position: absolute;
  top: 80px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 990px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
  transform: translateX(12%);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.popup-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2E7D32;
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.close-btn:hover {
  background: #f0f0f0;
}

.close-btn mat-icon {
  color: #666;
  font-size: 24px;
}

.site-form {
  padding: 25px;
}

.site-form .form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.site-form .form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.site-form .form-group.full-width {
  grid-column: 1 / -1;
}

.site-form label {
  font-size: 14px;
  font-weight: 500;
  color: hsl(223, 78%, 2%);
}

.required {
  color: #e53e3e;
}

.site-form input,
.site-form select,
.site-form textarea {
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.site-form input:focus,
.site-form select:focus,
.site-form textarea:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  outline: none;
}

.error-message {
  font-size: 12px;
  color: #e53e3e;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.btn-cancel {
  padding: 10px 20px;
  background: transparent;
  border: 1px solid #cbd5e0;
  color: #718096;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.btn-cancel:hover {
  background-color: #f7fafc;
}

.btn-submit {
  padding: 10px 20px;
  background: linear-gradient(45deg, var(--primary), #81C784);
  color: white;
  border: none;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.btn-submit:hover {
  background: linear-gradient(45deg, #81C784, var(--primary));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.btn-submit:disabled {
  background: #cbd5e0;
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
}

/* Responsive popup */
@media (max-width: 768px) {
  .popup-form {
    width: 95%;
    max-height: 95vh;
  }

  .popup-header {
    padding: 15px 20px;
  }

  .site-form {
    padding: 20px;
  }

  .site-form .form-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-cancel,
  .btn-submit {
    width: 100%;
    justify-content: center;
  }
}
/* Add these styles to the existing CSS file */
.validation-errors {
  background-color: #fee2e2;
  border-left: 4px solid #ef4444;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.validation-errors-title {
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.validation-errors-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.validation-errors-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 14px;
  color: #b91c1c;
}

.validation-errors-list li:last-child {
  margin-bottom: 0;
}

.validation-errors-list li i {
  font-size: 16px;
}

/* Add to organisation-details.component.css */
.show-more-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: none;
  border: none;
  color: #2E7D32;
  cursor: pointer;
  font-size: 14px;
  margin: 10px 0;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.show-more-btn:hover {
  background-color: #f0f8f0;
}

.show-more-btn i {
  font-size: 18px;
  transition: transform 0.2s ease;
}

.show-more-btn.expanded i {
  transform: rotate(180deg);
}

.optional-fields {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  max-height: 0;
  opacity: 0;
  grid-column: 1 / -1;
  margin-top: 0;
}

.optional-fields.expanded {
  max-height: 1000px; /* Adjust based on your content */
  opacity: 1;
  margin-top: 20px;
}

.optional-fields.expanded {
  max-height: 1000px; /* Adjust based on your content */
}

.search-section {
  margin: 20px 0;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-container {
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 500px;
}

.search-input {
  flex: 1;
  height: 025px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.search-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  outline: none;
}

.search-input::placeholder {
  color: #a0aec0;
}

.search-button{
  height: 40px;
  padding: 0 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s ease;
  min-width: 100px;
}

.search-button {
  background: linear-gradient(45deg, var(--primary), #81C784);
  color: white;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.search-button:hover {
  background: linear-gradient(45deg, #81C784, var(--primary));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.clear-button {
  background: #f44336;
  color: white;
  box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
}

.clear-button:hover {
  background: #d32f2f;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
}

/* Add this to your CSS file */
.show-more-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  color: var(--primary);
  border: 1px solid var(--primary);
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 10px 0;
}

.show-more-buttons:hover {
  background-color: #f5f5f5;
}

.show-more-buttons i {
  font-size: 20px;
}

/* Image upload styles */
.image-preview {
  position: relative;
  margin-bottom: 15px;
  max-width: 200px;
}

.preview-image {
  max-width: 100%;
  max-height: 150px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}



.remove-image-btn:hover {
  background: #fee2e2;
}

.image-upload-input {
  display: none;
}

.image-upload-label {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f8fafc;
  border: 1px dashed #e2e8f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.image-upload-label:hover {
  background: #f0f4f8;
  border-color: #cbd5e1;
}

.image-upload-label mat-icon {
  color: #64748b;
}

.tab-content {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.4s cubic-bezier(0.4,0,0.2,1);
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
}
.tab-content.active {
  opacity: 1;
  pointer-events: auto;
  position: relative;
  z-index: 2;
}
.tab-content.inactive {
  z-index: 1;
}
.tab-content-wrapper {
  position: relative;
  min-height: 400px; /* or whatever fits your design */
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-placeholder,
.loading-placeholder {
  flex: 1 1 auto;
  height: 100%;
  min-height: 320px; /* fallback for small screens */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(30, 64, 175, 0.06);
  margin: 0 auto;
  padding: 32px 16px;
  text-align: center;
  animation: fadeIn 0.5s;
}

.tab-loading-spinner {
  width: 56px;
  height: 56px;
  border: 6px solid #e0e7ef;
  border-top: 6px solid var(--primary, #2E7D32);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 18px;
}

.tab-loading-message {
  color: #2E7D32;
  font-size: 1.1rem;
  font-weight: 500;
  text-align: center;
  margin-top: 0;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
