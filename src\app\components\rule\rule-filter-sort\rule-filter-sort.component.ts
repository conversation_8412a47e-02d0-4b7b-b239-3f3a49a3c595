// rule-filter-sort.component.ts
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';
import { WhereParams } from '@app/shared/models/rule/WhereParams';

interface FilterOption {
  column: string;
  label: string;
  type: 'text' | 'number' | 'date' | 'select';
  options?: { value: string; label: string }[];
}

interface SortOption {
  value: string;
  label: string;
}

@Component({
  selector: 'app-rule-filter-sort',
  templateUrl: './rule-filter-sort.component.html',
  styleUrls: ['./rule-filter-sort.component.css'],
  standalone: true,
  animations: [
    trigger('slideInOut', [
      transition(':enter', [
        style({
          height: '0px',
          opacity: 0,
          transform: 'translateY(-10px)',
          overflow: 'hidden',
        }),
        animate(
          '300ms ease-in-out',
          style({
            height: '*',
            opacity: 1,
            transform: 'translateY(0px)',
          })
        ),
      ]),
      transition(':leave', [
        style({
          height: '*',
          opacity: 1,
          transform: 'translateY(0px)',
          overflow: 'hidden',
        }),
        animate(
          '300ms ease-in-out',
          style({
            height: '0px',
            opacity: 0,
            transform: 'translateY(-10px)',
          })
        ),
      ]),
    ]),
  ],
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
  ],
})
export class RuleFilterSortComponent {
  @Input() isLoading: boolean | null = false;
  @Input() hasPendingOperations: boolean = false;
  @Input() showAdvancedFilters: boolean = false;
  @Input() currentSortColumn: string = 'Priority';
  @Input() currentSortDirection: string = 'ASC';
  @Input() appliedFilters: WhereParams[] = [];

  @Input() sortOptions: SortOption[] = [
    { value: 'Priority', label: 'Priorité' },
    { value: 'RuleCreatedAt', label: 'Date de création' },
    { value: 'RuleLastUpdatedAt', label: 'Dernière modification' },
    { value: 'Status', label: 'Statut' },
    { value: 'TotalApplications', label: "Nombre d'applications" },
    { value: 'LastTriggered', label: 'Dernier déclenchement' },
  ];

  @Input() filterOptions: FilterOption[] = [
    {
      column: 'Status',
      label: 'Statut',
      type: 'select',
      options: [
        { value: 'active', label: 'Actif' },
        { value: 'inactive', label: 'Inactif' },
      ],
    },
    { column: 'Priority', label: 'Priorité', type: 'number' },
    { column: 'RuleCreatedAt', label: 'Date de création', type: 'date' },
    {
      column: 'TotalApplications',
      label: "Nombre d'applications",
      type: 'number',
    },
    { column: 'RawData', label: 'Contenu de la règle', type: 'text' },
    { column: 'TagsString', label: 'Tags', type: 'text' },
  ];

  @Output() toggleAdvancedFilters = new EventEmitter<void>();
  @Output() updateSort = new EventEmitter<string>();
  @Output() removeFilter = new EventEmitter<string>();
  @Output() clearAllFilters = new EventEmitter<void>();
  @Output() applyPredefinedFilter = new EventEmitter<string>();
  @Output() resetToDefaults = new EventEmitter<void>();
  @Output() onFilterOperatorChange = new EventEmitter<{
    event: Event;
    column: string;
  }>();
  @Output() onFilterValueChange = new EventEmitter<{
    event: Event;
    column: string;
  }>();
  @Output() onFilterSelectChange = new EventEmitter<{
    event: Event;
    column: string;
  }>();

  /**
   * Gets sort icon for a column
   */
  getSortIcon(column: string): string {
    if (this.currentSortColumn !== column) {
      return 'unfold_more';
    }
    return this.currentSortDirection === 'ASC'
      ? 'keyboard_arrow_up'
      : 'keyboard_arrow_down';
  }

  /**
   * Checks if a column is currently being sorted
   */
  isSortedBy(column: string): boolean {
    return this.currentSortColumn === column;
  }

  /**
   * Gets display text for a filter
   */
  // getFilterDisplayText(filter: WhereParams): string {
  //   const option = this.filterOptions.find(
  //     (opt) => opt.column === filter.Column
  //   );
  //   const label = option?.label || filter.Column;

  //   let operatorText = '';
  //   switch (filter.Operand) {
  //     case 'eq':
  //       operatorText = '=';
  //       break;
  //     case 'neq':
  //       operatorText = '≠';
  //       break;
  //     case 'lt':
  //       operatorText = '<';
  //       break;
  //     case 'gt':
  //       operatorText = '>';
  //       break;
  //     case 'lte':
  //       operatorText = '≤';
  //       break;
  //     case 'gte':
  //       operatorText = '≥';
  //       break;
  //     case 'contains':
  //       operatorText = 'contient';
  //       break;
  //     case 'startswith':
  //       operatorText = 'commence par';
  //       break;
  //     case 'endswith':
  //       operatorText = 'finit par';
  //       break;
  //     default:
  //       operatorText = filter.Operand;
  //   }

  //   return `${label} ${operatorText} ${filter.Value}`;
  // }

  /**
   * Gets the current filter value for a column
   */
  getFilterValue(column: string): any {
    const filter = this.appliedFilters.find((f) => f.Column === column);
    return filter ? filter.Value : null;
  }

  /**
   * Gets the current filter operator for a column
   */
  // getFilterOperator(column: string): string {
  //   const filter = this.appliedFilters.find((f) => f.Column === column);
  //   return filter ? filter.Operand : 'eq';
  // }

  /**
   * Checks if a filter is applied for a column
   */
  hasFilter(column: string): boolean {
    return this.appliedFilters.some((f) => f.Column === column);
  }

  /**
   * Gets the total number of active filters
   */
  getActiveFiltersCount(): number {
    return this.appliedFilters.length;
  }

  /**
   * Checks if any advanced features are active
   */
  hasAdvancedFeaturesActive(): boolean {
    return (
      this.appliedFilters.length > 0 ||
      this.currentSortColumn !== 'Priority' ||
      this.currentSortDirection !== 'ASC'
    );
  }

  /**
   * Validates filter values before applying
   */
  validateFilterValue(column: string, value: any, operator: string): boolean {
    if (value === null || value === undefined || value === '') {
      return false;
    }

    // Additional validation based on column type
    const option = this.filterOptions.find((opt) => opt.column === column);
    if (!option) return true;

    switch (option.type) {
      case 'number':
        return !isNaN(Number(value));
      case 'date':
        return !isNaN(Date.parse(value));
      default:
        return true;
    }
  }

  /**
   * Handles filter operator change events
   */
  handleFilterOperatorChange(event: Event, column: string): void {
    this.onFilterOperatorChange.emit({ event, column });
  }

  /**
   * Handles filter value change events
   */
  handleFilterValueChange(event: Event, column: string): void {
    this.onFilterValueChange.emit({ event, column });
  }

  /**
   * Handles filter select change events
   */
  handleFilterSelectChange(event: Event, column: string): void {
    this.onFilterSelectChange.emit({ event, column });
  }

  /**
   * Handles sort button clicks
   */
  handleSortClick(column: string): void {
    this.updateSort.emit(column);
  }

  /**
   * Handles filter removal
   */
  handleRemoveFilter(column: string): void {
    this.removeFilter.emit(column);
  }

  /**
   * Handles predefined filter application
   */
  handlePredefinedFilter(filterType: string): void {
    this.applyPredefinedFilter.emit(filterType);
  }

  /**
   * Handles advanced filters toggle
   */
  handleToggleAdvancedFilters(): void {
    this.toggleAdvancedFilters.emit();
  }

  /**
   * Handles reset to defaults
   */
  handleResetToDefaults(): void {
    this.resetToDefaults.emit();
  }

  /**
   * Handles clear all filters
   */
  handleClearAllFilters(): void {
    this.clearAllFilters.emit();
  }
}
