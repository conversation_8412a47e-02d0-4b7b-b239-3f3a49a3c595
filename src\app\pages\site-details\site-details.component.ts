// src/app/pages/site-details/site-details.component.ts
import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { Site } from '../../core/models/site';
import {
  Device,
  DeviceType,
  DeviceStatus,
} from '../../core/models/device.model';
import { CustomMqttService } from '../../core/services/custom-mqtt.service';
import { DeviceManagerService } from '@app/core/services/device-manager.service';
import { Local } from '@app/core/models/local';

import { SiteApiService } from '@app/core/services/administrative/site.service';
import { LogsComponent } from './logs/logs.component';
import { LocalApiService } from '@app/core/services/administrative/local.service';
import { LocalService } from '@app/core/services/local.service';
import { LocalDetailsComponent } from '@app/components/local-details/local-details.component';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-site-details',
  standalone: true,
  imports: [
    CommonModule,
    LogsComponent,
    LocalDetailsComponent,
    MatIconModule,
    MatProgressSpinnerModule,
    MatButtonModule,
  ],
  templateUrl: './site-details.component.html',
  styleUrls: ['./site-details.component.css'],
})
export class SiteDetailsComponent implements OnInit, OnDestroy {


  site?: Site;
  displaySite: any = {
    id: 0,
    name: '',
    floorPlan: {
      id: '0',
      name: 'Plan architecture',
      width: 800,
      height: 600,
      imageUrl: '',
    },
    devices: [],
  };
  siteId?: number;
  localId?: number;
  localIdStr = '';
  siteError: string | null = null;
  readonly subscription = new Subscription();
  floorPlanImage?: string;
  pairingMode = false;

  local?: Local;
  devices: any[] = [];
  readonly subs = new Subscription();

  staticDevices: Device[] = [
    {
      id: 'd1',
      name: 'Lumière du salon',
      type: DeviceType.LAMP,
      status: DeviceStatus.RUNNING,
      position: { x: 100, y: 100 },
      isZigbee: true,
    },
    {
      id: 'd2',
      name: 'Lumière de la chambre',
      type: DeviceType.LAMP,
      status: DeviceStatus.RUNNING,
      position: { x: 400, y: 150 },
      isZigbee: true,
    },
    {
      id: 'd3',
      name: 'Lumière de la cuisine',
      type: DeviceType.LAMP,
      status: DeviceStatus.PAUSED,
      position: { x: 200, y: 300 },
      isZigbee: false,
    },
    {
      id: 'd4',
      name: 'Climatisation de la chambre',
      type: DeviceType.CLIMATE,
      status: DeviceStatus.PAUSED,
      position: { x: 450, y: 200 },
      isZigbee: false,
    },
    {
      id: 'd5',
      name: 'Climatisation du salon',
      type: DeviceType.CLIMATE,
      status: DeviceStatus.RUNNING,
      position: { x: 300, y: 350 },
      isZigbee: true,
    },
    {
      id: 'd6',
      name: 'Contrôleur',
      type: DeviceType.CONTROLLER,
      status: DeviceStatus.RUNNING,
      position: { x: 50, y: 450 },
      isZigbee: false,
    },
  ];

  selectedLocal: Local | null = null;

  isDragging = false;
  selectedDeviceId: string | null = null;
  dragStartX = 0;
  dragStartY = 0;

  constructor(
    readonly route: ActivatedRoute,
    readonly siteService: SiteApiService,
    readonly mqttService: CustomMqttService,
    readonly localApiService: LocalApiService,
    readonly localService: LocalService,
    readonly deviceManager: DeviceManagerService,
    readonly router: Router,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.mqttService.connect();
    const localId = this.route.snapshot.paramMap.get('id');

    if (localId) {
      this.loadLocalDetails(localId);
      this.loadLocalImage(Number(localId));

      this.displaySite = {
        ...this.displaySite,
        devices: this.staticDevices,
      };
    }

    // this.subs.add(
    //   this.localApiService
    //     .gatePage({
    //       pagination: {
    //         currentPage: 0,
    //         pageSize: 100,
    //         isLast: true,
    //         isFirst: true,
    //         startIndex: 0,
    //         totalElement: 0,
    //       },
    //       sortPage: {
    //         Column: 'CreatedAt',
    //         Sort: 'desc',
    //       },
    //       FilterParams: [],
    //     })
    //     .subscribe({
    //       next: (res: any) => {
    //         const locals = res.Content || [];
    //         this.local = locals.find((l: any) => l.Id === localId);
    //         if (this.local) this.loadDevices(this.local);
    //       },
    //       error: (err: any) => {
    //         console.error('Failed to load locals:', err);
    //       },
    //     })
    // );
  }

  private loadLocalDetails(localId: string): void {
    // Start loader
    // this.ngxLoader.start();

    this.localApiService.getById(localId).subscribe({
      next: (local) => {
        this.selectedLocal = local;
        this.localIdStr = local.Id;
        if (local.IdSite) {
          this.loadSiteDetails(local.IdSite);
        }
        this.loadDevices(local);
        // Stop loader after everything is loaded
        // this.ngxLoader.stop();
      },
      error: (error) => {
        console.error('Error loading local details:', error);
        // Stop loader on error
        // this.ngxLoader.stop();
      }
    });
  }

  private loadSiteDetails(siteId: string): void {
    // this.ngxLoader.start();
    // this.siteService.getById(siteId).subscribe({
    //   next: (site) => {
    //     if (site) {
    //       this.site = site;
    //       // Update display site object
    //       this.displaySite = {
    //         id: Number(site.Id),
    //         name: site.Name,
    //         description: site.Description ?? undefined,
    //         floorPlan: {
    //           id: site.Id,
    //           name: 'Plan architecture',
    //           width: 800,
    //           height: 600,
    //           imageUrl: this.selectedLocal?.Architecture2DImage ?? '',
    //         },
    //         devices: this.staticDevices,
    //       };
    //     } else {
    //       // this.ngxLoader.stop();
    //       this.siteError = 'Site not found';
    //     }
    //   },
    //   error: (error) => {
    //     // this.ngxLoader.stop();
    //     console.error('Error loading site details:', error);
    //     this.siteError = 'Error loading site details';
    //   },
    // });
  }

  loadDevices(local: Local): void {
    this.subs.add(
      this.deviceManager.subscribeToLocal(local).subscribe((devices) => {
        this.devices = devices;
      })
    );
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
    this.subscription.unsubscribe();
    this.mqttService.disconnect();
  }

  private loadLocalImage(localId: number): void {
    // Handle null case
    if (localId === null) return;

    // this.localService.getLocalImageArchitecture(localId).subscribe({
    //   next: (blob) => {
    //     const reader = new FileReader();
    //     reader.onloadend = () => {
    //       this.floorPlanImage = reader.result as string;
    //       if (this.displaySite) {
    //         this.displaySite.floorPlan = {
    //           id: localId.toString(),
    //           name: 'Plan architecture',
    //           width: 800,
    //           height: 600,
    //           imageUrl: this.floorPlanImage,
    //         };
    //       }
    //     };
    //     reader.readAsDataURL(blob);
    //   },
    //   error: (error) => {
    //     console.error('Error loading local architecture image:', error);
    //   },
    // });
  }

  updateDevicePosition(
    deviceId: string,
    position: { x: number; y: number }
  ): void {
    const device = this.staticDevices.find((d) => d.id === deviceId);
    if (device) {
      device.position = position;
      console.log("Position de l'appareil mise à jour:", {
        deviceId,
        position,
      });
    }
  }

  updateDeviceStatus(deviceId: string, status: DeviceStatus): void {
    const device = this.staticDevices.find((d) => d.id === deviceId);
    if (device) {
      device.status = status;
      console.log("Statut de l'appareil mis à jour:", { deviceId, status });
    }
  }

  togglePermitJoin(deviceId: string): void {
    const device = this.staticDevices.find((d) => d.id === deviceId);
    if (device && device.type === DeviceType.CONTROLLER) {
      this.pairingMode = !this.pairingMode;
      const message = this.pairingMode
        ? '{"value": true, "time": 254}'
        : '{"value": false}'; // Current payload
      console.log(
        'Toggling Permit Join - New Pairing Mode:',
        this.pairingMode,
        'Message:',
        message
      );
      if (!this.pairingMode) {
        // Try a more explicit payload for deactivation
        const deactivationMessage = '{"value": false, "time": 0}';
        setTimeout(() => {
          this.mqttService.publish(
            'zigbee2mqtt/bridge/request/permit_join',
            deactivationMessage
          );
          console.log(
            'Deactivation message sent with delay:',
            deactivationMessage
          );
        }, 1000);
      } else {
        this.mqttService.publish(
          'zigbee2mqtt/bridge/request/permit_join',
          message
        );
        console.log('Activation message sent');
      }
      device.status = this.pairingMode
        ? DeviceStatus.PAIRING
        : DeviceStatus.RUNNING;
      console.log(
        `Appairage ${this.pairingMode ? 'activé' : 'désactivé'} pour ${
          device.name
        } via MQTT`
      );
    }
  }

  getLightDevices(): Device[] {
    return this.staticDevices.filter((d) => d.type === DeviceType.LAMP);
  }

  getClimateDevices(): Device[] {
    return this.staticDevices.filter((d) => d.type === DeviceType.CLIMATE);
  }

  getOtherDevices(): Device[] {
    return this.staticDevices.filter((d) => d.type === DeviceType.CONTROLLER);
  }

  goBack(): void {
    window.history.back();
  }

  private getRandomPosition(): { x: number; y: number } {

    const padding = 10;
    return {
      x: padding + Math.random() * (100 - 2 * padding),
      y: padding + Math.random() * (100 - 2 * padding),
    };
  }

  onImageLoad(event: Event): void {
    // Randomize the positions of devices
    this.staticDevices = this.staticDevices.map((device) => ({
      ...device,
      position: this.getRandomPosition(),
    }));
  }

  startDragging(event: MouseEvent | TouchEvent, device: Device): void {
    event.preventDefault();
    this.isDragging = true;
    this.selectedDeviceId = device.id;

    if (event instanceof MouseEvent) {
      this.dragStartX = event.clientX;
      this.dragStartY = event.clientY;
    } else {
      this.dragStartX = event.touches[0].clientX;
      this.dragStartY = event.touches[0].clientY;
    }

    document.addEventListener('mousemove', this.onDrag);
    document.addEventListener('touchmove', this.onDrag);
    document.addEventListener('mouseup', this.stopDragging);
    document.addEventListener('touchend', this.stopDragging);
  }

  onDrag = (event: MouseEvent | TouchEvent) => {
    if (!this.isDragging || !this.selectedDeviceId) return;

    const clientX =
      event instanceof MouseEvent ? event.clientX : event.touches[0].clientX;
    const clientY =
      event instanceof MouseEvent ? event.clientY : event.touches[0].clientY;

    const wrapper = document.querySelector(
      '.architecture-wrapper'
    ) as HTMLElement;
    if (!wrapper) return;

    const rect = wrapper.getBoundingClientRect();
    const x = ((clientX - rect.left) / rect.width) * 100;
    const y = ((clientY - rect.top) / rect.height) * 100;

    // Update device position
    const device = this.staticDevices.find(
      (d) => d.id === this.selectedDeviceId
    );
    if (device) {
      device.position = {
        x: Math.min(Math.max(x, 0), 100),
        y: Math.min(Math.max(y, 0), 100),
      };
    }
  };

  stopDragging = () => {
    if (this.isDragging && this.selectedDeviceId) {
      const device = this.staticDevices.find(
        (d) => d.id === this.selectedDeviceId
      );
      if (device) {
        this.updateDevicePosition(this.selectedDeviceId, device.position);
      }
    }

    this.isDragging = false;
    this.selectedDeviceId = null;
    document.removeEventListener('mousemove', this.onDrag);
    document.removeEventListener('touchmove', this.onDrag);
    document.removeEventListener('mouseup', this.stopDragging);
    document.removeEventListener('touchend', this.stopDragging);
  };
}
