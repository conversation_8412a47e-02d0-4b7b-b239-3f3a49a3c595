import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiService } from '../api.service';
import { Local } from '@app/core/models/local';
import { TypeLocal } from '@app/core/models/TypeLocal.1';
import { Observable } from 'rxjs';
import { environment } from '@app/environments/environment';
import { Lister, Page } from '@app/core/models/util/page';

@Injectable({ providedIn: 'root' })
export class LocalApiService extends ApiService<Local> {
  override baseUrl: string = environment.host.endsWith('/')
    ? environment.host + 'api/'
    : environment.host + '/api/';

  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('local');
  }

  uploadImage(localId: string, imageData: string): Observable<any> {
    const requestBody = {
      id: localId,
      imageData: imageData
    };

    return this.http.post(`${this.baseUrl}Images/upload/local`, requestBody, {
      headers: {
        'Content-Type': 'application/json-patch+json'
      }
    });
  }

  downloadImage(localId: string): Observable<any> {
    return this.http.post(`${this.baseUrl}Images/download/local`, null, {
      params: {
        Id: localId
      },
      headers: {
        'Content-Type': 'application/json-patch+json'
      }
    });
  }

  getArchitecture2DImage(localId: string): Observable<any> {
    return this.http.get(`${this.baseUrl}local/architecture/${localId}`, {});
  }

  paginateWithClientInfo(request: Lister): Observable<Page<Local>> {
    return this.http.post<Page<Local>>(
      `${this.baseUrl}local/paginate-with-client-info`,
      request,
      {
        headers: {
          'Content-Type': 'application/json-patch+json'
        }
      }
    );
  }
  
}

@Injectable({ providedIn: 'root' })
export class TypeLocalApiService extends ApiService<TypeLocal> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('type-local');
  }
  
}