

export interface ZWaveNode {
  nodeId: number;
  name?: string;
  location?: string;
  status: NodeStatus;
  deviceClass?: {
    basic: { key: number; label: string };
    generic: { key: number; label: string };
    specific: { key: number; label: string };
  };
  manufacturerSpecific?: {
    manufacturerId: number;
    productId: number;
    productType: number;
  };
  endpoints: ZWaveEndpoint[];
  values: ZWaveValue[];
  isListening: boolean;
  isFrequentListening: boolean | string;
  isRouting: boolean;
  maxDataRate: number;
  supportedDataRates: number[];
  protocolVersion: number;
  zwavePlusVersion?: number;
  zwavePlusNodeType?: number;
  zwavePlusRoleType?: number;
  nodeType?: number;
  roleType?: number;
  deviceConfig?: {
    filename: string;
    manufacturer: string;
    description: string;
    label: string;
  };
  ready: boolean;
  interviewStage: string;
  statistics?: NodeStatistics;
  firmwareVersion?: string;
  sdkVersion?: string;
  lastSeen?: Date;
}

export interface ZWaveEndpoint {
  nodeId: number;
  index: number;
  deviceClass?: {
    basic: { key: number; label: string };
    generic: { key: number; label: string };
    specific: { key: number; label: string };
  };
  commandClasses: CommandClassInfo[];
}

export interface CommandClassInfo {
  id: number;
  name: string;
  version: number;
  isSecure: boolean;
  isSupported: boolean;
  isControlled: boolean;
}

export interface ZWaveValue {
  commandClass: number;
  commandClassName: string;
  property: string | number;
  propertyKey?: string | number;
  propertyName?: string;
  propertyKeyName?: string;
  endpoint: number;
  nodeId: number;
  metadata: ValueMetadata;
  value?: any;
  newValue?: any;
  prevValue?: any;
  stateless?: boolean;
}

export interface ValueMetadata {
  type: 'number' | 'boolean' | 'string' | 'any' | 'buffer';
  readable: boolean;
  writeable: boolean;
  description?: string;
  label?: string;
  min?: number;
  max?: number;
  steps?: number;
  unit?: string;
  list?: boolean;
  states?: Record<number, string>;
  default?: any;
  ccSpecific?: Record<string, any>;
  valueChangeOptions?: string[];
}

export interface NodeStatistics {
  commandsTX: number;
  commandsRX: number;
  commandsDroppedRX: number;
  commandsDroppedTX: number;
  timeoutResponse: number;
  rtt?: number;
  rssi?: number;
  lwr?: { rssi: number[]; repeaters: number[] };
  nlwr?: { rssi: number[]; repeaters: number[] };
}

export enum NodeStatus {
  Unknown = 'unknown',
  Asleep = 'asleep',
  Awake = 'awake',
  Dead = 'dead',
  Alive = 'alive',
}

export interface ControllerInfo {
  homeId: number;
  ownNodeId: number;
  libVersion: string;
  type: number;
  manufacturerId: number;
  productType: number;
  productId: number;
  supportedFunctionTypes: number[];
  supportsTimers: boolean;
  isSecondary: boolean;
  isUsingHomeIdFromOtherNetwork: boolean;
  isSISPresent: boolean;
  wasRealPrimary: boolean;
  isStaticUpdateController: boolean;
  isSlave: boolean;
  serialApiVersion: string;
  supportsStartListening: boolean;
  nodes: number[];
  supportedDataRates: number[];
  supportsLongRange: boolean;
  maxLongRangePowerlevel?: number;
  longRangeChannel?: number;
  supportsLongRangeAutoChannelSelection?: boolean;
}

export interface InclusionState {
  strategy: InclusionStrategy;
  active: boolean;
  userCallbacks: UserCallbacks;
}

export enum InclusionStrategy {
  Default = 0,
  Security_S2 = 1,
  Security_S0 = 2,
  Insecure = 3,
}

export interface UserCallbacks {
  grantSecurityClasses?: (requested: SecurityClassGrant) => Promise<SecurityClassGrant>;
  validateDSKAndEnterPIN?: (dsk: string) => Promise<string>;
  abort?: () => void;
}

export interface SecurityClassGrant {
  securityClasses: number[];
  clientSideAuth: boolean;
}

// WebSocket message types
export interface WSMessage {
  messageId: string;
  command: string;
  [key: string]: any;
}

export interface WSResponse {
  type: 'result' | 'event';
  success?: boolean;
  messageId?: string;
  result?: any;
  errorCode?: string;
  zwaveErrorCode?: number;
  zwaveErrorMessage?: string;
  event?: WSEvent;
}

export interface WSEvent {
  source: 'controller' | 'driver' | 'node';
  event: string;
  [key: string]: any;
}

// Command interfaces for common operations
export interface SetValueCommand {
  nodeId: number;
  commandClass: number;
  property: string | number;
  propertyKey?: string | number;
  endpoint?: number;
  value: any;
  options?: {
    transitionDuration?: string;
    volume?: number;
  };
}

export interface NodeCommand {
  nodeId: number;
  endpoint?: number;
}

export interface ControllerCommand {
  messageId: string;
  command: string;
  [key: string]: any;
}

// UI-specific interfaces
export interface DeviceListItem {
  nodeId: number;
  name: string;
  status: NodeStatus;
  deviceType: string;
  location?: string;
  lastSeen?: Date;
  batteryLevel?: number;
  signalStrength?: number;
  capabilities: DeviceCapabilities;
  values: DeviceValue[];
}

export interface DeviceCapabilities {
  canSwitch: boolean;
  canDim: boolean;
  canSense: boolean;
  hasMetering: boolean;
  hasBattery: boolean;
  supportsSecurity: boolean;
  isBeaming: boolean;
  isListening: boolean;
}

export interface DeviceValue {
  id: string;
  label: string;
  value: any;
  unit?: string;
  type: 'switch' | 'dimmer' | 'sensor' | 'meter' | 'config' | 'info';
  writable: boolean;
  metadata: ValueMetadata;
}

// Error handling
export interface ZWaveError {
  code: string;
  message: string;
  zwaveErrorCode?: number;
  nodeId?: number;
  context?: any;
}

export enum ZWaveErrorCode {
  NodeNotFound = 'node_not_found',
  ValueNotFound = 'value_not_found',
  NotSupported = 'not_supported',
  InvalidValue = 'invalid_value',
  Timeout = 'timeout',
  NetworkError = 'network_error',
  PermissionDenied = 'permission_denied',
  InvalidState = 'invalid_state',
}

// Event types for subscription
export enum ZWaveEventType {
  NodeAdded = 'node added',
  NodeRemoved = 'node removed',
  NodeReady = 'node ready',
  NodeValueAdded = 'node value added',
  NodeValueUpdated = 'node value updated',
  NodeValueRemoved = 'node value removed',
  NodeStatusChanged = 'node status changed',
  InclusionStarted = 'inclusion started',
  InclusionStopped = 'inclusion stopped',
  InclusionFailed = 'inclusion failed',
  ExclusionStarted = 'exclusion started',
  ExclusionStopped = 'exclusion stopped',
  ExclusionFailed = 'exclusion failed',
  ControllerStatisticsUpdated = 'controller statistics updated',
  DriverReady = 'driver ready',
  DriverFailed = 'driver failed',
}

// Configuration options
export interface ZWaveServiceConfig {
  serverUrl: string;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  commandTimeout: number;
  enableLogging: boolean;
  logLevel: 'error' | 'warn' | 'info' | 'debug';
}

export const DEFAULT_CONFIG: ZWaveServiceConfig = {
  serverUrl: 'ws://146.59.198.243:8083/mqtt',
  reconnectInterval: 5000,
  maxReconnectAttempts: 10,
  commandTimeout: 30000,
  enableLogging: true,
  logLevel: 'info',
};