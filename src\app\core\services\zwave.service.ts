// import { Injectable, OnDestroy } from '@angular/core';
// import { BehaviorSubject, Observable, Subject, timer, throwError, of } from 'rxjs';
// import { webSocket, WebSocketSubject } from 'rxjs/webSocket';
// import { 
//   retryWhen, 
//   delay, 
//   take, 
//   takeUntil, 
//   filter, 
//   map, 
//   catchError, 
//   timeout,
//   share,
//   distinctUntilChanged
// } from 'rxjs/operators';
// import {
//   ZWaveNode,
//   ControllerInfo,
//   WSMessage,
//   WSResponse,
//   WSEvent,
//   SetValueCommand,
//   NodeCommand,
//   ZWaveEventType,
//   ZWaveError,
//   ZWaveErrorCode,
//   ZWaveServiceConfig,
//   DEFAULT_CONFIG,
//   DeviceListItem,
//   NodeStatus,
//   InclusionState,
//   SecurityClassGrant,
//   DeviceCapabilities,
//   DeviceValue
// } from '../../shared/models/zwave/ZwaveModels';

// @Injectable({
//   providedIn: 'root'
// })
// export class ZWaveService implements OnDestroy {
//   private destroy$ = new Subject<void>();
//   private socket$: WebSocketSubject<any> | null = null;
//   private messageId = 0;
//   private pendingCommands = new Map<string, { resolve: Function; reject: Function; timeout: any }>();
//   private reconnectAttempts = 0;
  
//   // Configuration
//   private config: ZWaveServiceConfig = { ...DEFAULT_CONFIG };
  
//   // Connection state
//   private connectionState$ = new BehaviorSubject<'disconnected' | 'connecting' | 'connected'>('disconnected');
//   private lastError$ = new BehaviorSubject<ZWaveError | null>(null);
  
//   // Z-Wave state
//   private controller$ = new BehaviorSubject<ControllerInfo | null>(null);
//   private nodes$ = new BehaviorSubject<Map<number, ZWaveNode>>(new Map());
//   private inclusionState$ = new BehaviorSubject<InclusionState | null>(null);
//   private driverReady$ = new BehaviorSubject<boolean>(false);
  
//   // Events stream
//   private events$ = new Subject<WSEvent>();

//   constructor() {
//     // Start connection on service initialization
//     this.connect();
//   }

//   ngOnDestroy(): void {
//     this.destroy$.next();
//     this.destroy$.complete();
//     this.disconnect();
//   }

//   // Configuration
//   updateConfig(config: Partial<ZWaveServiceConfig>): void {
//     this.config = { ...this.config, ...config };
//     if (this.socket$) {
//       this.disconnect();
//       this.connect();
//     }
//   }

//   // Connection management
//   connect(): void {
//     if (this.connectionState$.value === 'connected' || this.connectionState$.value === 'connecting') {
//       return;
//     }

//     this.connectionState$.next('connecting');
//     this.log('info', 'Connecting to Z-Wave server...', { url: this.config.serverUrl });

//     this.socket$ = webSocket({
//       url: this.config.serverUrl,
//       openObserver: {
//         next: () => {
//           this.connectionState$.next('connected');
//           this.reconnectAttempts = 0;
//           this.lastError$.next(null);
//           this.log('info', 'Connected to Z-Wave server');
//           this.initializeConnection();
//         }
//       },
//       closeObserver: {
//         next: () => {
//           this.connectionState$.next('disconnected');
//           this.log('warn', 'Disconnected from Z-Wave server');
//           this.handleReconnect();
//         }
//       }
//     });

//     this.socket$
//       .pipe(
//         takeUntil(this.destroy$),
//         retryWhen(errors => errors.pipe(
//           delay(this.config.reconnectInterval),
//           take(this.config.maxReconnectAttempts)
//         )),
//         catchError(error => {
//           this.handleError('Connection error', error);
//           return of(null);
//         })
//       )
//       .subscribe({
//         next: (message: WSResponse) => {
//           if (message) {
//             this.handleMessage(message);
//           }
//         },
//         error: (error) => {
//           this.handleError('WebSocket error', error);
//         }
//       });
//   }

//   disconnect(): void {
//     if (this.socket$) {
//       this.socket$.complete();
//       this.socket$ = null;
//     }
//     this.connectionState$.next('disconnected');
//     this.clearPendingCommands();
//   }

//   private handleReconnect(): void {
//     if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
//       this.reconnectAttempts++;
//       this.log('info', `Attempting to reconnect (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})...`);
      
//       timer(this.config.reconnectInterval)
//         .pipe(takeUntil(this.destroy$))
//         .subscribe(() => this.connect());
//     } else {
//       this.handleError('Max reconnection attempts reached', new Error('Could not reconnect to Z-Wave server'));
//     }
//   }

//   private initializeConnection(): void {
//     // Start listening to events and get initial state
//     this.sendCommand('start_listening', {})
//       .then(() => this.sendCommand('driver.get_config', {}))
//       .then(() => this.getControllerInfo())
//       .then(() => this.loadNodes())
//       .catch(error => this.handleError('Failed to initialize connection', error));
//   }

//   // WebSocket message handling
//   private handleMessage(message: WSResponse): void {
//     if (message.type === 'result') {
//       this.handleCommandResult(message);
//     } else if (message.type === 'event') {
//       this.handleEvent(message.event!);
//     }
//   }

//   private handleCommandResult(message: WSResponse): void {
//     const pending = this.pendingCommands.get(message.messageId!);
//     if (pending) {
//       clearTimeout(pending.timeout);
//       this.pendingCommands.delete(message.messageId!);
      
//       if (message.success) {
//         pending.resolve(message.result);
//       } else {
//         const error: ZWaveError = {
//           code: message.errorCode || ZWaveErrorCode.NetworkError,
//           message: message.zwaveErrorMessage || 'Command failed',
//           zwaveErrorCode: message.zwaveErrorCode
//         };
//         pending.reject(error);
//       }
//     }
//   }

//   private handleEvent(event: WSEvent): void {
//     this.events$.next(event);
    
//     switch (event.event) {
//       case 'driver ready':
//         this.driverReady$.next(true);
//         this.loadNodes();
//         break;
        
//       case 'node added':
//         this.handleNodeAdded(event);
//         break;
        
//       case 'node removed':
//         this.handleNodeRemoved(event);
//         break;
        
//       case 'node value updated':
//         this.handleNodeValueUpdated(event);
//         break;
        
//       case 'inclusion started':
//         this.updateInclusionState({ 
//           strategy: this.safeGetEventProperty(event, 'strategy', 0) || 0, 
//           active: true, 
//           userCallbacks: {} 
//         });
//         break;
        
//       case 'inclusion stopped':
//         this.updateInclusionState(null);
//         break;
        
//       case 'exclusion started':
//         this.log('info', 'Exclusion started');
//         break;
        
//       case 'exclusion stopped':
//         this.log('info', 'Exclusion stopped');
//         break;
        
//       case 'grant security classes':
//         this.handleSecurityClassGrant(event);
//         break;
//     }
//   }

//   private handleNodeAdded(event: WSEvent): void {
//     const node = this.safeGetEventProperty(event, 'node');
//     const nodeId = this.safeGetNestedProperty(node, 'nodeId');
//     if (typeof nodeId === 'number') {
//       this.log('info', `Node ${nodeId} added to network`);
//       this.loadNodeState(nodeId);
//     }
//   }

//   private handleNodeRemoved(event: WSEvent): void {
//     const node = this.safeGetEventProperty(event, 'node');
//     const nodeId = this.safeGetNestedProperty(node, 'nodeId');
//     if (typeof nodeId === 'number') {
//       const nodes = this.nodes$.value;
//       nodes.delete(nodeId);
//       this.nodes$.next(new Map(nodes));
//       this.log('info', `Node ${nodeId} removed from network`);
//     }
//   }

//   private handleNodeValueUpdated(event: WSEvent): void {
//     const nodeId = this.safeGetEventProperty(event, 'nodeId');
//     const value = this.safeGetEventProperty(event, 'value');
    
//     if (typeof nodeId !== 'number' || !value || typeof value !== 'object') return;
    
//     const nodes = this.nodes$.value;
//     const node = nodes.get(nodeId);
//     if (node) {
//       // Update the specific value in the node
//       const valueIndex = node.values.findIndex(v => 
//         v.commandClass === this.safeGetNestedProperty(value, 'commandClass') &&
//         v.property === this.safeGetNestedProperty(value, 'property') &&
//         v.propertyKey === this.safeGetNestedProperty(value, 'propertyKey') &&
//         v.endpoint === this.safeGetNestedProperty(value, 'endpoint')
//       );
      
//       if (valueIndex >= 0) {
//         node.values[valueIndex] = { ...node.values[valueIndex], ...value };
//       } else {
//         node.values.push(value as any);
//       }
      
//       // Update last seen
//       node.lastSeen = new Date();
      
//       nodes.set(nodeId, { ...node });
//       this.nodes$.next(new Map(nodes));
//     }
//   }

//   private handleSecurityClassGrant(event: WSEvent): void {
//     // This would typically show a dialog to the user
//     // For now, we'll grant the default security classes
//     const requested = this.safeGetEventProperty(event, 'requested');
//     if (requested && typeof requested === 'object') {
//       const securityClasses = this.safeGetNestedProperty(requested, 'securityClasses');
//       const grant: SecurityClassGrant = {
//         securityClasses: Array.isArray(securityClasses) ? securityClasses : [],
//         clientSideAuth: false
//       };
      
//       this.sendCommand('controller.grant_security_classes', grant)
//         .catch(error => this.handleError('Failed to grant security classes', error));
//     }
//   }

//   // Command sending
//   private sendCommand(command: string, params: any = {}): Promise<any> {
//     return new Promise((resolve, reject) => {
//       if (!this.socket$ || this.connectionState$.value !== 'connected') {
//         reject(new Error('Not connected to Z-Wave server'));
//         return;
//       }

//       const messageId = (++this.messageId).toString();
//       const message: WSMessage = {
//         messageId,
//         command,
//         ...params
//       };

//       const timeoutId = setTimeout(() => {
//         this.pendingCommands.delete(messageId);
//         reject({ code: ZWaveErrorCode.Timeout, message: 'Command timeout' });
//       }, this.config.commandTimeout);

//       this.pendingCommands.set(messageId, { resolve, reject, timeout: timeoutId });
      
//       try {
//         this.socket$.next(message);
//         this.log('debug', `Sent command: ${command}`, params);
//       } catch (error) {
//         this.pendingCommands.delete(messageId);
//         clearTimeout(timeoutId);
//         reject(error);
//       }
//     });
//   }

//   // Public API methods

//   // Connection state
//   getConnectionState(): Observable<'disconnected' | 'connecting' | 'connected'> {
//     return this.connectionState$.asObservable();
//   }

//   getLastError(): Observable<ZWaveError | null> {
//     return this.lastError$.asObservable();
//   }

//   isConnected(): boolean {
//     return this.connectionState$.value === 'connected';
//   }

//   // Controller operations
//   getController(): Observable<ControllerInfo | null> {
//     return this.controller$.asObservable();
//   }

//   async getControllerInfo(): Promise<ControllerInfo> {
//     const result = await this.sendCommand('controller.get_state');
//     this.controller$.next(result);
//     return result;
//   }

//   // Node management
//   getNodes(): Observable<Map<number, ZWaveNode>> {
//     return this.nodes$.asObservable();
//   }

//   getDeviceList(): Observable<DeviceListItem[]> {
//     return this.nodes$.pipe(
//       map(nodeMap => Array.from(nodeMap.values()).map(node => this.nodeToDeviceListItem(node))),
//       distinctUntilChanged()
//     );
//   }

//   async getNode(nodeId: number): Promise<ZWaveNode | null> {
//     try {
//       const result = await this.sendCommand('node.get_state', { nodeId });
//       const nodes = this.nodes$.value;
//       nodes.set(nodeId, result);
//       this.nodes$.next(new Map(nodes));
//       return result;
//     } catch (error) {
//       this.handleError(`Failed to get node ${nodeId}`, error);
//       return null;
//     }
//   }

//   private async loadNodes(): Promise<void> {
//     try {
//       // Get the controller state first, which includes node information
//       const controllerState = await this.sendCommand('controller.get_state');
//       this.controller$.next(controllerState);
      
//       // Get individual node states for each node
//       const nodeMap = new Map<number, ZWaveNode>();
      
//       if (controllerState.nodes && Array.isArray(controllerState.nodes)) {
//         for (const nodeId of controllerState.nodes) {
//           try {
//             const nodeData = await this.sendCommand('node.get_state', { nodeId });
//             nodeMap.set(nodeId, nodeData);
//           } catch (error) {
//             this.log('warn', `Failed to load node ${nodeId}:`, error);
//           }
//         }
//       }
      
//       this.nodes$.next(nodeMap);
//       this.log('info', `Loaded ${nodeMap.size} nodes`);
//     } catch (error) {
//       this.handleError('Failed to load nodes', error);
//     }
//   }

//   private async loadNodeState(nodeId: number): Promise<void> {
//     try {
//       const node = await this.sendCommand('node.get_state', { nodeId });
//       const nodes = this.nodes$.value;
//       nodes.set(nodeId, node);
//       this.nodes$.next(new Map(nodes));
//     } catch (error) {
//       this.handleError(`Failed to load node ${nodeId} state`, error);
//     }
//   }

//   // Device inclusion/exclusion
//   async startInclusion(options: { strategy?: number; forceSecurity?: boolean } = {}): Promise<void> {
//     try {
//       await this.sendCommand('controller.begin_inclusion', options);
//       this.log('info', 'Started device inclusion');
//     } catch (error) {
//       this.handleError('Failed to start inclusion', error);
//       throw error;
//     }
//   }

//   async stopInclusion(): Promise<void> {
//     try {
//       await this.sendCommand('controller.stop_inclusion');
//       this.log('info', 'Stopped device inclusion');
//     } catch (error) {
//       this.handleError('Failed to stop inclusion', error);
//       throw error;
//     }
//   }

//   async startExclusion(): Promise<void> {
//     try {
//       await this.sendCommand('controller.begin_exclusion');
//       this.log('info', 'Started device exclusion');
//     } catch (error) {
//       this.handleError('Failed to start exclusion', error);
//       throw error;
//     }
//   }

//   async stopExclusion(): Promise<void> {
//     try {
//       await this.sendCommand('controller.stop_exclusion');
//       this.log('info', 'Stopped device exclusion');
//     } catch (error) {
//       this.handleError('Failed to stop exclusion', error);
//       throw error;
//     }
//   }

//   async removeFailedNode(nodeId: number): Promise<void> {
//     try {
//       await this.sendCommand('controller.remove_failed_node', { nodeId });
//       this.log('info', `Removed failed node ${nodeId}`);
//     } catch (error) {
//       this.handleError(`Failed to remove failed node ${nodeId}`, error);
//       throw error;
//     }
//   }

//   // Device control
//   async setValue(command: SetValueCommand): Promise<void> {
//     try {
//       await this.sendCommand('node.set_value', command);
//       this.log('debug', `Set value for node ${command.nodeId}`, command);
//     } catch (error) {
//       this.handleError(`Failed to set value for node ${command.nodeId}`, error);
//       throw error;
//     }
//   }

//   async refreshValue(nodeId: number, commandClass: number, property: string | number, propertyKey?: string | number, endpoint = 0): Promise<void> {
//     try {
//       await this.sendCommand('node.refresh_value', {
//         nodeId,
//         commandClass,
//         property,
//         propertyKey,
//         endpoint
//       });
//     } catch (error) {
//       this.handleError(`Failed to refresh value for node ${nodeId}`, error);
//       throw error;
//     }
//   }

//   async refreshNode(nodeId: number): Promise<void> {
//     try {
//       await this.sendCommand('node.refresh_info', { nodeId });
//       this.log('info', `Refreshed node ${nodeId} info`);
//     } catch (error) {
//       this.handleError(`Failed to refresh node ${nodeId}`, error);
//       throw error;
//     }
//   }

//   // Convenience methods for common device types
//   async switchOn(nodeId: number, endpoint = 0): Promise<void> {
//     await this.setValue({
//       nodeId,
//       endpoint,
//       commandClass: 37, // Binary Switch
//       property: 'targetValue',
//       value: true
//     });
//   }

//   async switchOff(nodeId: number, endpoint = 0): Promise<void> {
//     await this.setValue({
//       nodeId,
//       endpoint,
//       commandClass: 37, // Binary Switch
//       property: 'targetValue',
//       value: false
//     });
//   }

//   async setDimLevel(nodeId: number, level: number, endpoint = 0): Promise<void> {
//     await this.setValue({
//       nodeId,
//       endpoint,
//       commandClass: 38, // Multilevel Switch
//       property: 'targetValue',
//       value: Math.max(0, Math.min(99, level))
//     });
//   }

//   // Event subscriptions
//   getEvents(): Observable<WSEvent> {
//     return this.events$.asObservable();
//   }

//   getEventsByType(eventType: ZWaveEventType): Observable<WSEvent> {
//     return this.events$.pipe(
//       filter(event => event.event === eventType)
//     );
//   }

//   getInclusionState(): Observable<InclusionState | null> {
//     return this.inclusionState$.asObservable();
//   }

//   getDriverReady(): Observable<boolean> {
//     return this.driverReady$.asObservable();
//   }

//   // Utility methods
//   private nodeToDeviceListItem(node: ZWaveNode): DeviceListItem {
//     const capabilities = this.analyzeNodeCapabilities(node);
//     const deviceValues = this.extractDeviceValues(node);
    
//     return {
//       nodeId: node.nodeId,
//       name: node.name || `Node ${node.nodeId}`,
//       status: node.status,
//       deviceType: node.deviceClass?.generic?.label || 'Unknown',
//       location: node.location,
//       lastSeen: node.lastSeen,
//       batteryLevel: this.getBatteryLevel(node),
//       signalStrength: this.getSignalStrength(node),
//       capabilities,
//       values: deviceValues
//     };
//   }

//   private safeGetEventProperty<T>(event: WSEvent, property: string, defaultValue?: T): T | undefined {
//     try {
//       const value = event[property];
//       return value !== undefined ? value as T : defaultValue;
//     } catch {
//       return defaultValue;
//     }
//   }

//   private safeGetNestedProperty(obj: any, property: string): any {
//     try {
//       return obj && typeof obj === 'object' ? obj[property] : undefined;
//     } catch {
//       return undefined;
//     }
//   }

//   private analyzeNodeCapabilities(node: ZWaveNode): DeviceCapabilities {
//     const commandClasses = node.endpoints[0]?.commandClasses || [];
    
//     return {
//       canSwitch: commandClasses.some(cc => cc.id === 37), // Binary Switch
//       canDim: commandClasses.some(cc => cc.id === 38), // Multilevel Switch
//       canSense: commandClasses.some(cc => [48, 49, 113].includes(cc.id)), // Sensor types
//       hasMetering: commandClasses.some(cc => cc.id === 50), // Meter
//       hasBattery: commandClasses.some(cc => cc.id === 128), // Battery
//       supportsSecurity: commandClasses.some(cc => [152, 159].includes(cc.id)), // Security, Security 2
//       isBeaming: node.isRouting && node.isListening,
//       isListening: node.isListening
//     };
//   }

//   private extractDeviceValues(node: ZWaveNode): DeviceValue[] {
//     return node.values
//       .filter(value => value.metadata.readable && !value.stateless)
//       .map(value => ({
//         id: `${value.nodeId}-${value.commandClass}-${value.property}-${value.propertyKey || 0}-${value.endpoint}`,
//         label: value.metadata.label || value.propertyName || `${value.property}`,
//         value: value.value,
//         unit: value.metadata.unit,
//         type: this.getValueType(value),
//         writable: value.metadata.writeable,
//         metadata: value.metadata
//       }));
//   }

//   private getValueType(value: any): 'switch' | 'dimmer' | 'sensor' | 'meter' | 'config' | 'info' {
//     if (value.commandClass === 37) return 'switch'; // Binary Switch
//     if (value.commandClass === 38) return 'dimmer'; // Multilevel Switch
//     if ([48, 49, 113].includes(value.commandClass)) return 'sensor'; // Sensors
//     if (value.commandClass === 50) return 'meter'; // Meter
//     if (value.commandClass === 112) return 'config'; // Configuration
//     return 'info';
//   }

//   private getBatteryLevel(node: ZWaveNode): number | undefined {
//     const batteryValue = node.values.find(v => 
//       v.commandClass === 128 && v.property === 'level'
//     );
//     return batteryValue?.value;
//   }

//   private getSignalStrength(node: ZWaveNode): number | undefined {
//     return node.statistics?.rssi;
//   }

//   private updateInclusionState(state: InclusionState | null): void {
//     this.inclusionState$.next(state);
//   }

//   private clearPendingCommands(): void {
//     for (const [messageId, pending] of this.pendingCommands) {
//       clearTimeout(pending.timeout);
//       pending.reject(new Error('Connection closed'));
//     }
//     this.pendingCommands.clear();
//   }

//   private handleError(message: string, error: any): void {
//     const zwaveError: ZWaveError = {
//       code: error.code || ZWaveErrorCode.NetworkError,
//       message: `${message}: ${error.message || error}`,
//       zwaveErrorCode: error.zwaveErrorCode,
//       context: error
//     };
    
//     this.lastError$.next(zwaveError);
//     this.log('error', zwaveError.message, zwaveError);
//   }

//   private log(level: 'error' | 'warn' | 'info' | 'debug', message: string, data?: any): void {
//     if (!this.config.enableLogging) return;
    
//     const logLevels = ['error', 'warn', 'info', 'debug'];
//     const currentLevelIndex = logLevels.indexOf(this.config.logLevel);
//     const messageLevelIndex = logLevels.indexOf(level);
    
//     if (messageLevelIndex <= currentLevelIndex) {
//       console[level](`[ZWaveService] ${message}`, data || '');
//     }
//   }

//   // Network management
//   async healNetwork(): Promise<void> {
//     try {
//       // Get all nodes and heal them individually
//       const nodes = this.nodes$.value;
//       const nodeIds = Array.from(nodes.keys()).filter(nodeId => nodeId !== 1); // Exclude controller
      
//       if (nodeIds.length === 0) {
//         this.log('info', 'No nodes to heal');
//         return;
//       }
      
//       this.log('info', `Starting to heal ${nodeIds.length} nodes`);
      
//       // Heal nodes one by one to avoid overwhelming the network
//       for (const nodeId of nodeIds) {
//         try {
//           await this.healNode(nodeId);
//           // Small delay between healing operations
//           await new Promise(resolve => setTimeout(resolve, 1000));
//         } catch (error) {
//           this.log('warn', `Failed to heal node ${nodeId}:`, error);
//           // Continue with other nodes even if one fails
//         }
//       }
      
//       this.log('info', 'Network healing completed');
//     } catch (error) {
//       this.handleError('Failed to heal network', error);
//       throw error;
//     }
//   }

//   async healNode(nodeId: number): Promise<void> {
//     try {
//       await this.sendCommand('node.rebuild_routes', { nodeId });
//       this.log('info', `Started healing node ${nodeId}`);
//     } catch (error) {
//       this.handleError(`Failed to heal node ${nodeId}`, error);
//       throw error;
//     }
//   }

//   async refreshRoutes(): Promise<void> {
//     try {
//       // Alternative approach: ping all nodes to refresh network topology
//       const nodes = this.nodes$.value;
//       const nodeIds = Array.from(nodes.keys()).filter(nodeId => nodeId !== 1); // Exclude controller
      
//       for (const nodeId of nodeIds) {
//         try {
//           await this.sendCommand('node.ping', { nodeId });
//           // Small delay between pings
//           await new Promise(resolve => setTimeout(resolve, 500));
//         } catch (error) {
//           this.log('warn', `Failed to ping node ${nodeId}:`, error);
//         }
//       }
      
//       this.log('info', 'Network topology refreshed');
//     } catch (error) {
//       this.handleError('Failed to refresh routes', error);
//       throw error;
//     }
//   }

//   async getNodeNeighbors(nodeId: number): Promise<number[]> {
//     try {
//       const result = await this.sendCommand('node.get_neighbors', { nodeId });
//       return result;
//     } catch (error) {
//       this.handleError(`Failed to get neighbors for node ${nodeId}`, error);
//       throw error;
//     }
//   }

//   async testNode(nodeId: number, powerLevel?: number): Promise<any> {
//     try {
//       const result = await this.sendCommand('node.ping', { nodeId });
//       return result;
//     } catch (error) {
//       this.handleError(`Failed to test node ${nodeId}`, error);
//       throw error;
//     }
//   }

//   // Configuration
//   async setConfigParameter(nodeId: number, parameter: number, value: any, valueSize?: number): Promise<void> {
//     try {
//       await this.sendCommand('node.set_value', {
//         nodeId,
//         commandClass: 112, // Configuration
//         property: parameter,
//         value,
//         options: valueSize ? { valueSize } : undefined
//       });
//       this.log('info', `Set config parameter ${parameter} for node ${nodeId} to ${value}`);
//     } catch (error) {
//       this.handleError(`Failed to set config parameter for node ${nodeId}`, error);
//       throw error;
//     }
//   }

//   async getConfigParameter(nodeId: number, parameter: number): Promise<any> {
//     try {
//       await this.sendCommand('node.refresh_value', {
//         nodeId,
//         commandClass: 112, // Configuration
//         property: parameter
//       });
      
//       // Return the current value from our cache
//       const node = this.nodes$.value.get(nodeId);
//       const configValue = node?.values.find(v => 
//         v.commandClass === 112 && v.property === parameter
//       );
//       return configValue?.value;
//     } catch (error) {
//       this.handleError(`Failed to get config parameter for node ${nodeId}`, error);
//       throw error;
//     }
//   }

//   // Statistics and diagnostics
//   async getNodeStatistics(nodeId: number): Promise<any> {
//     try {
//       const result = await this.sendCommand('node.get_statistics', { nodeId });
//       return result;
//     } catch (error) {
//       this.handleError(`Failed to get statistics for node ${nodeId}`, error);
//       throw error;
//     }
//   }

//   async getControllerStatistics(): Promise<any> {
//     try {
//       const result = await this.sendCommand('controller.get_statistics');
//       return result;
//     } catch (error) {
//       this.handleError('Failed to get controller statistics', error);
//       throw error;
//     }
//   }

//   // Firmware updates
//   async checkFirmwareUpdate(nodeId: number): Promise<any> {
//     try {
//       const result = await this.sendCommand('node.check_firmware_update', { nodeId });
//       return result;
//     } catch (error) {
//       this.handleError(`Failed to check firmware update for node ${nodeId}`, error);
//       throw error;
//     }
//   }

//   async startFirmwareUpdate(nodeId: number, firmwareData: ArrayBuffer): Promise<void> {
//     try {
//       // Convert ArrayBuffer to base64 for transmission
//       const base64Data = btoa(String.fromCharCode(...new Uint8Array(firmwareData)));
      
//       await this.sendCommand('node.start_firmware_update', {
//         nodeId,
//         firmware: base64Data
//       });
//       this.log('info', `Started firmware update for node ${nodeId}`);
//     } catch (error) {
//       this.handleError(`Failed to start firmware update for node ${nodeId}`, error);
//       throw error;
//     }
//   }

//   // Smart Start and S2 security
//   async provisionSmartStartNode(dsk: string, securityClasses: number[]): Promise<void> {
//     try {
//       await this.sendCommand('controller.provision_smart_start_node', {
//         dsk,
//         securityClasses
//       });
//       this.log('info', `Provisioned Smart Start node with DSK: ${dsk}`);
//     } catch (error) {
//       this.handleError('Failed to provision Smart Start node', error);
//       throw error;
//     }
//   }

//   async unprovisionSmartStartNode(dsk: string): Promise<void> {
//     try {
//       await this.sendCommand('controller.unprovision_smart_start_node', { dsk });
//       this.log('info', `Unprovisioned Smart Start node with DSK: ${dsk}`);
//     } catch (error) {
//       this.handleError('Failed to unprovision Smart Start node', error);
//       throw error;
//     }
//   }

//   // Association management
//   async getAssociations(nodeId: number, groupId: number): Promise<number[]> {
//     try {
//       const result = await this.sendCommand('node.get_associations', { nodeId, groupId });
//       return result;
//     } catch (error) {
//       this.handleError(`Failed to get associations for node ${nodeId} group ${groupId}`, error);
//       throw error;
//     }
//   }

//   async addAssociation(nodeId: number, groupId: number, targetNodeId: number, endpoint?: number): Promise<void> {
//     try {
//       await this.sendCommand('node.add_association', {
//         nodeId,
//         groupId,
//         targetNodeId,
//         endpoint
//       });
//       this.log('info', `Added association: node ${nodeId} group ${groupId} -> node ${targetNodeId}`);
//     } catch (error) {
//       this.handleError(`Failed to add association for node ${nodeId}`, error);
//       throw error;
//     }
//   }

//   async removeAssociation(nodeId: number, groupId: number, targetNodeId: number, endpoint?: number): Promise<void> {
//     try {
//       await this.sendCommand('node.remove_association', {
//         nodeId,
//         groupId,
//         targetNodeId,
//         endpoint
//       });
//       this.log('info', `Removed association: node ${nodeId} group ${groupId} -> node ${targetNodeId}`);
//     } catch (error) {
//       this.handleError(`Failed to remove association for node ${nodeId}`, error);
//       throw error;
//     }
//   }
// }
import { Injectable, OnDestroy } from '@angular/core';
import { BehaviorSubject, Observable, Subject, timer, throwError, of, combineLatest } from 'rxjs';
import { MqttService, IMqttMessage } from 'ngx-mqtt';
import { 
  retryWhen, 
  delay, 
  take, 
  takeUntil, 
  filter, 
  map, 
  catchError, 
  timeout,
  share,
  distinctUntilChanged,
  startWith,
  switchMap,
  debounceTime
} from 'rxjs/operators';
import {
  ZWaveNode,
  ControllerInfo,
  WSMessage,
  WSResponse,
  WSEvent,
  SetValueCommand,
  NodeCommand,
  ZWaveEventType,
  ZWaveError,
  ZWaveErrorCode,
  ZWaveServiceConfig,
  DEFAULT_CONFIG,
  DeviceListItem,
  NodeStatus,
  InclusionState,
  SecurityClassGrant,
  DeviceCapabilities,
  DeviceValue,
  ZWaveValue
} from '../../shared/models/zwave/ZwaveModels';

// MQTT Topic Patterns for zwave-js-ui
const MQTT_TOPICS = {
  // Status topics
  DRIVER_READY: 'zwave/_CLIENTS/ZWAVE_GATEWAY-{gatewayName}/driver/ready',
  CONTROLLER_STATUS: 'zwave/_CLIENTS/ZWAVE_GATEWAY-{gatewayName}/controller/status',
  GATEWAY_STATUS: 'zwave/_CLIENTS/ZWAVE_GATEWAY-{gatewayName}/status',
  
  // Node topics (both patterns supported)
  NODE_STATUS: 'zwave/{nodeLocation}/{nodeName}/nodeStatus', // Named
  NODE_STATUS_BY_ID: 'zwave/{nodeId}/nodeStatus', // By ID
  NODE_READY: 'zwave/{nodeLocation}/{nodeName}/ready',
  NODE_READY_BY_ID: 'zwave/{nodeId}/ready',
  NODE_LAST_ACTIVE: 'zwave/{nodeLocation}/{nodeName}/lastActive',
  NODE_LAST_ACTIVE_BY_ID: 'zwave/{nodeId}/lastActive',
  NODE_INFO: 'zwave/{nodeLocation}/{nodeName}/nodeinfo',
  NODE_INFO_BY_ID: 'zwave/{nodeId}/nodeinfo',
  
  // Value topics
  VALUE_UPDATED: 'zwave/{nodeLocation}/{nodeName}/{commandClass}/{endpoint}/{property}',
  VALUE_UPDATED_BY_ID: 'zwave/{nodeId}/{commandClass}/{endpoint}/{property}',
  VALUE_SET: 'zwave/{nodeLocation}/{nodeName}/{commandClass}/{endpoint}/{property}/set',
  VALUE_SET_BY_ID: 'zwave/{nodeId}/{commandClass}/{endpoint}/{property}/set',
  
  // Named topics (when using named mode)
  VALUE_NAMED: 'zwave/{nodeLocation}/{nodeName}/{commandClassName}/{endpoint}/{propertyName}',
  VALUE_NAMED_SET: 'zwave/{nodeLocation}/{nodeName}/{commandClassName}/{endpoint}/{propertyName}/set',
  
  // Events
  INCLUSION_STARTED: 'zwave/_CLIENTS/ZWAVE_GATEWAY-{gatewayName}/controller/inclusionStarted',
  INCLUSION_STOPPED: 'zwave/_CLIENTS/ZWAVE_GATEWAY-{gatewayName}/controller/inclusionStopped',
  EXCLUSION_STARTED: 'zwave/_CLIENTS/ZWAVE_GATEWAY-{gatewayName}/controller/exclusionStarted',
  EXCLUSION_STOPPED: 'zwave/_CLIENTS/ZWAVE_GATEWAY-{gatewayName}/controller/exclusionStopped',
  
  // API commands
  API_COMMAND: 'zwave/_CLIENTS/ZWAVE_GATEWAY-{gatewayName}/api/{command}/set',
  API_RESPONSE: 'zwave/_CLIENTS/ZWAVE_GATEWAY-{gatewayName}/api/{command}',
  
  // Broadcast commands
  BROADCAST: 'zwave/_CLIENTS/ZWAVE_GATEWAY-{gatewayName}/broadcast/{valueTopicSuffix}/set',
  MULTICAST: 'zwave/_CLIENTS/ZWAVE_GATEWAY-{gatewayName}/multicast/{valueTopicSuffix}/set'
};

interface MqttZwaveConfig extends ZWaveServiceConfig {
  mqttPrefix: string;
  gatewayName: string;
  useNamedTopics: boolean;
  subscribeToAllNodes: boolean;
}

const DEFAULT_MQTT_CONFIG: MqttZwaveConfig = {
  ...DEFAULT_CONFIG,
  serverUrl: 'ws://**************:8083/mqtt',
  mqttPrefix: 'zwave',
  gatewayName: 'zwave-js-ui',
  useNamedTopics: true,
  subscribeToAllNodes: true,
  maxReconnectAttempts: 5, // Reduced from default
  reconnectInterval: 5000 // 5 seconds between attempts
};

@Injectable({
  providedIn: 'root'
})
export class ZWaveMqttService implements OnDestroy {
  private destroy$ = new Subject<void>();
  private reconnectAttempts = 0;
  private isReconnecting = false; // Add flag to prevent multiple reconnection attempts
  private manualDisconnect = false; // Track if disconnect was intentional
  
  // Configuration
  private config: MqttZwaveConfig = { ...DEFAULT_MQTT_CONFIG };
  
  // Connection state
  private connectionState$ = new BehaviorSubject<'disconnected' | 'connecting' | 'connected'>('disconnected');
  private lastError$ = new BehaviorSubject<ZWaveError | null>(null);
  
  // Z-Wave state
  private controller$ = new BehaviorSubject<ControllerInfo | null>(null);
  private nodes$ = new BehaviorSubject<Map<number, ZWaveNode>>(new Map());
  private inclusionState$ = new BehaviorSubject<InclusionState | null>(null);
  private driverReady$ = new BehaviorSubject<boolean>(false);
  
  // Events stream
  private events$ = new Subject<WSEvent>();
  
  // Subscriptions tracking
  private activeSubscriptions = new Map<string, Observable<IMqttMessage>>();

  constructor(private mqttService: MqttService) {
    this.initializeMqttConnection();
  }

  ngOnDestroy(): void {
    this.manualDisconnect = true; // Mark as intentional disconnect
    this.destroy$.next();
    this.destroy$.complete();
    this.disconnect();
  }

  // Configuration
  updateConfig(config: Partial<MqttZwaveConfig>): void {
    this.config = { ...this.config, ...config };
    this.manualDisconnect = true; // This is intentional
    this.disconnect();
    setTimeout(() => {
      this.manualDisconnect = false;
      this.connect();
    }, 1000);
  }

  // Connection management
  private initializeMqttConnection(): void {
    // Monitor MQTT connection state with debouncing to prevent rapid state changes
    this.mqttService.state.pipe(
      takeUntil(this.destroy$),
      map(state => {
        switch (state) {
          case 0: return 'connecting'; // MqttConnectionState.CONNECTING
          case 1: return 'connected';  // MqttConnectionState.CONNECTED
          default: return 'disconnected';
        }
      }),
      distinctUntilChanged(),
      debounceTime(100) // Small debounce to prevent rapid state changes
    ).subscribe(state => {
      this.log('debug', `Connection state changed to: ${state}`);
      this.connectionState$.next(state);
      
      if (state === 'connected') {
        this.onConnected();
      } else if (state === 'disconnected' && !this.manualDisconnect) {
        this.onDisconnected();
      }
    });

    // Only auto-connect if not manually disconnected
    if (!this.manualDisconnect) {
      this.connect();
    }
  }

  private onConnected(): void {
    this.reconnectAttempts = 0;
    this.isReconnecting = false;
    this.lastError$.next(null);
    this.log('info', 'Connected to MQTT broker');
    this.initializeSubscriptions();
  }

  private onDisconnected(): void {
    this.log('warn', 'Disconnected from MQTT broker');
    
    // Only attempt reconnection if not manually disconnected and not already reconnecting
    if (!this.manualDisconnect && !this.isReconnecting) {
      this.handleReconnect();
    }
  }

  connect(): void {
    if (this.connectionState$.value === 'connected' || this.connectionState$.value === 'connecting') {
      this.log('debug', 'Already connected or connecting, skipping connect attempt');
      return;
    }

    this.manualDisconnect = false; // Reset manual disconnect flag
    this.connectionState$.next('connecting');
    this.log('info', 'Connecting to MQTT broker...');

    try {
      this.mqttService.connect();
    } catch (error) {
      this.handleError('Failed to connect to MQTT broker', error);
    }
  }

  disconnect(): void {
    this.manualDisconnect = true; // Mark as intentional
    this.isReconnecting = false; // Stop any reconnection attempts
    
    try {
      this.mqttService.disconnect();
    } catch (error) {
      this.log('warn', 'Error during disconnect:', error);
    }
    this.connectionState$.next('disconnected');
    this.clearSubscriptions();
  }

  private handleReconnect(): void {
    // Prevent multiple concurrent reconnection attempts
    if (this.isReconnecting) {
      this.log('debug', 'Reconnection already in progress, skipping');
      return;
    }

    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      this.handleError('Max reconnection attempts reached', new Error('Could not reconnect to MQTT broker'));
      return;
    }

    this.isReconnecting = true;
    this.reconnectAttempts++;
    this.log('info', `Attempting to reconnect (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})...`);
    
    timer(this.config.reconnectInterval)
      .pipe(
        takeUntil(this.destroy$),
        take(1) // Only take one emission
      )
      .subscribe(() => {
        // Check if we're still disconnected and should reconnect
        if (this.connectionState$.value === 'disconnected' && !this.manualDisconnect) {
          this.connect();
        } else {
          this.isReconnecting = false;
        }
      });
  }

  private initializeSubscriptions(): void {
    this.clearSubscriptions();
    
    // Subscribe to gateway status
    this.subscribeToGatewayStatus();
    
    // Subscribe to driver ready
    this.subscribeToDriverReady();
    
    // Subscribe to controller events
    this.subscribeToControllerEvents();
    
    // Subscribe to all node updates if configured
    if (this.config.subscribeToAllNodes) {
      this.subscribeToAllNodes();
    }
    
    // Load initial state
    this.loadInitialState();
  }

  private subscribeToGatewayStatus(): void {
    const topic = this.buildTopic(MQTT_TOPICS.GATEWAY_STATUS);
    this.subscribe(topic, 'gateway-status').pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (message) => {
        const payload = this.parsePayload(message.payload.toString());
        this.log('debug', 'Gateway status:', payload);
      },
      error: (error) => this.handleError('Gateway status subscription error', error)
    });
  }

  private subscribeToDriverReady(): void {
    const topic = this.buildTopic(MQTT_TOPICS.DRIVER_READY);
    this.subscribe(topic, 'driver-ready').pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (message) => {
        const payload = this.parsePayload(message.payload.toString());
        const isReady = payload?.value === true || payload === true;
        this.driverReady$.next(isReady);
        
        if (isReady) {
          this.log('info', 'Z-Wave driver is ready');
          this.loadNodes();
        }
      },
      error: (error) => this.handleError('Driver ready subscription error', error)
    });
  }

  private subscribeToControllerEvents(): void {
    // Inclusion events
    const inclusionStartedTopic = this.buildTopic(MQTT_TOPICS.INCLUSION_STARTED);
    this.subscribe(inclusionStartedTopic, 'inclusion-started').pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (message) => {
        const payload = this.parsePayload(message.payload.toString());
        this.updateInclusionState({ 
          strategy: payload?.strategy || 0, 
          active: true, 
          userCallbacks: {} 
        });
        this.events$.next({ 
          event: 'inclusion started', 
          source: 'controller',
          data: payload 
        } as WSEvent);
      }
    });

    const inclusionStoppedTopic = this.buildTopic(MQTT_TOPICS.INCLUSION_STOPPED);
    this.subscribe(inclusionStoppedTopic, 'inclusion-stopped').pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (message) => {
        this.updateInclusionState(null);
        this.events$.next({ 
          event: 'inclusion stopped', 
          source: 'controller',
          data: null 
        } as WSEvent);
      }
    });

    // Exclusion events
    const exclusionStartedTopic = this.buildTopic(MQTT_TOPICS.EXCLUSION_STARTED);
    this.subscribe(exclusionStartedTopic, 'exclusion-started').pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (message) => {
        this.events$.next({ 
          event: 'exclusion started', 
          source: 'controller',
          data: null 
        } as WSEvent);
      }
    });

    const exclusionStoppedTopic = this.buildTopic(MQTT_TOPICS.EXCLUSION_STOPPED);
    this.subscribe(exclusionStoppedTopic, 'exclusion-stopped').pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (message) => {
        this.events$.next({ 
          event: 'exclusion stopped', 
          source: 'controller',
          data: null 
        } as WSEvent);
      }
    });
  }

  private subscribeToAllNodes(): void {
    // Subscribe to all node value updates using wildcards
    const valueTopicPattern = this.config.useNamedTopics 
      ? `${this.config.mqttPrefix}/+/+/+/+/+`
      : `${this.config.mqttPrefix}/+/+/+/+`;
    
    this.subscribe(valueTopicPattern, 'all-node-values').pipe(
      takeUntil(this.destroy$),
      filter(message => !message.topic.endsWith('/set') && !message.topic.includes('/_CLIENTS/'))
    ).subscribe({
      next: (message) => this.handleNodeValueUpdate(message),
      error: (error) => this.handleError('Node value subscription error', error)
    });

    // Subscribe to node status updates
    const nodeStatusPattern = `${this.config.mqttPrefix}/+/nodeStatus`;
    this.subscribe(nodeStatusPattern, 'node-status').pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (message) => this.handleNodeStatusUpdate(message),
      error: (error) => this.handleError('Node status subscription error', error)
    });

    // Subscribe to node info updates
    const nodeInfoPattern = `${this.config.mqttPrefix}/+/nodeinfo`;
    this.subscribe(nodeInfoPattern, 'node-info').pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (message) => this.handleNodeInfoUpdate(message),
      error: (error) => this.handleError('Node info subscription error', error)
    });
  }

  private subscribe(topic: string, key: string): Observable<IMqttMessage> {
    if (this.activeSubscriptions.has(key)) {
      return this.activeSubscriptions.get(key)!;
    }

    const subscription = this.mqttService.observe(topic).pipe(
      share(),
      catchError(error => {
        this.handleError(`Subscription error for ${topic}`, error);
        return of(); // Return empty observable to prevent subscription termination
      })
    );

    this.activeSubscriptions.set(key, subscription);
    this.log('debug', `Subscribed to MQTT topic: ${topic}`);
    
    return subscription;
  }

  private clearSubscriptions(): void {
    this.activeSubscriptions.clear();
  }

  // ... (rest of the methods remain the same as they were working correctly)

  private handleNodeValueUpdate(message: IMqttMessage): void {
    const topicParts = message.topic.split('/');
    const payload = this.parsePayload(message.payload.toString());
    
    let nodeId: number;
    let commandClass: number;
    let endpoint: number;
    let property: string | number;
    
    if (this.config.useNamedTopics && topicParts.length >= 6) {
      // Named topics: zwave/location/nodeName/commandClassName/endpoint/propertyName
      const nodeName = topicParts[2];
      nodeId = this.getNodeIdByName(nodeName);
      commandClass = this.getCommandClassIdByName(topicParts[3]);
      endpoint = parseInt(topicParts[4].replace('endpoint_', '')) || 0;
      property = topicParts[5];
    } else if (topicParts.length >= 5) {
      // Value ID topics: zwave/nodeId/commandClass/endpoint/property
      nodeId = parseInt(topicParts[1]);
      commandClass = parseInt(topicParts[2]);
      endpoint = parseInt(topicParts[3]);
      property = topicParts[4];
      
      // Try to parse property as number if possible
      const propertyNum = parseInt(property);
      if (!isNaN(propertyNum)) {
        property = propertyNum;
      }
    } else {
      this.log('warn', `Unable to parse topic: ${message.topic}`);
      return;
    }

    if (isNaN(nodeId)) {
      this.log('warn', `Invalid node ID from topic: ${message.topic}`);
      return;
    }

    this.updateNodeValue(nodeId, commandClass, endpoint, property, payload);
  }

  private handleNodeStatusUpdate(message: IMqttMessage): void {
    const topicParts = message.topic.split('/');
    const nodeId = this.extractNodeId(topicParts[1]);
    const payload = this.parsePayload(message.payload.toString());
    
    if (nodeId && !isNaN(nodeId)) {
      this.updateNodeStatus(nodeId, payload);
    }
  }

  private handleNodeInfoUpdate(message: IMqttMessage): void {
    const topicParts = message.topic.split('/');
    const nodeId = this.extractNodeId(topicParts[1]);
    const payload = this.parsePayload(message.payload.toString());
    
    if (nodeId && !isNaN(nodeId)) {
      this.updateNodeInfo(nodeId, payload);
    }
  }

  private updateNodeValue(nodeId: number, commandClass: number, endpoint: number, property: string | number, payload: any): void {
    const nodes = this.nodes$.value;
    const node = nodes.get(nodeId);
    
    if (!node) {
      this.log('warn', `Node ${nodeId} not found for value update`);
      return;
    }

    const value = payload?.value !== undefined ? payload.value : payload;
    const timestamp = payload?.time ? new Date(payload.time) : new Date();

    // Convert property to string for comparison
    const propertyStr = typeof property === 'string' ? property : property.toString();

    // Find and update the specific value
    const valueIndex = node.values.findIndex(v => 
      v.commandClass === commandClass &&
      v.property.toString() === propertyStr &&
      v.endpoint === endpoint
    );

    if (valueIndex >= 0) {
      node.values[valueIndex] = { 
        ...node.values[valueIndex], 
        value
      };
    } else {
      // Add new value if not found
      const newValue: ZWaveValue = {
        nodeId,
        commandClass,
        commandClassName: this.getCommandClassName(commandClass),
        endpoint,
        property: propertyStr,
        propertyKey: undefined,
        propertyName: propertyStr,
        value,
        metadata: {
          type: 'any',
          readable: true,
          writeable: false
        },
        stateless: false
      };
      node.values.push(newValue);
    }

    // Update last seen
    node.lastSeen = timestamp;
    
    nodes.set(nodeId, { ...node });
    this.nodes$.next(new Map(nodes));

    // Emit event
    this.events$.next({
      event: 'node value updated',
      source: 'node',
      nodeId,
      value: { commandClass, endpoint, property: propertyStr, value }
    } as WSEvent);
  }

  private updateNodeStatus(nodeId: number, payload: any): void {
    const nodes = this.nodes$.value;
    const node = nodes.get(nodeId);
    
    if (node) {
      const status = payload?.value || payload;
      node.status = status;
      nodes.set(nodeId, { ...node });
      this.nodes$.next(new Map(nodes));
    }
  }

  private updateNodeInfo(nodeId: number, payload: any): void {
    const nodes = this.nodes$.value;
    const node = nodes.get(nodeId);
    
    if (node) {
      // Update node info from payload
      Object.assign(node, payload);
      nodes.set(nodeId, { ...node });
      this.nodes$.next(new Map(nodes));
    }
  }

  // MQTT Publishing methods
  private publish(topic: string, payload: any, options: any = {}): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const message = typeof payload === 'string' ? payload : JSON.stringify(payload);
        this.mqttService.publish(topic, message, options).subscribe({
          next: () => {
            this.log('debug', `Published to ${topic}:`, payload);
            resolve();
          },
          error: (error) => {
            this.handleError(`Failed to publish to ${topic}`, error);
            reject(error);
          }
        });
      } catch (error) {
        this.handleError(`Failed to publish to ${topic}`, error);
        reject(error);
      }
    });
  }

  // Public API methods

  // Connection state
  getConnectionState(): Observable<'disconnected' | 'connecting' | 'connected'> {
    return this.connectionState$.asObservable();
  }

  getLastError(): Observable<ZWaveError | null> {
    return this.lastError$.asObservable();
  }

  isConnected(): boolean {
    return this.connectionState$.value === 'connected';
  }

  // Controller operations
  getController(): Observable<ControllerInfo | null> {
    return this.controller$.asObservable();
  }

  async getControllerInfo(): Promise<ControllerInfo> {
    const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'getControllerInfo' });
    await this.publish(topic, {});
    
    // Wait for response - in a real implementation, you'd implement request/response correlation
    // For now, return cached controller info
    return this.controller$.value || {} as ControllerInfo;
  }

  // Node management
  getNodes(): Observable<Map<number, ZWaveNode>> {
    return this.nodes$.asObservable();
  }

  getDeviceList(): Observable<DeviceListItem[]> {
    return this.nodes$.pipe(
      map(nodeMap => Array.from(nodeMap.values()).map(node => this.nodeToDeviceListItem(node))),
      distinctUntilChanged()
    );
  }

  async getNode(nodeId: number): Promise<ZWaveNode | null> {
    const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'getNodeInfo' });
    await this.publish(topic, { nodeId });
    
    // Return cached node for now
    return this.nodes$.value.get(nodeId) || null;
  }

  private async loadInitialState(): Promise<void> {
    try {
      // Request controller info
      await this.getControllerInfo();
      
      // Request all nodes
      await this.loadNodes();
      
      this.log('info', 'Initial state loaded successfully');
    } catch (error) {
      this.handleError('Failed to load initial state', error);
    }
  }

  private async loadNodes(): Promise<void> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'getAllNodes' });
      await this.publish(topic, {});
      
      this.log('info', 'Requested all nodes from gateway');
    } catch (error) {
      this.handleError('Failed to load nodes', error);
    }
  }

  // Device control
  async setValue(command: SetValueCommand): Promise<void> {
    try {
      const topic = this.buildValueSetTopic(
        command.nodeId, 
        command.commandClass, 
        command.endpoint || 0, // Default to 0 if undefined
        command.property
      );
      const payload = command.options ? { value: command.value, ...command.options } : command.value;
      
      await this.publish(topic, payload);
      this.log('debug', `Set value for node ${command.nodeId}`, command);
    } catch (error) {
      this.handleError(`Failed to set value for node ${command.nodeId}`, error);
      throw error;
    }
  }

  async refreshValue(nodeId: number, commandClass: number, property: string | number, propertyKey?: string | number, endpoint = 0): Promise<void> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'refreshValue' });
      await this.publish(topic, {
        nodeId,
        commandClass,
        property,
        propertyKey,
        endpoint
      });
    } catch (error) {
      this.handleError(`Failed to refresh value for node ${nodeId}`, error);
      throw error;
    }
  }

  async refreshNode(nodeId: number): Promise<void> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'refreshNodeInfo' });
      await this.publish(topic, { nodeId });
      this.log('info', `Refreshed node ${nodeId} info`);
    } catch (error) {
      this.handleError(`Failed to refresh node ${nodeId}`, error);
      throw error;
    }
  }

  // Device inclusion/exclusion
  async startInclusion(options: { strategy?: number; forceSecurity?: boolean } = {}): Promise<void> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'startInclusion' });
      await this.publish(topic, options);
      this.log('info', 'Started device inclusion');
    } catch (error) {
      this.handleError('Failed to start inclusion', error);
      throw error;
    }
  }

  async stopInclusion(): Promise<void> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'stopInclusion' });
      await this.publish(topic, {});
      this.log('info', 'Stopped device inclusion');
    } catch (error) {
      this.handleError('Failed to stop inclusion', error);
      throw error;
    }
  }

  async startExclusion(): Promise<void> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'startExclusion' });
      await this.publish(topic, {});
      this.log('info', 'Started device exclusion');
    } catch (error) {
      this.handleError('Failed to start exclusion', error);
      throw error;
    }
  }

  async stopExclusion(): Promise<void> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'stopExclusion' });
      await this.publish(topic, {});
      this.log('info', 'Stopped device exclusion');
    } catch (error) {
      this.handleError('Failed to stop exclusion', error);
      throw error;
    }
  }

  async removeFailedNode(nodeId: number): Promise<void> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'removeFailedNode' });
      await this.publish(topic, { nodeId });
      this.log('info', `Removed failed node ${nodeId}`);
    } catch (error) {
      this.handleError(`Failed to remove failed node ${nodeId}`, error);
      throw error;
    }
  }

  // Convenience methods for common device types
  async switchOn(nodeId: number, endpoint = 0): Promise<void> {
    await this.setValue({
      nodeId,
      endpoint,
      commandClass: 37, // Binary Switch
      property: 'targetValue',
      value: true
    });
  }

  async switchOff(nodeId: number, endpoint = 0): Promise<void> {
    await this.setValue({
      nodeId,
      endpoint,
      commandClass: 37, // Binary Switch
      property: 'targetValue',
      value: false
    });
  }

  async setDimLevel(nodeId: number, level: number, endpoint = 0, transitionDuration?: string): Promise<void> {
    const command: SetValueCommand = {
      nodeId,
      endpoint,
      commandClass: 38, // Multilevel Switch
      property: 'targetValue',
      value: Math.max(0, Math.min(99, level))
    };
    
    if (transitionDuration) {
      command.options = { transitionDuration };
    }
    
    await this.setValue(command);
  }

  // Event subscriptions
  getEvents(): Observable<WSEvent> {
    return this.events$.asObservable();
  }

  getEventsByType(eventType: ZWaveEventType): Observable<WSEvent> {
    return this.events$.pipe(
      filter(event => event.event === eventType)
    );
  }

  getInclusionState(): Observable<InclusionState | null> {
    return this.inclusionState$.asObservable();
  }

  getDriverReady(): Observable<boolean> {
    return this.driverReady$.asObservable();
  }

  // Network management
  async healNetwork(): Promise<void> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'healNetwork' });
      await this.publish(topic, {});
      this.log('info', 'Started network healing');
    } catch (error) {
      this.handleError('Failed to heal network', error);
      throw error;
    }
  }

  async healNode(nodeId: number): Promise<void> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'healNode' });
      await this.publish(topic, { nodeId });
      this.log('info', `Started healing node ${nodeId}`);
    } catch (error) {
      this.handleError(`Failed to heal node ${nodeId}`, error);
      throw error;
    }
  }

  async refreshRoutes(): Promise<void> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'refreshRoutes' });
      await this.publish(topic, {});
      this.log('info', 'Network topology refresh requested');
    } catch (error) {
      this.handleError('Failed to refresh routes', error);
      throw error;
    }
  }

  async getNodeNeighbors(nodeId: number): Promise<number[]> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'getNodeNeighbors' });
      await this.publish(topic, { nodeId });
      
      // In a real implementation, you'd wait for the response
      // For now, return empty array
      return [];
    } catch (error) {
      this.handleError(`Failed to get neighbors for node ${nodeId}`, error);
      throw error;
    }
  }

  async testNode(nodeId: number, powerLevel?: number): Promise<any> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'pingNode' });
      await this.publish(topic, { nodeId, powerLevel });
      
      // Return success for now
      return { success: true };
    } catch (error) {
      this.handleError(`Failed to test node ${nodeId}`, error);
      throw error;
    }
  }

  // Configuration - Alternative approach using direct MQTT topics
  async setConfigParameterDirect(nodeId: number, parameter: number, value: any, valueSize?: number): Promise<void> {
    try {
      // Build direct MQTT topic for configuration parameter
      const topic = this.buildValueSetTopic(nodeId, 112, 0, parameter); // 112 = Configuration CC
      
      // Create payload with valueSize if provided
      const payload = valueSize ? { value, valueSize } : value;
      
      await this.publish(topic, payload);
      this.log('info', `Set config parameter ${parameter} for node ${nodeId} to ${value}${valueSize ? ` (size: ${valueSize})` : ''}`);
    } catch (error) {
      this.handleError(`Failed to set config parameter for node ${nodeId}`, error);
      throw error;
    }
  }

  // Configuration - Standard approach (without valueSize)
  async setConfigParameter(nodeId: number, parameter: number, value: any, valueSize?: number): Promise<void> {
    if (valueSize) {
      // Use direct MQTT approach when valueSize is needed
      return this.setConfigParameterDirect(nodeId, parameter, value, valueSize);
    }

    try {
      const command: SetValueCommand = {
        nodeId,
        commandClass: 112, // Configuration
        property: parameter,
        value,
        endpoint: 0
      };
      
      await this.setValue(command);
      this.log('info', `Set config parameter ${parameter} for node ${nodeId} to ${value}`);
    } catch (error) {
      this.handleError(`Failed to set config parameter for node ${nodeId}`, error);
      throw error;
    }
  }

  async getConfigParameter(nodeId: number, parameter: number): Promise<any> {
    try {
      await this.refreshValue(nodeId, 112, parameter);
      
      // Return the current value from our cache
      const node = this.nodes$.value.get(nodeId);
      const configValue = node?.values.find(v => 
        v.commandClass === 112 && v.property === parameter
      );
      return configValue?.value;
    } catch (error) {
      this.handleError(`Failed to get config parameter for node ${nodeId}`, error);
      throw error;
    }
  }

  // Statistics and diagnostics
  async getNodeStatistics(nodeId: number): Promise<any> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'getNodeStatistics' });
      await this.publish(topic, { nodeId });
      
      // Return placeholder for now
      return {};
    } catch (error) {
      this.handleError(`Failed to get statistics for node ${nodeId}`, error);
      throw error;
    }
  }

  async getControllerStatistics(): Promise<any> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'getControllerStatistics' });
      await this.publish(topic, {});
      
      // Return placeholder for now
      return {};
    } catch (error) {
      this.handleError('Failed to get controller statistics', error);
      throw error;
    }
  }

  // Firmware updates
  async checkFirmwareUpdate(nodeId: number): Promise<any> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'checkFirmwareUpdate' });
      await this.publish(topic, { nodeId });
      
      // Return placeholder for now
      return {};
    } catch (error) {
      this.handleError(`Failed to check firmware update for node ${nodeId}`, error);
      throw error;
    }
  }

  async startFirmwareUpdate(nodeId: number, firmwareData: ArrayBuffer): Promise<void> {
    try {
      // Convert ArrayBuffer to base64 for transmission
      const base64Data = btoa(String.fromCharCode(...new Uint8Array(firmwareData)));
      
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'startFirmwareUpdate' });
      await this.publish(topic, {
        nodeId,
        firmware: base64Data
      });
      this.log('info', `Started firmware update for node ${nodeId}`);
    } catch (error) {
      this.handleError(`Failed to start firmware update for node ${nodeId}`, error);
      throw error;
    }
  }

  // Smart Start and S2 security
  async provisionSmartStartNode(dsk: string, securityClasses: number[]): Promise<void> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'provisionSmartStartNode' });
      await this.publish(topic, {
        dsk,
        securityClasses
      });
      this.log('info', `Provisioned Smart Start node with DSK: ${dsk}`);
    } catch (error) {
      this.handleError('Failed to provision Smart Start node', error);
      throw error;
    }
  }

  async unprovisionSmartStartNode(dsk: string): Promise<void> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'unprovisionSmartStartNode' });
      await this.publish(topic, { dsk });
      this.log('info', `Unprovisioned Smart Start node with DSK: ${dsk}`);
    } catch (error) {
      this.handleError('Failed to unprovision Smart Start node', error);
      throw error;
    }
  }

  // Association management
  async getAssociations(nodeId: number, groupId: number): Promise<number[]> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'getAssociations' });
      await this.publish(topic, { nodeId, groupId });
      
      // Return empty array for now
      return [];
    } catch (error) {
      this.handleError(`Failed to get associations for node ${nodeId} group ${groupId}`, error);
      throw error;
    }
  }

  async addAssociation(nodeId: number, groupId: number, targetNodeId: number, endpoint?: number): Promise<void> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'addAssociation' });
      await this.publish(topic, {
        nodeId,
        groupId,
        targetNodeId,
        endpoint
      });
      this.log('info', `Added association: node ${nodeId} group ${groupId} -> node ${targetNodeId}`);
    } catch (error) {
      this.handleError(`Failed to add association for node ${nodeId}`, error);
      throw error;
    }
  }

  async removeAssociation(nodeId: number, groupId: number, targetNodeId: number, endpoint?: number): Promise<void> {
    try {
      const topic = this.buildTopic(MQTT_TOPICS.API_COMMAND, { command: 'removeAssociation' });
      await this.publish(topic, {
        nodeId,
        groupId,
        targetNodeId,
        endpoint
      });
      this.log('info', `Removed association: node ${nodeId} group ${groupId} -> node ${targetNodeId}`);
    } catch (error) {
      this.handleError(`Failed to remove association for node ${nodeId}`, error);
      throw error;
    }
  }

  // Utility methods
  private buildTopic(template: string, params: Record<string, any> = {}): string {
    let topic = template;
    
    // Replace common placeholders
    topic = topic.replace('{gatewayName}', this.config.gatewayName);
    topic = topic.replace('{mqttPrefix}', this.config.mqttPrefix);
    
    // Replace custom parameters
    Object.keys(params).forEach(key => {
      topic = topic.replace(`{${key}}`, params[key]);
    });
    
    return topic;
  }

  private buildValueSetTopic(nodeId: number, commandClass: number, endpoint: number, property: string | number): string {
    const node = this.nodes$.value.get(nodeId);
    
    if (this.config.useNamedTopics && node?.name && node?.location) {
      // Use named topics: zwave/location/nodeName/commandClassName/endpoint/propertyName/set
      const commandClassName = this.getCommandClassName(commandClass);
      const propertyName = typeof property === 'string' ? property : property.toString();
      return `${this.config.mqttPrefix}/${node.location}/${node.name}/${commandClassName}/endpoint_${endpoint}/${propertyName}/set`;
    } else {
      // Use value ID topics: zwave/nodeId/commandClass/endpoint/property/set
      const propertyStr = typeof property === 'string' ? property : property.toString();
      return `${this.config.mqttPrefix}/${nodeId}/${commandClass}/${endpoint}/${propertyStr}/set`;
    }
  }

  private getCommandClassName(commandClass: number): string {
    // Map common command class IDs to names
    const commandClassNames: Record<number, string> = {
      37: 'switch_binary',
      38: 'switch_multilevel',
      48: 'sensor_binary',
      49: 'sensor_multilevel',
      50: 'meter',
      112: 'configuration',
      113: 'notification',
      128: 'battery',
      134: 'version'
    };
    
    return commandClassNames[commandClass] || `commandclass_${commandClass}`;
  }

  private getCommandClassIdByName(name: string): number {
    // Map command class names back to IDs
    const nameToId: Record<string, number> = {
      'switch_binary': 37,
      'switch_multilevel': 38,
      'sensor_binary': 48,
      'sensor_multilevel': 49,
      'meter': 50,
      'configuration': 112,
      'notification': 113,
      'battery': 128,
      'version': 134
    };
    
    return nameToId[name] || parseInt(name.replace('commandclass_', '')) || 0;
  }

  private nodeToDeviceListItem(node: ZWaveNode): DeviceListItem {
    const capabilities = this.analyzeNodeCapabilities(node);
    const deviceValues = this.extractDeviceValues(node);
    
    return {
      nodeId: node.nodeId,
      name: node.name || `Node ${node.nodeId}`,
      status: node.status,
      deviceType: node.deviceClass?.generic?.label || 'Unknown',
      location: node.location,
      lastSeen: node.lastSeen,
      batteryLevel: this.getBatteryLevel(node),
      signalStrength: this.getSignalStrength(node),
      capabilities,
      values: deviceValues
    };
  }

  private analyzeNodeCapabilities(node: ZWaveNode): DeviceCapabilities {
    const commandClasses = node.endpoints?.[0]?.commandClasses || [];
    
    return {
      canSwitch: commandClasses.some(cc => cc.id === 37), // Binary Switch
      canDim: commandClasses.some(cc => cc.id === 38), // Multilevel Switch
      canSense: commandClasses.some(cc => [48, 49, 113].includes(cc.id)), // Sensor types
      hasMetering: commandClasses.some(cc => cc.id === 50), // Meter
      hasBattery: commandClasses.some(cc => cc.id === 128), // Battery
      supportsSecurity: commandClasses.some(cc => [152, 159].includes(cc.id)), // Security, Security 2
      isBeaming: node.isRouting && node.isListening,
      isListening: node.isListening
    };
  }

  private extractDeviceValues(node: ZWaveNode): DeviceValue[] {
    return (node.values || [])
      .filter(value => value.metadata?.readable && !value.stateless)
      .map(value => ({
        id: `${value.nodeId}-${value.commandClass}-${value.property}-${value.propertyKey || 0}-${value.endpoint}`,
        label: value.metadata?.label || value.propertyName || `${value.property}`,
        value: value.value,
        unit: value.metadata?.unit,
        type: this.getValueType(value),
        writable: value.metadata?.writeable,
        metadata: value.metadata
      }));
  }

  private getValueType(value: any): 'switch' | 'dimmer' | 'sensor' | 'meter' | 'config' | 'info' {
    if (value.commandClass === 37) return 'switch'; // Binary Switch
    if (value.commandClass === 38) return 'dimmer'; // Multilevel Switch
    if ([48, 49, 113].includes(value.commandClass)) return 'sensor'; // Sensors
    if (value.commandClass === 50) return 'meter'; // Meter
    if (value.commandClass === 112) return 'config'; // Configuration
    return 'info';
  }

  private getBatteryLevel(node: ZWaveNode): number | undefined {
    const batteryValue = node.values?.find(v => 
      v.commandClass === 128 && v.property === 'level'
    );
    return batteryValue?.value;
  }

  private getSignalStrength(node: ZWaveNode): number | undefined {
    return node.statistics?.rssi;
  }

  private getNodeIdByName(nodeName: string): number {
    // Find node by name in our cache
    for (const [nodeId, node] of this.nodes$.value) {
      if (node.name === nodeName) {
        return nodeId;
      }
    }
    
    // If not found, try to parse as number
    const parsed = parseInt(nodeName);
    return isNaN(parsed) ? 0 : parsed;
  }

  private extractNodeId(topicPart: string): number {
    // Try to extract node ID from topic part
    // Could be a node name or node ID
    const parsed = parseInt(topicPart);
    if (!isNaN(parsed)) {
      return parsed;
    }
    
    // Try to find by name
    return this.getNodeIdByName(topicPart);
  }

  private parsePayload(payload: string): any {
    try {
      return JSON.parse(payload);
    } catch {
      // Try to parse as number
      const num = parseFloat(payload);
      if (!isNaN(num)) {
        return num;
      }
      
      // Try to parse as boolean
      if (payload.toLowerCase() === 'true') return true;
      if (payload.toLowerCase() === 'false') return false;
      
      // Return as string
      return payload;
    }
  }

  private updateInclusionState(state: InclusionState | null): void {
    this.inclusionState$.next(state);
  }

  private handleError(message: string, error: any): void {
    const zwaveError: ZWaveError = {
      code: error.code || ZWaveErrorCode.NetworkError,
      message: `${message}: ${error.message || error}`,
      zwaveErrorCode: error.zwaveErrorCode,
      context: error
    };
    
    this.lastError$.next(zwaveError);
    this.log('error', zwaveError.message, zwaveError);
  }

  private log(level: 'error' | 'warn' | 'info' | 'debug', message: string, data?: any): void {
    if (!this.config.enableLogging) return;
    
    const logLevels = ['error', 'warn', 'info', 'debug'];
    const currentLevelIndex = logLevels.indexOf(this.config.logLevel);
    const messageLevelIndex = logLevels.indexOf(level);
    
    if (messageLevelIndex <= currentLevelIndex) {
      console[level](`[ZWaveMqttService] ${message}`, data || '');
    }
  }
}