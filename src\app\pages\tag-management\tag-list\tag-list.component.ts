import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS,
} from 'ng-angular-popup';

import { TagApiService } from '@app/core/services/administrative/tag.service';
import { TagAssignmentApiService } from '@app/core/services/administrative/tag-assignment.service';
import { Tag } from '@app/core/models/tag';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { SortPage } from '@app/core/models/util/page';


@Component({
  selector: 'app-tag-list',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatPaginatorModule,
    MatDialogModule,
    MatButtonModule,
    NgToastComponent
  ],
  templateUrl: './tag-list.component.html',
  styleUrls: ['./tag-list.component.css']
})
export class TagListComponent implements OnInit {
  TOAST_POSITIONS = TOAST_POSITIONS;

  // Form
  tagForm: FormGroup;

  // Data
  tags: Tag[] = [];
  filteredTags: Tag[] = [];
  tagCounts: any[] = [];

  // UI State
  isLoading = false;
  isSubmitting = false;
  showTagForm = false;
  searchTerm = '';

  // Pagination
  currentPage = 0;
  pageSize = 5;
  totalCount = 0;

  // Sorting
  currentSort: SortPage[] = [{
    Column: 'CreatedAt',
    Sort: 'desc'
  }];

  // Table configuration for tag counts
  headers: { header: string; colspan: number }[] = [
    { header: 'Nom Tag', colspan: 1 },
    { header: 'Affectations', colspan: 1 },
    { header: 'Total Affectations', colspan: 1 }
  ];

  keys: string[] = [
    'TagName',
    'AssignmentDetails',
    'TotalAssignments'
  ];

  // Actions for the table
  tableActions: string[] = ['edit', 'delete'];

  constructor(
    private fb: FormBuilder,
    private dialog: MatDialog,
    private toast: NgToastService,
    private tagService: TagApiService,
    private tagAssignmentService: TagAssignmentApiService
  ) {
    this.tagForm = this.fb.group({
      id: [''],
      nom: ['', [Validators.required, Validators.minLength(2)]]
    });
  }

  ngOnInit(): void {
    this.loadTagCounts();
  }

  // Transform tag counts for generic table display
  get transformedTags(): any[] {
    return this.tagCounts.map(tag => ({
      ...tag,
      AssignmentDetails: this.formatAssignmentDetails(tag)
    }));
  }

  // Format assignment details for display
  formatAssignmentDetails(tag: any): string {
    const clientCount = tag.ClientCount || 0;
    const siteCount = tag.SiteCount || 0;
    const localCount = tag.LocalCount || 0;

    return `Clients: ${clientCount} • Sites: ${siteCount} • Locaux: ${localCount}`;
  }

  loadTagCounts(): void {
    this.isLoading = true;

    const searchRequest = {
      pageSize: this.pageSize,
      pageNumber: this.currentPage + 1, // Convert to 1-based index for backend
      searchTerm: this.searchTerm.trim()
    };

    this.tagAssignmentService.searchTagAssignmentCounts(searchRequest).subscribe({
      next: (response: any) => {
        this.tagCounts = response.Data ?? [];
        this.totalCount = response.TotalCount || 0;
        this.currentPage = (response.PageNumber || 1) - 1; // Convert back to 0-based index
        this.pageSize = response.PageSize || this.pageSize;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading tag counts:', error);
        this.showError('Erreur lors du chargement des statistiques des tags');
        this.isLoading = false;
      }
    });
  }

  filterTags(): void {
    this.currentPage = 0;
    this.loadTagCounts();
  }

  onSearchKeyup(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.filterTags();
    } else if (event.key === 'Backspace' && this.searchTerm === '') {
      this.loadTagCounts();
    }
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadTagCounts();
  }

  showCreateTagForm(): void {
    this.tagForm.reset();
    this.showTagForm = true;
  }

  hideTagForm(): void {
    this.showTagForm = false;
    this.tagForm.reset();
    this.isSubmitting = false;
  }

  editTag(tag: any): void {
    this.tagForm.patchValue({
      id: tag.TagId,
      nom: tag.TagName
    });
    this.showTagForm = true;
  }

  async onSubmitTag(): Promise<void> {
    if (this.tagForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    const formValue = this.tagForm.value;

    // Transform form data to match API expected format
    const tagData = {
      Id: formValue.id || undefined,
      Nom: formValue.nom
    };

    try {
      if (formValue.id) {
        // Update existing tag
        await this.tagService.update(tagData).toPromise();
        this.showSuccess('Tag mis à jour avec succès');
      } else {
        // Create new tag
        await this.tagService.create(tagData).toPromise();
        this.showSuccess('Tag créé avec succès');
      }

      this.hideTagForm();
      this.loadTagCounts();
    } catch (error) {
      console.error('Error saving tag:', error);
      this.showError('Erreur lors de la sauvegarde du tag');
    } finally {
      this.isSubmitting = false;
    }
  }

  deleteTag(tag: any): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmer la suppression',
        message: 'Êtes-vous sûr de vouloir supprimer ce tag ?',
        icon: 'delete',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && tag.TagId) {
        this.isLoading = true;
        this.tagService.delete(tag.TagId).subscribe({
          next: () => {
            this.showSuccess('Tag supprimé avec succès');
            this.loadTagCounts();
          },
          error: (error) => {
            console.error('Error deleting tag:', error);
            this.showError('Erreur lors de la suppression du tag');
            this.isLoading = false;
          }
        });
      }
    });
  }

  handleAction(event: { action: string; row: any }): void {
    const { action, row } = event;
    if (action === 'edit') {
      this.editTag(row);
    } else if (action === 'delete') {
      this.deleteTag(row);
    }
  }

  // Action helper methods for the table
  triggerAction(action: string, row: any): void {
    this.handleAction({ action, row });
  }

  getActionIcon(action: string): string {
    switch (action) {
      case 'edit':
        return 'edit';
      case 'delete':
        return 'delete';
      default:
        return '';
    }
  }

  getActionClass(action: string): string {
    switch (action) {
      case 'edit':
        return 'action-edit';
      case 'delete':
        return 'action-delete';
      default:
        return '';
    }
  }

  getActionLabel(action: string): string {
    switch (action) {
      case 'edit':
        return 'Modifier';
      case 'delete':
        return 'Supprimer';
      default:
        return '';
    }
  }

  private showSuccess(message: string): void {
    this.toast.success(message, 'Succès', 3000, false);
  }

  private showError(message: string): void {
    this.toast.warning(message, 'Erreur', 5000, false);
  }
}