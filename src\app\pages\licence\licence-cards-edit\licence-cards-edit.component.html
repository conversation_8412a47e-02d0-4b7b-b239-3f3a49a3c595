<ngx-loading 
  [show]="isLoading" 
  [config]="{ 
    animationType: 'threeBounce', 
    backdropBackgroundColour: 'rgba(0,0,0,0.2)', 
    primaryColour: '#10b981',
    secondaryColour: '#10b981',
    tertiaryColour: '#10b981'
  }"></ngx-loading>

<div class="licence-cards-container">
  <!-- Content wrapper for consistent alignment -->
  <div class="content-wrapper">
    <!-- Header section -->
    <div class="header improved-header" style="background: #fff;">
      <h1 class="title improved-title" style="background-color: white;">
        {{ subscriptionIdFromRoute ? 'Modifier l\'abonnement' : 'Modifier l\'abonnement pour un client' }}
      </h1>
      <p class="subtitle improved-subtitle">
        {{ subscriptionIdFromRoute ? 'Modifiez les options de cet abonnement.' : 'Choisir des nouveau options ou mettre à niveau l\'abonnement pour le client sélectionné.' }}
      </p>
    </div>

    <!-- Client billing section -->
    <div class="client-billing-section">
      <div class="search-container">    
        <div class="search-input-container" *ngIf="selectedClient || subscriptionIdFromRoute" 
             style="display: flex; align-items: center; justify-content: center;">
          <div class="selected-client-container improved-client-card" @growIn>
            <div class="client-avatar-section">
            </div>
            <div class="client-details-section">
              <div class="client-name-row">
                <span class="client-name-large">{{ selectedClient?.Name }}</span>
              </div>
              <div class="client-subtitle">
                {{ subscriptionIdFromRoute ? 'Abonnement en cours de modification' : 'Gérez les licences et options pour ce client' }}
              </div>
            </div>
          </div>
        </div>

        <!-- <div class="select-type-frequence-payement improved-select-type-frequence-payement">
          <div class="frequence-payement improved-frequence-payement improved-frequence-inline">
            <label for="payment-frequency" style="font-weight: 600; color: #374151; font-size: 1.1rem; font-family: 'Lato', sans-serif;">
              Fréquence de paiement:
            </label>
            <select 
              id="payment-frequency"
              class="improved-select" 
              [(ngModel)]="selectedPaymentFrequency" 
              disabled
            >
              <option *ngFor="let freq of paymentFrequencies" [value]="freq.value">
                {{ freq.label }}
              </option>
            </select>
          </div>
        </div> -->
      </div>
    </div>

    <!-- MAIN GRID LAYOUT - ALL CARDS IN RESPONSIVE GRID -->
    <div class="license-grid improved-license-grid" *ngIf="clientSubscription">
      <ng-container *ngFor="let licence of licences; let i = index">
        <div 
          class="license-card improved-license-card"
          [style.animation-delay]="i * 0.1 + 's'"
        >
          <div class="card-content-wrapper">
            <!-- Card Header -->
            <div class="card-header improved-card-header">
              <h3 class="improved-title">{{ licence.Name }}</h3>
              <div class="card-description-wrapper">
                <p class="card-description improved-card-description" [title]="licence.Description">
                  {{ licence.Description || 'Aucune description disponible pour cette licence.' }}
                </p>
              </div>
            </div>

            <!-- Features List -->
            <div class="features-list-wrapper">
              <ul class="features-list improved-features-list">
                <li *ngFor="let option of getOptionsForLicence(licence); let j = index">
                  <div class="feature-item" [style.animation-delay]="(i * 0.1 + j * 0.05) + 's'">
                    <label class="custom-checkbox">
                      <input 
                        type="checkbox"
                        [checked]="isOptionChecked(licence, option.Id)"
                        (change)="toggleOption(licence, option.Id, $event)"
                        [disabled]="!selectedClient"
                      />
                      <span class="checkmark" [ngClass]="{
                        'checked-green': isOptionChecked(licence, option.Id),
                        'unchecked-red': !isOptionChecked(licence, option.Id)
                      }">
                        <span 
                          class="material-icons verified-icon"
                          *ngIf="isOptionChecked(licence, option.Id)"
                          style="color: var(--green-main); opacity: 1; transform: scale(1);"
                        >check_circle</span>
                        <span 
                          class="material-icons cancel-icon"
                          *ngIf="!isOptionChecked(licence, option.Id)"
                          style="color: var(--danger); opacity: 1; transform: scale(1);"
                        >cancel</span>
                      </span>
                    </label>
                    <div class="feature-details">
                      <span class="feature-name">{{ option.Name }}</span>
                    </div>
                  </div>
                </li>
              </ul>
            </div>

            <!-- License Actions -->
            <div class="license-actions">
              <button 
                *ngIf="isLicenceAssignedToClient(licence) && hasOptionChanges(licence) && !isSubscriptionCancelled(licence)"
                class="select-btn"
                style="background: linear-gradient(135deg, #49b38e, #2c7744);"
                (click)="openModifyPopup(licence)">
                <span class="material-icons" style="margin-right: 0.5rem; font-size: 1.2rem;">save</span>
                Sauvegarder les modifications
              </button>

              <button
                *ngIf="!isLicenceAssignedToClient(licence) && !isSubscriptionCancelled(licence)"
                class="select-btn"
                style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);"
                (click)="upgradeLicenceForClient(licence)">
                <span class="material-icons" style="margin-right: 0.5rem; font-size: 1.2rem;">upgrade</span>
                Mettre à niveau
              </button>

              <button
                *ngIf="isSubscriptionCancelled(licence)"
                class="select-btn"
                disabled>
                <span class="material-icons" style="margin-right: 0.5rem; font-size: 1.2rem;">block</span>
                Abonnement résilié
              </button>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</div>

<!-- Upgrade Confirmation Popup -->
<div class="overlay" *ngIf="showUpgradePopup" @fadeInOut>
  <div class="confirmation-popup" @slideDown>
    <div class="popup-header">
      <h3>
        <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem; color: #3b82f6;">upgrade</span>
        Confirmer la mise à niveau de la licence
      </h3>
      <button class="close-popup-btn" (click)="cancelUpgradePopup()" [disabled]="isUpgrading">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="popup-content">
      <div class="confirmation-message">
        <span class="material-icons confirmation-icon" style="color: #3b82f6;">upgrade</span>
        <p>
          Êtes-vous sûr de vouloir mettre à niveau la licence <strong>{{ selectedLicenseForConfirmation?.Name }}</strong> pour le client
          <strong>{{ selectedClient?.Name }}</strong> ?
        </p>
      </div>
      <div class="client-license-summary">
        <div class="summary-item">
          <span class="label">Client :</span>
          <div class="client-info-summary">
            <span>{{ selectedClient?.Name }}</span>
          </div>
        </div>
        <div class="summary-item">
          <span class="label">Ancienne licence :</span>
          <span class="license-name">{{ oldLicence?.Name }}</span>
        </div>
        <div class="summary-item">
          <span class="label">Anciennes options :</span>
          <div class="chosen-options-list">
            <span *ngFor="let option of oldOptions" 
                  class="option-tag"
                  style="
                    display: inline-block;
                    background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
                    color: #374151;
                    border-radius: 12px;
                    padding: 6px 12px;
                    font-size: 0.9rem;
                    font-weight: 600;
                    border: 1px solid #d1d5db;
                  ">
              {{ option.Name }}
            </span>
          </div>
        </div>
        <div class="summary-item">
          <span class="label">Nouvelle licence :</span>
          <span class="license-name">{{ selectedLicenseForConfirmation?.Name }}</span>
        </div>
        <div class="summary-item">
          <span class="label">Nouvelles options :</span>
          <div class="chosen-options-list">
            <span *ngFor="let option of getNewOptionsForUpgrade()" 
                  class="option-tag"
                  style="
                    display: inline-block;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    border-radius: 12px;
                    padding: 6px 12px;
                    font-size: 0.9rem;
                    font-weight: 600;
                    border: 1px solid #059669;
                    box-shadow: 0 2px 8px rgba(16,185,129,0.2);
                  ">
              {{ option.Name }}
            </span>
          </div>
        </div>
        <div class="summary-item">
          <span class="label">Ancien total:</span>
          <span class="license-total">{{ proratedOldForUpgrade | number:'1.0-2' }} €</span>
        </div>
        <div class="summary-item" *ngIf="proratedDiferencePay > 0">
          <span class="label">Prix à payer:</span>
          <span class="license-total" style="color: #3b82f6;">{{ proratedDiferencePay | number:'1.0-2' }} €</span>
        </div>
        <div class="summary-item" *ngIf="proratedDiferenceReturn > 0">
          <span class="label">Prix à rembourser:</span>
          <span class="license-total" style="color: #f59e0b;">- {{ proratedDiferenceReturn | number:'1.0-2' }} €</span>
        </div>
        <div class="summary-item">
          <span class="label">Nouveau prix :</span>
          <span class="license-total">{{ proratedTotalForUpgrade | number:'1.0-2' }} €</span>
        </div>
      </div>
    </div>
    <div class="popup-actions">
      <button class="cancel-btn" (click)="cancelUpgradePopup()" [disabled]="isUpgrading">
        <span class="material-icons">close</span>
        Annuler
      </button>
      <button class="confirm-btn" (click)="confirmUpgradeLicence()" [disabled]="isUpgrading">
        <span 
          *ngIf="isUpgrading" 
          class="material-icons spinning-icon">
          refresh
        </span>
        <span 
          *ngIf="!isUpgrading" 
          class="material-icons">
          upgrade
        </span>
        {{ isUpgrading ? 'Mise à niveau en cours...' : 'Confirmer' }}
      </button>
    </div>
  </div>
</div>

<!-- Modify Confirmation Popup -->
<div class="overlay" *ngIf="showModifyPopup" @fadeInOut>
  <div class="confirmation-popup" @slideDown>
    <div class="popup-header">
      <h3>
        <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem; color: #10b981;">edit</span>
        Confirmer la modification de la licence
      </h3>
      <button class="close-popup-btn" (click)="cancelModifyLicence()" [disabled]="isModifying">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="popup-content">
      <div class="confirmation-message">
        <span class="material-icons confirmation-icon" style="color: #10b981;">edit</span>
        <p>
          Êtes-vous sûr de vouloir modifier les options de la licence
          <strong>{{ modifyingLicence?.Name }}</strong>
          pour le client
          <strong>{{ selectedClient?.Name }}</strong> ?
        </p>
      </div>
      <div class="client-license-summary">
        <div class="summary-item">
          <span class="label">Client :</span>
          <div class="client-info-summary">
            <span>{{ selectedClient?.Name }}</span>
          </div>
        </div>
        <div class="summary-item">
          <span class="label">Licence :</span>
          <span class="license-name">{{ modifyingLicence?.Name }}</span>
        </div>
        <div class="summary-item">
          <span class="label">Anciennes options :</span>
          <div class="chosen-options-list">
            <span *ngFor="let option of getOldOptionsForModifying()" 
                  class="option-tag"
                  style="
                    display: inline-block;
                    background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
                    color: #374151;
                    border-radius: 12px;
                    padding: 6px 12px;
                    font-size: 0.9rem;
                    font-weight: 600;
                    border: 1px solid #d1d5db;
                  ">
              {{ option.Name }}
            </span>
          </div>
        </div>
        <div class="summary-item">
          <span class="label">Nouvelles options :</span>
          <div class="chosen-options-list">
            <span *ngFor="let option of getNewOptionsForModifying()" 
                  class="option-tag"
                  style="
                    display: inline-block;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    border-radius: 12px;
                    padding: 6px 12px;
                    font-size: 0.9rem;
                    font-weight: 600;
                    border: 1px solid #059669;
                    box-shadow: 0 2px 8px rgba(16,185,129,0.2);
                  ">
              {{ option.Name }}
            </span>
          </div>
        </div>
        <div class="summary-item">
          <span class="label">Ancien total :</span>
          <span class="license-total">{{ getOldTotalForModifying() | number:'1.0-2' }} €</span>
        </div>
        <div class="summary-item" *ngIf="getModificationType() === 'add'">
          <span class="label">Prix à payer :</span>
          <span class="license-total" style="color: #3b82f6;">{{ getPriceToPayForModification() | number:'1.0-2' }} €</span>
        </div>
        <div class="summary-item" *ngIf="getModificationType() === 'remove'">
          <span class="label">Prix à rembourser :</span>
          <span class="license-total" style="color: #f59e0b;">- {{ getPriceToRefundForModification() | number:'1.0-2' }} €</span>
        </div>
        <div class="summary-item" *ngIf="getModificationType() === 'both'">
          <span class="label">Prix à payer :</span>
          <span class="license-total" style="color: #3b82f6;">{{ getPriceToPayForModification() | number:'1.0-2' }} €</span>
        </div>
        <div class="summary-item" *ngIf="getModificationType() === 'both'">
          <span class="label">Prix à rembourser :</span>
          <span class="license-total" style="color: #f59e0b;">- {{ getPriceToRefundForModification() | number:'1.0-2' }} €</span>
        </div>
        <div class="summary-item">
          <span class="label">Nouveau total :</span>
          <span class="license-total">{{ getNewTotalWithOldForModifying() | number:'1.0-2' }} €</span>
        </div>
      </div>
    </div>
    <div class="popup-actions">
      <button class="cancel-btn" (click)="cancelModifyLicence()" [disabled]="isModifying">
        <span class="material-icons">close</span>
        Annuler
      </button>
      <button class="confirm-btn" (click)="confirmSaveModifiedLicenceOptions()" [disabled]="isModifying">
        <span 
          *ngIf="isModifying" 
          class="material-icons spinning-icon">
          refresh
        </span>
        <span 
          *ngIf="!isModifying" 
          class="material-icons">
          save
        </span>
        {{ isModifying ? 'Sauvegarde en cours...' : 'Confirmer' }}
      </button>
    </div>
  </div>
</div>

<!-- Save or Discard Changes Popup -->
<div class="overlay" *ngIf="showSaveOrDiscardPopup" @fadeInOut>
  <div class="confirmation-popup" @slideDown>
    <div class="popup-header">
      <h3>
        <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem; color: #f59e0b;">warning</span>
        Modifications non sauvegardées
      </h3>
      <button class="close-popup-btn" (click)="showSaveOrDiscardPopup = false">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="popup-content">
      <div class="confirmation-message">
        <span class="material-icons confirmation-icon">warning</span>
        <p>
          Vous avez des modifications non sauvegardées pour la licence
          <strong>{{ licenceWithUnsavedChanges?.Name }}</strong>.
          Voulez-vous sauvegarder les modifications avant de désélectionner le client ?
        </p>
      </div>
    </div>
    <div class="popup-actions">
      <button class="cancel-btn" (click)="discardChangesAndClearSelection()">
        <span class="material-icons">delete_outline</span>
        Ne pas sauvegarder
      </button>
      <button class="confirm-btn" (click)="confirmSaveAndClearSelection()">
        <span class="material-icons">save</span>
        Sauvegarder et continuer
      </button>
    </div>
  </div>
</div>

<!-- No Option Checked Popup -->
<div class="overlay" *ngIf="showNoOptionCheckedPopup" @fadeInOut>
  <div class="confirmation-popup" @slideDown>
    <div class="popup-header">
      <h3>
        <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem; color: #ef4444;">error_outline</span>
        Attention
      </h3>
      <button class="close-popup-btn" (click)="closeNoOptionCheckedPopup()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="popup-content">
      <div class="confirmation-message">
        <span class="material-icons confirmation-icon" style="color:#ef4444;">error_outline</span>
        <p>Vous devez cocher au moins une option pour continuer.</p>
      </div>
    </div>
    <div class="popup-actions">
      <button class="confirm-btn" (click)="closeNoOptionCheckedPopup()">
        <span class="material-icons">check</span>
        Compris
      </button>
    </div>
  </div>
</div>

<!-- Success Notification -->
<div class="success-notification" *ngIf="showSaveNotification" @slideDown>
  <div class="notification-content"> 
    <span class="material-icons success-icon">check_circle</span>
    <div class="notification-text">
      <h4>Options sauvegardées avec succès !</h4>
      <p>Les modifications ont été enregistrées et appliquées à l'abonnement.</p>
    </div>
    <button class="close-notification-btn" (click)="showSaveNotification = false">
      <span class="material-icons">close</span>
    </button>
  </div>
</div>