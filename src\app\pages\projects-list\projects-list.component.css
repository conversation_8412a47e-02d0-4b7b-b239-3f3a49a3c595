/* Frontend: src/app/pages/projects-list/projects-list.component.css */

.container {
  max-width: 100%;
  margin: 20px auto;
  margin-left: 50px;
  padding: 20px;
  background: var(--container-bg);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  animation: cardEntrance 0.5s ease-out;
}

.page-title {
  font-size: 28px;
  font-weight: 500;
  color: var(--green-main);
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Animation */
@keyframes cardEntrance {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.actions {
  display: flex;
  gap: 10px;
}

.btn-add {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: var(--primary);
}

.view-toggle {
  color: #000000;
}

/* Updated Project Cards View */
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: 28px;
  padding: 1.5rem;
}

.project-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 0;
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  animation: cardEntrance 0.5s ease-out;
  animation-fill-mode: backwards;
  transform-origin: center bottom;
  border: none;

}

.project-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 30px rgba(0, 0, 0, 0.1);
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: var(--green-main);
}

/* Card Label-Based Color Schemes */
.project-card[data-label="audit"]::before {
  background: var(--audit);
}

.project-card[data-label="installation"]::before {
  background: var(--installation);
}

.project-card[data-label="maintenance"]::before {
  background: var(--maintenance);
}

.project-card[data-label="planification"]::before {
  background: var(--planification);
}

/* Card Header Styles */
mat-card-header {
  padding: 20px 20px 0;
  margin-bottom: 0;
  position: relative;
}

mat-card-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--card-description);
  margin-bottom: 4px;
  letter-spacing: -0.01em;
  transition: color 0.3s ease;
}

mat-card-subtitle {
  display: inline-flex; /* Changed from inline-block */
  justify-content: center; /* Add horizontal centering */
  align-items: center;    /* Add vertical centering */
  padding: 0px 10px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: 8px;
  color: white;
  width: max-content; /* Ensures pill-shaped background fits content */
  margin-left: auto;  /* Center horizontally in parent */
  margin-right: auto;
}

/* Specific label colors */
.project-card[data-label="audit"] mat-card-subtitle {
  background-color: var(--audit);
}

.project-card[data-label="installation"] mat-card-subtitle {
  background-color: var(--installation);
}

.project-card[data-label="maintenance"] mat-card-subtitle {
  background-color: var(--maintenance);
  color: #212121;
}

.project-card[data-label="planification"] mat-card-subtitle {
  background-color: var(--planification);
}

/* Card Content Styles */
mat-card-content {
  padding: 0 20px 20px;
  margin-top: 16px;
}

.card-description {
  margin-bottom: 20px;
  color: var(--card-description);
  line-height: 1.5;
  font-size: 0.95rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

.card-detail {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 0.9rem;
  color: var(--card-description);
}

.card-detail mat-icon {
  margin-right: 8px;
  font-size: 18px;
  width: 18px;
  height: 18px;
  color: var(--card-detail-text);
}

button[mat-raised-button] {
  background-color: var(--green-main);
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 3px 1px -2px rgba(0,0,0,.2), 0 2px 2px 0 rgba(0,0,0,.14), 0 1px 5px 0 rgba(0,0,0,.12);
  z-index: 0;
}

.card-detail strong {
  font-weight: 500;
  color: var(--card-detail-label);
  margin-right: 4px;
}

/* Progress Bar Styles */
.progress-container {
  margin-top: 20px;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-title {
  font-weight: 500;
  color: #424242;
  font-size: 0.9rem;
}

.progress-text {
  font-weight: 700;
  font-size: 0.9rem;
}

/* Color the progress percentage based on value */
.progress-low {
  color: var(--danger);
}

.progress-medium {
  color: var(--warning);
}

.progress-high {
  color: var(--green-main);
}

mat-progress-bar {
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
}

/* Angular Material 15+ progress bar styling */
:host ::ng-deep .progress-low .mdc-linear-progress__bar-inner {
  border-color: var(--danger) !important;
  background-color: var(--danger) !important;
}

:host ::ng-deep .progress-medium .mdc-linear-progress__bar-inner {
  border-color: var(--warning) !important;
  background-color: var(--warning) !important;
}

:host ::ng-deep .progress-high .mdc-linear-progress__bar-inner {
  border-color: var(--green-main) !important;
  background-color: var(--green-main) !important;
}

/* For older Angular Material versions */
:host ::ng-deep .progress-low .mat-progress-bar-fill::after {
  background-color: var(--danger) !important;
}

:host ::ng-deep .progress-medium .mat-progress-bar-fill::after {
  background-color: var(--warning) !important;
}

:host ::ng-deep .progress-high .mat-progress-bar-fill::after {
  background-color: var(--green-main) !important;
}

/* Ensure the buffer color is consistent */
:host ::ng-deep .mat-progress-bar-buffer {
  background: #e0e0e0;
}

/* Members section */
.members-container {
  margin-top: 16px;
  display: flex;
  align-items: center;
}

.members-title {
  font-weight: 500;
  color: var(--card-description);
  margin-right: 8px;
  font-size: 0.9rem;
}

.members-avatars {
  display: flex;
  align-items: center;
}

.member-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: var(--secondary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 500;
  border: 2px solid white;
  transition: transform 0.2s ease;
  margin: auto;
}

.member-avatar:hover {
  transform: translateY(-4px);
  z-index: 2;
}

.member-more {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #616161;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 500;
  border: 2px solid white;
  margin: auto;
}

/* Card Actions */
.card-actions {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-top: 1px solid #eeeeee;
}

.action-button {
  margin-left: 8px;
}

/* Menu Button Styles */
.menu-button {
  position: absolute;
  top: 12px;
  right: 12px;
  color: #757575;
  transition: all 0.2s ease;
}

.menu-button:hover {
  background-color: rgba(0, 0, 0, 0.08);
  transform: rotate(90deg);
}

/* Animations */
@keyframes cardEntrance {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Enterprise card style variant */
.enterprise-card {
  background-color: #f8faff;
}

.enterprise-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #e3f2fd 50%, transparent 50%);
}

/* Responsive design */
@media (max-width: 768px) {
  .cards-container {
    grid-template-columns: 1fr;
    padding: 1rem 0.75rem;
  }
}
/*---------------------------------------------------------------------------------------------------------------*/ 
/*-------------------------------------------------- Dark Mode --------------------------------------------------*/ 
/*---------------------------------------------------------------------------------------------------------------*/ 

.container {
  max-width: 100%;
  margin: 20px auto;
  margin-left: 50px;
  padding: 20px;
  background: #fff;
  background: var(--container-bg, #fff);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  box-shadow: var(--card-shadow, 0 2px 10px rgba(0, 0, 0, 0.1));
  animation: cardEntrance 0.5s ease-out;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.page-title {
  font-size: 28px;
  font-weight: 500;
  color: var(--green-main);
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Animation */
@keyframes cardEntrance {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.actions {
  display: flex;
  gap: 10px;
}

.btn-add {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: var(--primary);
}

.view-toggle {
  color: #000000;
  color: var(--text-primary, #000000);
  transition: color 0.3s ease;
}

/* Updated Project Cards View */
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: 28px;
  padding: 1.5rem;
}

.project-card {
  background: #ffffff;
  background: var(--card-bg, #ffffff);
  border-radius: 12px;
  padding: 0;
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  box-shadow: var(--card-shadow, 0 4px 12px rgba(0, 0, 0, 0.05));
  position: relative;
  overflow: hidden;
  animation: cardEntrance 0.5s ease-out;
  animation-fill-mode: backwards;
  transform-origin: center bottom;
  border: none;
}

.project-card:hover {
  transform: translateY(-8px);
  box-shadow: 0;
}