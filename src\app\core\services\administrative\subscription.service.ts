// subscription.service.ts - Enhanced
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiService } from '../api.service';
import { Subscription } from '@app/core/models/subscription';
import { Observable } from 'rxjs';
import { Lister, Page } from '@app/core/models/util/page';
import { environment } from '@app/environments/environment';

@Injectable({ providedIn: 'root' })
export class SubscriptionApiService extends ApiService<Subscription> {
  private apiUrl = `${environment.host}/api/subscription`;
  
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('subscription');
  }

  // Enhanced getCounts method
  getCounts(): Observable<{ TotalAbonnements: number; EnAttente: number; Payes: number; Resilies: number }> {
    return this.http.get<{ TotalAbonnements: number; EnAttente: number; Payes: number; Resilies: number }>(
      `${this.apiUrl}/dashboard/counts`,
    );
  }

  // NEW: Get filter options from the database
  getFilterOptions(): Observable<{
    statuses: string[];
    licences: string[];
    frequencies: string[];
  }> {
    return this.http.get<{
      statuses: string[];
      licences: string[];
      frequencies: string[];
    }>(`${this.apiUrl}/filter-options`);
  }

  renewSubscription(
    id: string,
    type: 'monthly' | 'yearly'
  ): Observable<{ subscription: Subscription; facture: any }> {
    return this.http.post<{ subscription: Subscription; facture: any }>(
      `${this.apiUrl}/renew/${id}`,
      type
    );
  }
}
