/* IoT Rules Generator - Complete CSS without Tailwind */

/* ===== DIALOG STRUCTURE ===== */
.dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 85vh;
  width: 100%;
  max-width: none;
  min-width: auto;
  position: relative;
  overflow: hidden;
}

.dialog-header {
  flex-shrink: 0;
  padding: 16px 16px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  background: var(--surface);
  z-index: 10;
  border-bottom: 1px solid var(--card-border);
}

.dialog-content {
  flex-grow: 1;
  overflow-y: auto;
  padding: 16px;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.dialog-content::-webkit-scrollbar {
  display: none;
}

.dialog-footer {
  flex-shrink: 0;
  padding: 16px;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  position: sticky;
  bottom: 0;
  background: var(--surface);
  border-top: 1px solid var(--card-border);
}

/* ===== TYPOGRAPHY ===== */
.main-title {
  color: var(--primary);
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0;
}

.main-title-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: var(--primary);
}

.section-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.section-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--primary);
}

.json-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.125rem;
  margin-bottom: 0.75rem;
}

/* ===== FORM ELEMENTS ===== */
.form-field {
  position: relative;
  margin-bottom: 0.75rem;
}

.field-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  line-height: 1.25;
}

.field-label.small {
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.text-input {
  width: 100%;
  height: 42px;
  padding: 0.75rem;
  background: var(--surface);
  border: 1px solid var(--card-border);
  border-radius: 6px;
  color: var(--text-primary);
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.2s ease;
  box-sizing: border-box;
  outline: none;
}

.text-input.small {
  height: 36px;
  padding: 0.5rem;
  font-size: 0.75rem;
}

.text-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px var(--highlight-color);
}

.text-input:hover:not(:focus) {
  border-color: var(--primary-light);
}

.text-input::placeholder {
  color: var(--grey-light);
  opacity: 0.7;
}

.text-input.invalid {
  border-color: var(--danger);
}

.text-input.invalid:focus {
  border-color: var(--danger);
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.text-input.disabled {
  background: var(--beige-light);
  color: var(--grey-light);
  border-color: var(--card-border);
  cursor: not-allowed;
  opacity: 0.6;
}

.number-input {
  width: 100%;
  height: 42px;
  padding: 0.75rem;
  background: var(--surface);
  border: 1px solid var(--card-border);
  border-radius: 6px;
  color: var(--text-primary);
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.2s ease;
  box-sizing: border-box;
  outline: none;
}

.number-input.small {
  height: 36px;
  padding: 0.5rem;
  font-size: 0.75rem;
}

.number-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px var(--highlight-color);
}

.number-input.invalid {
  border-color: var(--danger);
}

.time-input {
  width: 100%;
  height: 42px;
  padding: 0.75rem;
  background: var(--surface);
  border: 1px solid var(--card-border);
  border-radius: 6px;
  color: var(--text-primary);
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.2s ease;
  box-sizing: border-box;
  outline: none;
}

.time-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px var(--highlight-color);
}

.select-input {
  appearance: none;
  width: 100%;
  height: 42px;
  padding: 0.75rem 2.5rem 0.75rem 0.75rem;
  background: var(--surface);
  border: 1px solid var(--card-border);
  border-radius: 6px;
  color: var(--text-primary);
  font-size: 0.875rem;
  line-height: 1.2;
  font-weight: 500;
  transition: all 0.2s ease;
  box-sizing: border-box;
  outline: none;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23047857' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.125em 1.125em;
}

.select-input.small {
  height: 36px;
  padding: 0.5rem 2rem 0.5rem 0.5rem;
  font-size: 0.75rem;
  background-size: 1em 1em;
  background-position: right 0.5rem center;
}

.select-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px var(--highlight-color);
}

.select-input:hover:not(:focus) {
  border-color: var(--primary-light);
}

.select-input option {
  background: var(--surface);
  color: var(--text-primary);
  padding: 0.625rem 0.75rem;
  font-size: 0.8125rem;
  line-height: 1.3;
  font-weight: 500;
}

/* ===== LAYOUT GRIDS ===== */
.config-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.config-section {
  margin-bottom: 1.5rem;
}

.condition-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.time-condition-grid {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 0.75rem;
  align-items: end;
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 0.75rem;
  align-items: end;
}

.log-action-grid {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 0.75rem;
  align-items: end;
}

/* ===== RESPONSIVE GRIDS ===== */
@media (min-width: 768px) {
  .config-grid {
    grid-template-columns: 1fr 1fr;
  }

  .condition-grid {
    grid-template-columns: 1fr 1fr 1fr 1fr auto;
  }
}

/* ===== CHECKBOX STYLING ===== */
.checkbox-group {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding-top: 1.5rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.checkbox-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

input[type="checkbox"] {
  width: 1.25rem;
  height: 1.25rem;
  background: var(--surface);
  border: 2px solid var(--card-border);
  border-radius: 0.25rem;
  appearance: none;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

input[type="checkbox"]:checked {
  background: var(--primary);
  border-color: var(--primary);
}

input[type="checkbox"]:checked::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--white);
  font-weight: bold;
  font-size: 0.875rem;
}

/* ===== SECTIONS ===== */
.section {
  margin-bottom: 1.5rem;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.section-actions {
  display: flex;
  gap: 0.5rem;
}

/* ===== TOPICS DISPLAY ===== */
.topics-display {
  padding: 0.5rem;
  border: 1px solid var(--card-border);
  border-radius: 0.375rem;
  background: var(--beige-light);
  min-height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.topics-placeholder {
  color: var(--grey-light);
  font-size: 0.875rem;
}

.topic-item {
  font-size: 0.875rem;
  color: var(--primary);
  margin-bottom: 0.25rem;
}

.topic-item:last-child {
  margin-bottom: 0;
}

/* ===== GLOBAL OPERATOR ===== */
.global-operator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.operator-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.operator-select {
  padding: 0.25rem;
  border: 1px solid var(--card-border);
  border-radius: 0.25rem;
  font-size: 0.875rem;
  width: auto;
  background: var(--surface);
  color: var(--text-primary);
}

.operator-hint {
  font-size: 0.75rem;
  color: var(--grey-light);
}

/* ===== CONDITION GROUPS ===== */
.condition-groups-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.condition-group {
  background: var(--beige-light);
  border: 2px solid var(--primary);
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px var(--box-shadow);
  padding: 1rem;
  margin-bottom: 1rem;
}

.group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.group-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.group-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.group-operator-select {
  padding: 0.25rem;
  border: 1px solid var(--card-border);
  border-radius: 0.25rem;
  font-size: 0.875rem;
  background: var(--surface);
  color: var(--text-primary);
}

.group-actions {
  display: flex;
  gap: 0.5rem;
}

/* ===== CONDITIONS LIST ===== */
.conditions-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.condition-item {
  background: var(--surface);
  border: 1px solid var(--card-border);
  border-radius: 0.5rem;
  padding: 0.75rem;
  box-shadow: var(--shadow-sm);
  margin-bottom: 0.5rem;
}

.condition-item:hover {
  border-color: var(--primary-light);
}

.condition-actions {
  display: flex;
  align-items: flex-end;
}

/* ===== ACTIONS LIST ===== */
.actions-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.action-item {
  background: var(--beige-light);
  border: 1px solid var(--card-border);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 0.75rem;
}

.action-actions {
  display: flex;
  align-items: flex-end;
}

.payload-section {
  margin-top: 0.5rem;
}

/* ===== VALUE INPUT CONTAINER ===== */
.value-input-container {
  position: relative;
}

/* ===== VALIDATION INDICATORS ===== */
.validation-error {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--danger);
}

.validation-success {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--success);
}

.validation-icon {
  width: 0.75rem;
  height: 0.75rem;
  flex-shrink: 0;
}

.type-indicator {
  font-size: 0.75rem;
  color: var(--grey-light);
  margin-top: 0.25rem;
}

/* ===== DRAG & DROP ===== */
.drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-bottom: 0.5rem;
  margin-left: auto;
  margin-right: auto;
  cursor: grab;
  color: var(--grey-light);
  border-radius: 0.25rem;
  transition: all 0.2s ease;
  background: transparent;
  border: 1px dashed var(--card-border);
}

.drag-handle:hover {
  background: var(--beige-light);
  color: var(--primary);
  border-color: var(--primary);
}

.drag-handle:active,
.cdk-drag-preview .drag-handle {
  cursor: grabbing;
  background: var(--primary);
  color: var(--white);
  border-color: var(--primary);
}

.drag-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--grey-light);
}

.drag-icon.small {
  width: 1rem;
  height: 1rem;
}

/* ===== BUTTONS ===== */
.add-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: background-color 0.2s ease;
  border: none;
  cursor: pointer;
}

.add-button.small {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
}

.add-button:hover {
  background: linear-gradient(45deg, #81c784, var(--primary)) !important;
}

.delete-button {
  color: var(--danger);
  background: transparent;
  border: none;
  padding: 0.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.delete-button:hover {
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
}

.save-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: var(--white) !important;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  min-width: 100px;
  justify-content: center;
}

.save-button:hover:not(:disabled) {
  background: linear-gradient(45deg, #81c784, var(--primary)) !important;
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cancel-button {
  background: var(--beige-light) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--card-border) !important;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  min-width: 100px;
  justify-content: center;
}

.cancel-button:hover {
  background: var(--card-border) !important;
}

.download-button {
  background: linear-gradient(45deg, var(--audit), #01579e) !important;
  color: var(--white) !important;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  min-width: 100px;
  justify-content: center;
}

.download-button:hover {
  background: linear-gradient(45deg, #01579e, var(--audit)) !important;
}

.retry-button {
  background: var(--danger);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retry-button:hover {
  background: #dc2626;
}

.button-icon {
  width: 1rem;
  height: 1rem;
}

.button-icon.small {
  width: 0.75rem;
  height: 0.75rem;
}

/* ===== ERROR BANNER ===== */
.error-banner {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.error-banner-content {
  display: flex;
  align-items: center;
}

.error-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #f87171;
  margin-right: 0.5rem;
  flex-shrink: 0;
}

.error-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #991b1b;
  margin: 0;
}

.error-message {
  font-size: 0.875rem;
  color: #b91c1c;
  margin: 0.25rem 0 0 0;
}

.error-actions {
  margin-top: 0.75rem;
}

/* ===== JSON PREVIEW ===== */
.json-section {
  margin-top: 1.5rem;
}

.json-preview {
  background: #1a1a1a;
  color: #22c55e;
  padding: 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  overflow-x: auto;
  max-height: 12rem;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  line-height: 1.6;
  position: relative;
  border: 1px solid var(--card-border);
  margin: 0;
}

.json-preview::before {
  content: "JSON";
  position: absolute;
  top: 0.5rem;
  right: 0.75rem;
  background: var(--primary);
  color: var(--white);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: bold;
}

/* ===== BUSY OVERLAY ===== */
.busy-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(2px);
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
}

.busy-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.spinner {
  width: 3rem;
  height: 3rem;
  border: 4px solid var(--primary);
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.busy-message {
  color: var(--primary);
  font-weight: 600;
  font-size: 1.125rem;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--beige-light);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--success));
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* ===== UTILITY CLASSES ===== */
.pointer-events-none {
  pointer-events: none;
}

/* ===== ANIMATIONS ===== */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .dialog-header {
    padding: 12px;
  }

  .dialog-content {
    padding: 12px;
  }

  .dialog-footer {
    padding: 12px;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .main-title {
    font-size: 1.5rem;
  }

  .config-grid {
    gap: 0.75rem;
  }

  .condition-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .time-condition-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .action-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .log-action-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .group-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .group-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .section-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .checkbox-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    padding-top: 1rem;
  }

  .global-operator {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .condition-group {
    padding: 0.75rem;
  }

  .action-item {
    padding: 0.75rem;
  }

  .text-input,
  .select-input,
  .number-input,
  .time-input {
    height: 38px;
    font-size: 0.75rem;
  }

  .text-input.small,
  .select-input.small,
  .number-input.small {
    height: 34px;
    font-size: 0.6875rem;
  }

  .busy-message {
    font-size: 1rem;
  }

  .spinner {
    width: 2.5rem;
    height: 2.5rem;
  }
}

@media (max-width: 480px) {
  .dialog-footer {
    flex-direction: column;
  }

  .save-button,
  .cancel-button,
  .download-button {
    width: 100%;
    min-width: auto;
  }
}
