<div class="overlay"></div>

<div class="timeline-popup chat-style" >
  <div class="popup-header">
    <h3>Fiche de vie ( {{name}} )</h3>
    <button class="close-btn" (click)="closeTimeline()">✖</button>
  </div>

  <div class="chat-body" #chatBody>
    <div *ngFor="let entry of displayData" class="chat-message" [ngClass]="entry.status">
      <div class="bubble">
        <div class="token">{{ entry.status }}</div>
        <div class="message-time">{{ entry.start }} - {{ entry.end }}</div>
        <div class="message-log">{{ entry.log ? '' : 'Aucun journal pour cette période.' }}</div>

        <div class="sensor-grid" *ngIf="entry.sensorData">
          <ng-container *ngFor="let sensor of getSensorArray(entry.sensorData)">
            <div class="sensor-item">
              <mat-icon class="sensor-icon">{{ sensor.icon }}</mat-icon>
              <div class="sensor-info">
                <span class="sensor-label">{{ sensor.label }}</span>
                <span class="sensor-value" [ngClass]="sensor.type === 'boolean' ? 'boolean-' + sensor.value : ''">
                  {{ sensor.value }} <span *ngIf="sensor.unit">{{ sensor.unit }}</span>
                </span>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>
