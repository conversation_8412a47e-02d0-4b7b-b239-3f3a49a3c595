
/* ===== CONTAINER LAYOUT ===== */
.dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 90vh;
  width: 100%;
  min-height: 70vh;
  overflow: hidden;
}

.dialog-header {
  flex-shrink: 0;
  padding: 1.5rem 2rem 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: var(--surface);
  border-bottom: 1px solid var(--card-border);
  min-height: 80px;
}

.dialog-content {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1rem 1.5rem;
  min-height: 0;
}


/* ===== TYPOGRAPHY ===== */
.header-title {
  color: var(--primary);
  font-size: 1.875rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-right: 3rem;
  line-height: 1.3;
  margin-bottom: 1.5rem;
}

.header-icon {
  width: 2rem;
  height: 2rem;
  color: var(--primary);
}

.step-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.json-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.125rem;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.json-icon {
  font-size: 0.875rem !important;
}

/* ===== STEP PROGRESS SYSTEM ===== */
.step-progress-container {
  margin-bottom: 2rem;
}

.step-progress-bar {
  width: 100%;
  height: 6px;
  background: var(--card-border);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.step-progress-fill {
  height: 100%;
  background: var(--primary);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* ===== STEP INDICATORS ===== */
.step-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  cursor: default;
}

.step-indicator.clickable {
  cursor: pointer;
}

.step-number {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: var(--card-border);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
  transition: var(--transition);
  border: 2px solid transparent;
}

.step-indicator.active .step-number {
  background: var(--primary);
  color: white;
}

.step-indicator.completed .step-number {
  background: var(--success);
  color: white;
}

.step-check {
  font-size: 1.25rem !important;
  width: 1.25rem !important;
  height: 1.25rem !important;
}

.step-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
  text-align: center;
}

.step-indicator.active .step-label,
.step-indicator.completed .step-label {
  color: var(--primary);
  font-weight: 600;
}

/* ===== STEP SECTIONS ===== */
.step-section {
  border-radius: 0.75rem;
  border: 2px solid transparent;
  background: var(--surface);
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.step-section.step-current {
  border-color: var(--primary);
  box-shadow: var(--shadow-md);
}

.step-section.step-completed {
  border-color: var(--success);
}

.step-section.step-disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* ===== STEP HEADERS ===== */
.step-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--card-border);
  margin-bottom: 0;
}

.step-header.clickable {
  cursor: pointer;
}

.step-number-badge {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: var(--card-border);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
  flex-shrink: 0;
  border: 2px solid transparent;
}

.step-number-badge.active {
  background: var(--primary);
  color: white;
}

.step-number-badge.completed {
  background: var(--success);
  color: white;
}

.step-number-badge.disabled {
  opacity: 0.6;
}

.step-check-small {
  font-size: 1rem !important;
  width: 1rem !important;
  height: 1rem !important;
}

.step-progress-indicator {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-weight: 600;
  font-size: 0.75rem;
  border: 1px solid rgba(34, 197, 94, 0.2);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.progress-icon {
  font-size: 0.75rem !important;
}

.progress-text {
  font-size: 0.75rem;
  color: var(--success);
  font-weight: 500;
}

/* ===== STEP CONTENT ===== */
.step-content {
  padding: 1.5rem;
  transition: all 0.3s ease;
  overflow: hidden;
  opacity: 1;
  max-height: none;
}

.step-content.collapsed {
  max-height: 0;
  padding: 0 1.5rem;
  opacity: 0;
  pointer-events: none;
}

.step-content.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* ===== FORM ELEMENTS ===== */
.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.label-with-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 1rem;

}

.label-icon {
  font-size: 0.875rem !important;
}

.form-select {
  width: 100%;
  padding: 0.75rem;
  background: var(--surface);
  border: 2px solid var(--card-border);
  border-radius: 0.5rem;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: var(--transition);
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  background: var(--surface);
  border: 2px solid var(--card-border);
  border-radius: 0.5rem;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: var(--transition);
}

.form-select:focus,
.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(47, 125, 51, 0.1);
}

.form-select:disabled,
.form-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f5f5f5;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 1rem;
}

.readonly-field {
  padding: 0.5rem;
  background: #f9fafb;
  border: 1px solid var(--card-border);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.field-icon {
  font-size: 0.75rem !important;
  color: var(--grey-light);
}

/* ===== CUSTOM CHECKBOX ===== */
.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
}

.custom-checkbox {
  width: 1.25rem;
  height: 1.25rem;
  background: var(--surface);
  border: 2px solid var(--card-border);
  border-radius: 0.25rem;
  appearance: none;
  cursor: pointer;
  position: relative;
  transition: var(--transition);
}

.custom-checkbox:checked {
  background: var(--primary);
  border-color: var(--primary);
}

.custom-checkbox:checked::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 0.875rem;
}

.checkbox-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-icon {
  font-size: 0.875rem !important;
}

.checkbox-section {
  margin-top: 1rem;
  display: flex;
  align-items: center;
}

/* ===== DEVICE MAPPING CARDS ===== */
.device-mapping-card {
  transition: var(--transition);
  border: 2px solid var(--card-border);
  background: var(--surface);
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.device-mapping-card.mapping-completed {
  border-color: var(--success);
  background: rgba(34, 197, 94, 0.02);
}

.mapping-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mapping-success {
  background: rgba(34, 197, 94, 0.05);
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.success-spacing {
  margin-top: 0.5rem;
}

.success-content {
  font-size: 0.875rem;
  color: #15803d;
}

.success-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.success-item-vertical {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.success-icon {
  color: #16a34a;
  font-size: 0.875rem !important;
}

.success-small-icon {
  color: #16a34a;
  font-size: 0.75rem !important;
}

.success-small-icon-top {
  color: #16a34a;
  font-size: 0.75rem !important;
  margin-top: 0.125rem;
}

.property-tags,
.action-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: 0.25rem;
}

.property-tag,
.action-tag {
  display: inline-block;
  background: #dcfce7;
  color: #15803d;
  padding: 0.125rem 0.5rem;
  border-radius: 0.75rem;
  font-size: 0.75rem;
  border: 1px solid #bbf7d0;
}

/* ===== PREVIEW CARDS ===== */
.rule-preview-card {
  background: rgba(59, 130, 246, 0.05);
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 0.75rem;
  padding: 1rem;
}

.preview-spacing {
  margin-top: 1rem;
}

.preview-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.preview-icon {
  font-size: 0.875rem !important;
}

.preview-content {
  font-size: 0.875rem;
  color: var(--text-primary);
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.preview-item-vertical {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.preview-small-icon {
  font-size: 0.75rem !important;
}

.preview-small-icon-top {
  font-size: 0.75rem !important;
  margin-top: 0.125rem;
}

.preview-summary {
  font-size: 0.875rem;
  color: var(--text-primary);
}

.preview-card {
  background: #f9fafb;
  border: 2px solid var(--card-border);
  border-radius: 0.75rem;
  padding: 1rem;
}

.preview-form-container {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
  flex-wrap: wrap;
}

.preview-form-item {
  flex: 1;
  min-width: 250px;
  margin-bottom: 0;
}

.topic-section {
  margin-top: 1rem;
}

.topics-display {
  min-height: 50px;
  padding: 0.5rem;
  border: 1px solid var(--card-border);
  border-radius: 0.5rem;
  background: var(--surface);
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  min-width: 0;
  overflow-x: auto;
}

.no-topics {
  color: var(--grey-light);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.no-topics-icon {
  font-size: 0.75rem !important;
}

.topic-tag {
  display: inline-block;
  background: rgba(34, 197, 94, 0.1);
  color: var(--primary);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  border: 1px solid rgba(34, 197, 94, 0.2);
  font-weight: 500;
  font-size: 0.875rem;
  max-width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  margin-bottom: 0.25rem;
}

.topic-icon {
  font-size: 0.75rem !important;
}

.json-section {
  margin-top: 1rem;
}

.json-preview {
  background: var(--text-primary) !important;
  color: var(--success) !important;
  border: 2px solid var(--card-border);
  border-radius: 0.5rem;
  padding: 1rem;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  line-height: 1.6;
  overflow: auto;
  font-size: 0.875rem;
  max-height: 16rem;
  overflow-x: auto;
  word-break: break-all;
  white-space: pre;
  max-width: 100%;
}

/* ===== SUMMARY CARD ===== */
.summary-section {
  margin-bottom: 1.5rem;
}

.summary-card {
  background: rgba(59, 130, 246, 0.05);
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 0.75rem;
  padding: 1rem;
}

.summary-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.summary-icon {
  color: var(--text-primary);
}

.summary-content {
  font-size: 0.875rem;
  color: var(--text-primary);
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.summary-item-icon {
  font-size: 0.875rem !important;
}

.summary-value {
  font-weight: 500;
}

.summary-progress {
  padding: 0.125rem 0.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 0.75rem;
  display: inline-block;
}

.summary-progress.progress-complete {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
  color: #16a34a;
}

.summary-progress.progress-partial {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.2);
  color: #d97706;
}

.summary-progress.progress-none {
  background: rgba(156, 163, 175, 0.1);
  border: 1px solid rgba(156, 163, 175, 0.2);
  color: var(--grey-light);
}

.rule-transaction-note {
  margin-top: 0.75rem;
  padding: 0.5rem;
  background: #dbeafe;
  border-radius: 0.5rem;
  border: 1px solid #93c5fd;
}

.note-text {
  font-size: 0.75rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.note-icon {
  font-size: 0.75rem !important;
}

/* ===== ERROR AND WARNING CONTAINERS ===== */
.error-container {
  background: rgba(239, 68, 68, 0.05);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.error-content {
  display: flex;
  align-items: center;
}

.error-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #dc2626;
  margin-right: 0.5rem;
}

.error-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #991b1b;
}

.error-message {
  font-size: 0.875rem;
  color: #dc2626;
  margin-top: 0.25rem;
}

.error-actions {
  margin-top: 0.75rem;
}

.error-retry-btn {
  background: #dc2626;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
  transition: var(--transition);
}

.error-retry-btn:hover {
  background: #b91c1c;
}

.warning-container {
  background: rgba(245, 158, 11, 0.05);
  border: 1px solid rgba(245, 158, 11, 0.2);
  border-radius: 0.375rem;
  padding: 1rem;
}

.warning-spacing {
  margin-top: 1rem;
}

.warning-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.warning-icon {
  color: #d97706;
}

.warning-message {
  font-size: 0.875rem;
  color: #92400e;
}

.info-container {
  background: rgba(245, 158, 11, 0.05);
  border: 1px solid rgba(245, 158, 11, 0.2);
  border-radius: 0.375rem;
  padding: 0.75rem;
}

.info-spacing {
  margin-bottom: 1rem;
}

.info-content {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.info-icon {
  color: #d97706;
  margin-top: 0.125rem;
}

.info-text {
  font-size: 0.875rem;
  color: #92400e;
}

/* ===== BUTTONS ===== */
button {
  padding: 0.75rem 1.25rem;
  border-radius: 0.5rem;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--primary);
  color: white;
  min-height: 44px;
}

button:hover:not(:disabled) {
  background: #1b5e20;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.button-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.loading-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.save-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white !important;
  font-weight: 600;
  min-width: 140px;
}

.save-button:hover:not(:disabled) {
  background: linear-gradient(45deg, #81c784, var(--primary)) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.save-button:disabled {
  background: var(--grey-light) !important;
  color: var(--text-primary) !important;
}

.cancel-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white !important;
}

.cancel-button:hover:not(:disabled) {
  background: linear-gradient(45deg, #81c784, var(--primary)) !important;
}

.download-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white !important;
  border-color: var(--info) !important;
}

.download-button:hover:not(:disabled) {
  background: linear-gradient(45deg, #81c784, var(--primary)) !important;
}

.close-button {
  background: transparent !important;
  border: none !important;
  padding: 8px !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  min-width: 40px !important;
  min-height: 40px !important;
  color: var(--grey-light) !important;
}

.close-button:hover:not(:disabled) {
  background: var(--card-border) !important;
  color: var(--danger) !important;
}

/* ===== DIALOG FOOTER ===== */
.dialog-footer {
  flex-shrink: 0;
  padding: 1.25rem 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--surface);
  border-top: 1px solid var(--card-border);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  min-height: 80px;
}

.footer-center {
  display: flex;
  gap: 0.875rem;
  color: var(--primary);
  justify-content: center;
  align-items: center;
  flex: 0 0 auto;
}

/* ===== UTILITY CLASSES ===== */
.pointer-events-none {
  pointer-events: none;
}

.steps-container {
  /* Container for all steps */
}

/* ===== MAPPING PROGRESS ===== */
.mapping-progress {
  padding: 0.125rem 0.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 0.75rem;
  display: inline-block;
}

.mapping-progress.text-green-600 {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
  color: #16a34a;
}

.mapping-progress.text-orange-600 {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.2);
  color: #d97706;
}

.mapping-progress.text-gray-500 {
  background: rgba(156, 163, 175, 0.1);
  border: 1px solid rgba(156, 163, 175, 0.2);
  color: var(--grey-light);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .preview-form-item {
    min-width: 200px;
  }
}

@media (max-width: 1200px) {
  /* Responsive adjustments */
}

@media (max-width: 900px) {
  .step-indicators {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .step-number {
    width: 2.5rem;
    height: 2.5rem;
  }

  .step-label {
    font-size: 0.7rem;
  }

  .dialog-footer {
    padding: 1rem;
  }

  .footer-center {
    gap: 0.5rem;
  }

  .footer-center button {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .dialog-header,
  .dialog-content {
    padding: 0.75rem 1rem;
  }

  .dialog-footer {
    padding: 0.875rem 1rem;
    flex-direction: column;
    gap: 0.75rem;
    min-height: auto;
  }

  .footer-center {
    width: 100%;
    justify-content: space-between;
    gap: 0.5rem;
  }

  .footer-center button {
    flex: 1;
    min-width: 0;
    font-size: 0.75rem;
    padding: 0.75rem 0.5rem;
  }

  .header-title {
    font-size: 1.5rem;
  }

  .step-number {
    width: 2rem;
    height: 2rem;
    font-size: 0.875rem;
  }

  .step-label {
    font-size: 0.65rem;
  }

  .step-content {
    padding: 1rem;
  }

  .device-mapping-card {
    padding: 0.75rem;
  }

  .form-grid {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .preview-form-container {
    flex-direction: column;
    gap: 1.5rem;
  }

  .preview-form-item {
    min-width: auto;
    margin-bottom: 0;
  }
}

@media (max-width: 600px) {
  .dialog-header {
    padding: 1rem;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
    min-height: 60px;
  }

  .dialog-header .header-title {
    font-size: 1.25rem;
    padding-right: 2.5rem;
  }

  .dialog-footer {
    padding: 0.75rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .footer-center {
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
  }

  .footer-center button {
    width: 100%;
    justify-content: center;
  }

  .step-indicators {
    grid-template-columns: repeat(5, 1fr);
    gap: 0.25rem;
  }

  .step-number {
    width: 1.75rem;
    height: 1.75rem;
    font-size: 0.75rem;
  }

  .step-label {
    font-size: 0.6rem;
  }
}

/* ===== FOCUS STYLES FOR ACCESSIBILITY ===== */
button:focus-visible,
input:focus-visible,
select:focus-visible,
.step-indicator:focus-visible,
.step-header.clickable:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {
  .dialog-header,
  .dialog-footer,
  .close-button {
    display: none !important;
  }

  .dialog-content {
    overflow: visible !important;
    height: auto !important;
    max-height: none !important;
    padding: 0 !important;
  }

  .step-section {
    page-break-inside: avoid;
    border: 1px solid #ccc !important;
    margin-bottom: 1rem !important;
  }

  .json-preview {
    background: white !important;
    color: black !important;
    border: 1px solid black !important;
  }
}

/* ===== MATERIAL DESIGN INTEGRATION ===== */
::ng-deep .mat-dialog-container {
  width: 55vw !important;
  max-width: 900px !important;
  min-width: 650px !important;
  max-height: 90vh !important;
  padding: 0 !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

::ng-deep .mat-dialog-content {
  padding: 0 !important;
  margin: 0 !important;
  max-height: none !important;
  overflow: visible !important;
}

::ng-deep .mat-dialog-actions {
  padding: 0 !important;
  margin: 0 !important;
  min-height: auto !important;
}

@media (max-width: 1200px) {
  ::ng-deep .mat-dialog-container {
    width: 65vw !important;
  }
}

@media (max-width: 900px) {
  ::ng-deep .mat-dialog-container {
    width: 85vw !important;
    min-width: 400px !important;
  }
}

@media (max-width: 600px) {
  ::ng-deep .mat-dialog-container {
    width: 95vw !important;
    min-width: 320px !important;
  }
}