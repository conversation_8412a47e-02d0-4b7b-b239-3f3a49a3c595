.container {
  max-width: none;
  width: 100%;
  margin: 0;
  padding: 0;
  height: auto;
  display: flex;
  flex-direction: column;
  background: #f9fafb;
}

/* Content wrapper for consistent alignment */
.content-wrapper {
  max-width: 98%;
  margin-left: 50px;
  box-sizing: border-box;
}

/* Header improvements - ALIGNED WITH CONTENT */
.improved-header {
  border-radius: 20px;
  padding: 3rem 2rem 2.5rem 2rem;
  margin-bottom: 2rem;
  height: auto;
  text-align: center;
  position: relative;
  overflow: visible;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e2e8f0;
  box-sizing: border-box;
}

.improved-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.75rem;
  letter-spacing: -0.02em;
  position: relative;
  z-index: 1;
}

.improved-subtitle {
  font-family: 'Lato', sans-serif;
  font-size: 1.2rem;
  color: #64748b;
  margin-bottom: 0;
  font-weight: 500;
  letter-spacing: 0.01em;
  position: relative;
  z-index: 1;
}

/* Client billing section with aligned positioning */
.client-billing-section {
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  height: auto;
  position: relative;
  overflow: visible;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e2e8f0;
  box-sizing: border-box;
}

.search-container {
  display: flex;
  justify-content: flex-start;
  gap: 2rem;
  flex-wrap: wrap;
  align-items: center;
}

.search-input-container {
  position: relative;
  flex: 0 1 auto;
  min-width: 320px;
  transition: all 0.3s ease;
  margin-right: auto;
}

/* Search input glass effect */
.search-input-glass-wrapper {
  position: relative;
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  z-index: 10;
}

.search-input.glass {
  width: 100%;
  padding: 1.1rem 3rem 1.1rem 3.5rem;
  border: none;
  border-radius: 20px;
  font-size: 1.15rem;
  background: rgba(255,255,255,0.3);
  box-shadow: 0 12px 40px 0 rgba(31,38,135,0.15);
  color: black;
  font-weight: 500;
  letter-spacing: 0.01em;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 2px solid rgba(16,185,129,0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  font-family: 'Lato', sans-serif;
}

.search-input.glass:focus {
  border: 2px solid rgba(16,185,129,0.4);
  background: rgba(255,255,255,0.9);
  box-shadow: 0 20px 60px 0 rgba(31,38,135,0.2);
  transform: translateY(-2px);
}

.search-input-glass-icon {
  position: absolute;
  left: 1.3rem;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-size: 1.8rem;
  pointer-events: none;
  z-index: 2;
}

.search-input-glass-clear-btn {
  position: absolute;
  right: 1.3rem;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: #a0aec0;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  z-index: 2;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 32px;
  height: 32px;
}

.search-input-glass-clear-btn:hover {
  color: #ef4444;
  background: rgba(239,68,68,0.1);
}

.choose-client-error {
  color: #ef4444;
  font-size: 1rem;
  margin-top: 0.5rem;
  margin-left: 0.3rem;
  font-weight: 600;
  letter-spacing: 0.01em;
  font-family: 'Lato', sans-serif;
}

.input-error {
  border: 2px solid #ef4444 !important;
  background: #fef2f2 !important;
}

/* Dropdown glass effect */
.dropdown-glass {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  width: 120%;
  min-width: 280px;
  background: rgba(255,255,255,0.9);
  border-radius: 20px;
  box-shadow: 0 20px 60px 0 rgba(31,38,135,0.2);
  border: 2px solid rgba(16,185,129,0.1);
  padding: 0.75rem 0;
  z-index: 1001;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  animation: dropdownGlassFadeIn 0.3s cubic-bezier(.4,2,.6,1) both;
  max-height: 350px;
  overflow-y: auto;
}

@keyframes dropdownGlassFadeIn {
  from { opacity: 0; transform: translateY(-15px) scale(0.95);}
  to   { opacity: 1; transform: translateY(0) scale(1);}
}

.dropdown-glass-item {
  display: flex;
  align-items: center;
  gap: 1.2rem;
  padding: 1rem 1.5rem;
  cursor: pointer;
  border-radius: 16px;
  margin: 0 0.75rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.dropdown-glass-item:hover {
  background: rgba(16,185,129,0.1);
  transform: translateX(4px);
}

.dropdown-glass-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.dropdown-glass-name {
  font-weight: 600;
  font-size: 1.1rem;
  color: #065f46;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.01em;
  font-family: 'Lato', sans-serif;
}

/* Selected client card */
.improved-client-card {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #edfff9 0%, #f0fdfa 100%);
  border-radius: 20px;
  padding: 1.25rem 1.5rem;
  box-shadow: 0 8px 32px rgba(16,185,129,0.15);
  border: 2px solid #10b981;
  min-width: 280px;
  position: relative;
  gap: 1rem;
  transition: all 0.3s ease;
}

.improved-client-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(16,185,129,0.2);
}

.client-details-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
}

.client-name-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.25rem;
}

.client-name-large {
  font-weight: 700;
  font-size: 1.2rem;
  color: #065f46;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: 'Montserrat', sans-serif;
}

.client-subtitle {
  font-size: 0.95rem;
  color: #64748b;
  margin-top: 3px;
  font-weight: 500;
  letter-spacing: 0.01em;
  font-family: 'Lato', sans-serif;
}

/* Payment frequency selection */
.improved-select-type-frequence-payement {
  margin-top: 1.5rem;
}

.improved-frequence-payement {
  display: flex;
  align-items: center;
  gap: 1.2rem;
}

.improved-frequence-inline {
  flex-direction: row !important;
  align-items: center !important;
  gap: 1.5rem;
}

.improved-select {
  font-family: 'Lato', sans-serif;
  font-size: 1.1rem;
  padding: 1rem 1.5rem;
  border-radius: 16px;
  border: 2px solid #bbf7d0;
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
  color: #065f46;
  font-weight: 600;
  outline: none;
  transition: all 0.3s ease;
  min-width: 200px;
  box-shadow: 0 4px 16px rgba(16,185,129,0.1);
}

.improved-select:focus {
  border: 2px solid #10b981;
  background: #edfff9;
  box-shadow: 0 8px 24px rgba(16,185,129,0.2);
  transform: translateY(-1px);
}

/* MAIN GRID LAYOUT - RESPONSIVE GRID */
.licence-cards-container {
  max-width: 98%;
  margin-left: 50px;
  padding: 24px;
  box-sizing: border-box;
}

.license-grid,
.improved-license-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 0 auto;
  max-width: 100%;
  width: 100%;
  align-items: stretch;
}

/* Responsive breakpoints for better control */
@media (min-width: 1600px) {
  .license-grid,
  .improved-license-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
  }
}

@media (min-width: 1200px) and (max-width: 1599px) {
  .license-grid,
  .improved-license-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
  }
}

@media (min-width: 900px) and (max-width: 1199px) {
  .license-grid,
  .improved-license-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }
}

@media (min-width: 600px) and (max-width: 899px) {
  .license-grid,
  .improved-license-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

/* Card improvements */
.license-card,
.improved-license-card {
  position: relative;
  background: white;
  border-radius: 24px;
  padding: 0;
  height: auto;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0,0,0,0.08);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 400px;
}

.license-card:hover,
.improved-license-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.license-card::before,
.improved-license-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%; 
  height: 6px;
  background: linear-gradient(90deg, #e2e8f0, #cbd5e1);
  border-radius: 24px 24px 0 0;
  z-index: 1;
}

.license-card:nth-child(4n+1)::before,
.improved-license-card:nth-child(4n+1)::before {
  background: linear-gradient(135deg, #10b981, #34d399);
}

.license-card:nth-child(4n+2)::before,
.improved-license-card:nth-child(4n+2)::before {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
}

.license-card:nth-child(4n+3)::before,
.improved-license-card:nth-child(4n+3)::before {
  background: linear-gradient(135deg, #8b5cf6, #a78bfa);
}

.license-card:nth-child(4n+4)::before,
.improved-license-card:nth-child(4n+4)::before {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

/* Card content wrapper to ensure proper padding */
.card-content-wrapper {
  padding: 2rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

/* Card header and description */
.card-header,
.improved-card-header {
  margin-bottom: 1.5rem;
  text-align: center;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f5f9;
  flex-shrink: 0;
}

.card-header h3,
.improved-card-header .improved-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.4rem;
  font-weight: 700;
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.75rem;
  letter-spacing: 0.01em;
}

.card-description-wrapper {
  min-height: 60px;
  max-height: 80px;
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.card-description,
.improved-card-description {
  color: #64748b;
  font-size: 0.95rem;
  line-height: 1.5;
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  margin: 0;
  overflow-wrap: break-word;
  word-wrap: break-word;
  hyphens: auto;
  text-align: justify;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Features list */
.features-list-wrapper {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  margin-top: 1rem;
  margin-bottom: 1.5rem;
}

.features-list,
.improved-features-list {
  list-style: none;
  padding: 0;
  margin: 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 12px;
  border: 2px solid #f1f5f9;
  gap: 0.75rem;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
}

.feature-item:hover {
  border-color: #e2e8f0;
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
}

.custom-checkbox {
  display: block;
  position: relative;
  cursor: pointer;
  user-select: none;
  margin-right: 0;
  flex-shrink: 0;
}

.custom-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.custom-checkbox input:disabled {
  background: #e5e7eb !important;
  color: #a1a1aa !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  opacity: 0.7;
}

.checkmark {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  width: 24px;
  border-radius: 6px;
  background-color: #f1f5f9;
  transition: all 0.3s ease;
}

.custom-checkbox input:checked ~ .checkmark {
  background-color: #10b981;
  border-color: #10b981;
  transform: scale(1.1);
}

.custom-checkbox input:disabled ~ .checkmark {
  background-color: #e2e8f0;
  cursor: not-allowed;
}

.verified-icon, .cancel-icon {
  font-size: 16px;
  color: white;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.custom-checkbox input:checked ~ .checkmark .verified-icon,
.custom-checkbox input:not(:checked) ~ .checkmark .cancel-icon {
  opacity: 1;
  transform: scale(1);
}

.feature-details {
  flex-grow: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.feature-name {
  font-size: 1rem;
  color: #334155;
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  letter-spacing: 0.01em;
}

.feature-price {
  font-weight: 700;
  color: #10b981;
  font-size: 0.95rem;
  font-family: 'Montserrat', sans-serif;
}

.checked-green {
  background-color: #d1fae5 !important;
  border: 2px solid #10b981 !important;
}

.unchecked-red {
  background-color: #fee2e2 !important;
  border: 2px solid #ef4444 !important;
}

/* License actions */
.license-actions {
  margin-top: auto;
  padding-top: 1.5rem;
  flex-shrink: 0;
}

.select-btn {
  width: 100%;
  padding: 0.875rem 1.25rem;
  border: none;
  border-radius: 14px;
  background: linear-gradient(135deg, #49b38e, #2c7744);
  color: white;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-family: 'Lato', sans-serif;
  letter-spacing: 0.01em;
  box-shadow: 0 4px 16px rgba(73, 179, 142, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.select-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(73, 179, 142, 0.4);
}

.select-btn:disabled {
  background: #e5e7eb !important;
  color: #a1a1aa !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  opacity: 0.7;
  transform: none !important;
}

/* COMPACT OVERLAY AND POPUPS */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  padding: 1rem;
  box-sizing: border-box;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.confirmation-popup {
  background: white;
  border-radius: 20px;
  padding: 1.5rem;
  width: 100%;
  max-width: 480px;
  max-height: 85vh;
  overflow: hidden;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
  position: relative;
  border: 2px solid rgba(16,185,129,0.1);
  box-sizing: border-box;
  margin: auto;
  transform: translateY(0);
  animation: popupSlideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  display: flex;
  flex-direction: column;
}

@keyframes popupSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.25rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f5f9;
  gap: 1rem;
  flex-wrap: wrap;
  flex-shrink: 0;
}

.popup-header h3 {
  font-size: 1.2rem;
  color: #1e293b;
  margin: 0;
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  flex: 1;
  min-width: 0;
  line-height: 1.4;
}

.close-popup-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  font-size: 1rem;
}

.close-popup-btn:hover {
  background: #e2e8f0;
  color: #475569;
  transform: scale(1.1);
}

.popup-content {
  margin-bottom: 1.5rem;
  flex-grow: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.confirmation-message {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.25rem;
  flex-wrap: wrap;
  flex-shrink: 0;
}

.confirmation-icon {
  color: #f59e0b;
  font-size: 1.8rem;
  margin-top: 0.2rem;
  flex-shrink: 0;
}

.confirmation-message p {
  font-size: 0.95rem;
  color: #475569;
  line-height: 1.5;
  margin: 0;
  font-family: 'Lato', sans-serif;
  flex: 1;
  min-width: 0;
}

/* SCROLLABLE SUMMARY CONTAINER */
.client-license-summary {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  padding: 1.25rem;
  border: 2px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(0,0,0,0.05);
  flex-grow: 1;
  overflow-y: auto;
  max-height: 300px;
}

/* Custom scrollbar for summary */
.client-license-summary::-webkit-scrollbar {
  width: 6px;
}

.client-license-summary::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 8px;
  margin: 0.5rem 0;
}

.client-license-summary::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 8px;
  border: 1px solid transparent;
  background-clip: content-box;
}

.client-license-summary::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #059669, #047857);
  background-clip: content-box;
}

/* Firefox scrollbar for summary */
.client-license-summary {
  scrollbar-width: thin;
  scrollbar-color: #10b981 #f1f5f9;
}

.summary-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding: 0.5rem 0;
  gap: 1rem;
  flex-wrap: wrap;
  min-height: auto;
}

.summary-item:last-child {
  margin-bottom: 0;
  border-top: 2px solid #e2e8f0;
  padding-top: 1rem;
  font-weight: 700;
}

/* Spinning animation for loading icons */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Enhanced spinning class */
.spinning-icon {
  animation: spin 1s linear infinite;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Enhanced disabled button styles */
.confirm-btn:disabled,
.cancel-btn:disabled,
.close-popup-btn:disabled {
  background: #e5e7eb !important;
  color: #a1a1aa !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  opacity: 0.7;
  transform: none !important;
  border: 2px solid #e5e7eb !important;
  pointer-events: none;
  user-select: none;
}

.confirm-btn:disabled:hover,
.cancel-btn:disabled:hover,
.close-popup-btn:disabled:hover {
  background: #e5e7eb !important;
  color: #a1a1aa !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Loading state for confirm buttons */
.confirm-btn[disabled]:not(.cancel-btn) {
  background: linear-gradient(135deg, rgba(73, 179, 142, 0.7), rgba(44, 119, 68, 0.7)) !important;
  color: white !important;
  cursor: wait !important;
  opacity: 0.9;
}

/* Ensure the spinning icon is visible */
.confirm-btn .material-icons {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
  font-family: 'Lato', sans-serif;
  flex-shrink: 0;
  min-width: fit-content;
}

.client-info-summary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #065f46;
  flex: 1;
  min-width: 0;
  font-size: 0.9rem;
}

.client-image {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #d1fae5;
  flex-shrink: 0;
}

.license-name {
  font-weight: 600;
  color: #1e293b;
  background: linear-gradient(135deg, #49b38e, #2c7744);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 0.9rem;
}

.license-total {
  font-weight: 700;
  color: #10b981;
  font-size: 1rem;
  flex-shrink: 0;
}

.popup-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  flex-wrap: wrap;
  align-items: center;
  flex-shrink: 0;
}

.cancel-btn, .confirm-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  min-width: 120px;
  justify-content: center;
  white-space: nowrap;
}

.cancel-btn {
  background: #f8fafc;
  color: #64748b;
  border: 2px solid #e2e8f0;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.cancel-btn:hover:not(:disabled) {
  background: #e2e8f0;
  color: #475569;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0,0,0,0.1);
}

.confirm-btn {
  background: linear-gradient(135deg, #49b38e, #2c7744);
  color: white;
  box-shadow: 0 6px 20px rgba(73, 179, 142, 0.3);
}

.confirm-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 28px rgba(73, 179, 142, 0.4);
  background: linear-gradient(135deg, #2c7744, #1f5f3f);
}

/* Success notification */
.success-notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: white;
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border-left: 6px solid #10b981;
  z-index: 3000;
  max-width: 400px;
  width: calc(100% - 4rem);
  animation: slideInFromRight 0.4s ease-out;
  box-sizing: border-box;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  flex-wrap: wrap;
}

.success-icon {
  color: #10b981;
  font-size: 2rem;
  margin-top: 0.2rem;
  flex-shrink: 0;
}

.notification-text {
  flex: 1;
  min-width: 0;
}

.notification-text h4 {
  font-size: 1.1rem;
  color: #065f46;
  margin: 0 0 0.5rem 0;
  font-weight: 700;
  font-family: 'Montserrat', sans-serif;
}

.notification-text p {
  font-size: 0.9rem;
  color: #374151;
  margin: 0;
  line-height: 1.5;
  font-family: 'Lato', sans-serif;
}

.close-notification-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  background: #f0fdf4;
  color: #10b981;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: auto;
  flex-shrink: 0;
}

.close-notification-btn:hover {
  background: #dcfce7;
  color: #059669;
  transform: scale(1.1);
}

.chosen-options-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 0.5rem;
  align-items: flex-start;
}

.option-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
  border: 1px solid;
  white-space: nowrap;
}

/* Back button styling */
.back-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 20px;
  cursor: pointer;
  font-size: 1rem;
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(16,185,129,0.3);
}

.back-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 28px rgba(16,185,129,0.4);
}

/* RESPONSIVE DESIGN FOR COMPACT POPUPS */

/* Extra Large Screens */
@media (min-width: 1400px) {
  .confirmation-popup {
    max-width: 520px;
    padding: 2rem;
  }
  
  .popup-header h3 {
    font-size: 1.3rem;
  }
  
  .confirmation-message p {
    font-size: 1rem;
  }
  
  .client-license-summary {
    padding: 1.5rem;
    max-height: 350px;
  }
}

/* Large Screens */
@media (min-width: 1200px) and (max-width: 1399px) {
  .confirmation-popup {
    max-width: 500px;
    padding: 1.75rem;
  }
}

/* Medium Screens */
@media (min-width: 900px) and (max-width: 1199px) {
  .confirmation-popup {
    max-width: 460px;
    padding: 1.5rem;
  }
  
  .client-license-summary {
    max-height: 280px;
  }
}

/* Tablet Portrait and Small Laptops */
@media (min-width: 769px) and (max-width: 899px) {
  .confirmation-popup {
    max-width: 420px;
    padding: 1.5rem;
    margin: 1rem;
  }
  
  .popup-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .popup-header h3 {
    font-size: 1.1rem;
  }
  
  .confirmation-message {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .confirmation-icon {
    font-size: 1.5rem;
    align-self: center;
  }
  
  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.4rem;
  }
  
  .popup-actions {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .cancel-btn, .confirm-btn {
    width: 100%;
    min-width: auto;
  }
  
  .client-license-summary {
    max-height: 240px;
  }
}

/* Mobile Landscape and Large Mobile */
@media (min-width: 481px) and (max-width: 768px) {
  .overlay {
    padding: 0.75rem;
    align-items: flex-start;
    padding-top: 2rem;
  }
  
  .confirmation-popup {
    max-width: 100%;
    width: 100%;
    padding: 1.25rem;
    margin: 0;
    max-height: calc(100vh - 1.5rem);
    border-radius: 16px;
  }
  
  .popup-header {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .popup-header h3 {
    font-size: 1.1rem;
    line-height: 1.3;
  }
  
  .close-popup-btn {
    align-self: flex-end;
    width: 30px;
    height: 30px;
  }
  
  .confirmation-message {
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1rem;
  }
  
  .confirmation-icon {
    font-size: 1.5rem;
    align-self: center;
  }
  
  .confirmation-message p {
    font-size: 0.9rem;
    text-align: left;
  }
  
  .client-license-summary {
    padding: 1rem;
    border-radius: 12px;
    max-height: 200px;
  }
  
  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.4rem;
    margin-bottom: 0.75rem;
    padding: 0.4rem 0;
  }
  
  .label {
    font-size: 0.85rem;
  }
  
  .license-total {
    font-size: 0.9rem;
  }
  
  .chosen-options-list {
    gap: 4px;
  }
  
  .option-tag {
    font-size: 0.7rem;
    padding: 3px 6px;
  }
  
  .popup-actions {
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1rem;
  }
  
  .cancel-btn, .confirm-btn {
    width: 100%;
    padding: 0.7rem 1.25rem;
    font-size: 0.9rem;
    min-width: auto;
  }
}

/* Mobile Portrait */
@media (max-width: 480px) {
  .overlay {
    padding: 0.5rem;
    align-items: flex-start;
    padding-top: 1rem;
  }
  
  .confirmation-popup {
    max-width: 100%;
    width: 100%;
    padding: 1rem;
    margin: 0;
    max-height: calc(100vh - 1rem);
    border-radius: 12px;
  }
  
  .popup-header {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .popup-header h3 {
    font-size: 1rem;
    line-height: 1.3;
  }
  
  .close-popup-btn {
    align-self: flex-end;
    width: 28px;
    height: 28px;
    font-size: 0.9rem;
  }
  
  .confirmation-message {
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }
  
  .confirmation-icon {
    font-size: 1.3rem;
    align-self: center;
  }
  
  .confirmation-message p {
    font-size: 0.85rem;
    line-height: 1.4;
  }
  
  .client-license-summary {
    padding: 0.85rem;
    border-radius: 10px;
    max-height: 180px;
  }
  
  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
    margin-bottom: 0.6rem;
    padding: 0.3rem 0;
  }
  
  .label {
    font-size: 0.8rem;
  }
  
  .license-total {
    font-size: 0.85rem;
  }
  
  .chosen-options-list {
    gap: 3px;
    margin-top: 0.4rem;
  }
  
  .option-tag {
    font-size: 0.65rem;
    padding: 2px 5px;
    border-radius: 6px;
  }
  
  .popup-actions {
    flex-direction: column;
    gap: 0.6rem;
    margin-top: 1rem;
  }
  
  .cancel-btn, .confirm-btn {
    width: 100%;
    padding: 0.65rem 1rem;
    font-size: 0.85rem;
    min-width: auto;
    gap: 0.4rem;
  }
  
  .popup-content {
    margin-bottom: 1rem;
  }
}

/* Main Page Mobile Responsive Design */
@media (max-width: 768px) {
  .content-wrapper {
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .improved-header {
    margin: 0 1rem 2rem 1rem;
    padding: 2rem 1rem;
  }

  .improved-title {
    font-size: 2rem;
  }

  .improved-subtitle {
    font-size: 1rem;
  }

  .client-billing-section {
    margin: 0 1rem 2rem 1rem;
    padding: 1.5rem;
  }

  .licence-cards-container {
    margin-left: 0;
    padding: 0 1rem 2rem 1rem;
  }

  .license-grid,
  .improved-license-grid {
    grid-template-columns: 1fr !important;
    gap: 1.5rem;
  }

  .search-container {
    flex-direction: column;
    gap: 1.5rem;
  }

  .search-input-container {
    margin-right: 0;
    min-width: auto;
  }

  .improved-client-card {
    min-width: auto;
  }

  .success-notification {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    width: auto;
    max-width: none;
    padding: 1.25rem;
  }

  .notification-content {
    gap: 0.75rem;
  }

  .success-icon {
    font-size: 1.75rem;
  }

  .notification-text h4 {
    font-size: 1rem;
  }

  .notification-text p {
    font-size: 0.85rem;
  }
}

/* Tablet Responsive Design */
@media (min-width: 769px) and (max-width: 1024px) {
  .content-wrapper {
    margin-left: 25px;
  }
  
  .licence-cards-container {
    margin-left: 25px;
    padding: 20px;
  }

  .success-notification {
    max-width: 350px;
  }

  /* Tablet grid adjustments */
  .license-grid,
  .improved-license-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }
}

/* Enhanced scroll behavior for all screen sizes */
@media (max-height: 600px) {
  .overlay {
    align-items: flex-start;
    padding-top: 0.5rem;
  }
  
  .confirmation-popup {
    max-height: calc(100vh - 1rem);
    margin: 0.5rem 0;
  }
  
  .client-license-summary {
    max-height: 150px;
  }
}

/* Landscape mobile specific adjustments */
@media (max-height: 500px) and (orientation: landscape) {
  .overlay {
    padding: 0.25rem;
    align-items: flex-start;
  }
  
  .confirmation-popup {
    max-height: calc(100vh - 0.5rem);
    padding: 0.75rem;
  }
  
  .popup-header {
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
  }
  
  .popup-content {
    margin-bottom: 0.75rem;
  }
  
  .client-license-summary {
    max-height: 120px;
  }
  
  .summary-item {
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
  }
}