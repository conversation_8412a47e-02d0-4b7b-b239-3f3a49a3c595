
import { ApplicationConfig,importProvidersFrom } from '@angular/core';
import { provideRouter, RouterModule } from '@angular/router';
import { routes } from './app.routes';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { authInterceptor } from '@app/core/services/interceptors/auth.interceptor';
import { BrowserAnimationsModule, provideAnimations } from '@angular/platform-browser/animations';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { MatDialogModule } from '@angular/material/dialog';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { EnterpriseService } from './core/services/enterprise.service';
import { Zigbee2MqttService } from './core/services/mqtt.service' ;
import { MqttModule, IMqttServiceOptions } from 'ngx-mqtt';
import { HashLocationStrategy, LocationStrategy } from '@angular/common';
import { provideHotToastConfig } from '@ngxpert/hot-toast';
import { NgxUiLoaderModule, NgxUiLoaderConfig, SPINNER } from 'ngx-ui-loader';
import { MessageService } from 'primeng/api';
import { NgxLoadingModule } from 'ngx-loading';
import { environment } from './environments/environment';


const ngxUiLoaderConfig: NgxUiLoaderConfig = {
  fgsColor: '#2F7D33',
  pbColor: '#2F7D33',
  overlayColor: 'rgba(40,40,40,0.8)',
  fgsType: SPINNER.threeBounce ,

};
// MQTT Configuration for Zigbee2MQTT
const MQTT_SERVICE_OPTIONS: IMqttServiceOptions = {
  connectOnCreate: true,
  hostname: environment.mqtt.hostname,
  port: environment.mqtt.port,
  path: environment.mqtt.path,
  protocol: 'ws',
  //clientId: 'zigbee-dashboard-' + Math.random().toString(16).substr(2, 8)
};

export const appConfig: ApplicationConfig = {
  providers: [
    MessageService,
    provideHotToastConfig({
      position: 'top-right',
      duration: 3000,
    }),
    Zigbee2MqttService,
    {
      provide: LocationStrategy,
      useClass: HashLocationStrategy
    },
    importProvidersFrom(NgxUiLoaderModule.forRoot(ngxUiLoaderConfig)),
    importProvidersFrom(MqttModule.forRoot(MQTT_SERVICE_OPTIONS)),
    provideRouter(routes),
    provideHttpClient(withInterceptors([authInterceptor])),
    EnterpriseService,
    provideAnimations(),
    importProvidersFrom([
      // NgxSpinnerModule,
      BrowserAnimationsModule,
      MatToolbarModule,
      MatSidenavModule,
      MatListModule,
      MatIconModule,
      MatButtonModule,
      MatCardModule,
      MatFormFieldModule,
      MatInputModule,
      // MatSnackBarModule,
      MatTableModule,
      MatDialogModule,
      FormsModule,
      NgxLoadingModule.forRoot({}),

      ReactiveFormsModule,
      RouterModule.forRoot(routes),
    ])
  ]
};
