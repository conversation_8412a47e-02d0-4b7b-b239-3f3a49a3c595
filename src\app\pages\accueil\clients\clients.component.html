<ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
<div id="section" class="header-section">
  <div class="page-title">
    <h1 class="title">
      <i class="material-icons title-icon">business</i>
      {{
      selectedType
      ? "Clients de " + selectedType
      : "Gestion des Clients"
      }}
    </h1>
  </div>

  <div class="actions">
    <button class="view-toggle" (click)="toggleViewMode()">
      <i class="material-icons action-icon">{{
        viewMode === "cards" ? "view_list" : "grid_view"
        }}</i>
      {{ viewMode === "cards" ? "Vue Tableau" : "Vue Cartes" }}
    </button>
  </div>
</div>
<div class="search-bar">
  <div class="search-container">
    <input type="text" [(ngModel)]="searchParam" placeholder="Rechercher par nom..." class="search-input"
      (keyup.enter)="searchClients()" />
    <button class="clear-btn" (click)="clearSearch()" *ngIf="searchParam">
      <mat-icon style="font-size: 22px;">close</mat-icon>
    </button>
  </div>
  <button class="search-button" (click)="searchClients()">
    <i class="material-icons">search</i>
  </button>
</div>
<div class="active-filters" *ngIf="searchOrganisationType">
  <span class="active-filter">
    {{ getOrganisationNameById(selectedOrganisationId)}}
    <button (click)="clearTypeFilter()" class="clear-filter" title="Supprimer le filtre">
      <i class="material-icons">close</i>
    </button>
  </span>
</div>

<div class="loading-container" *ngIf="isLoading">
  <p>Chargement...</p>
  <ngx-ui-loader></ngx-ui-loader>
</div>

<div class="no-data" *ngIf="!isLoading && clients.length === 0">
  <p>no Client found</p>
</div>

<!-- Cards View -->
<div class="cards-view-container" *ngIf="viewMode === 'cards' && !isLoading && clients.length > 0">
  <div class="cards-container-grid">
    <app-card *ngFor="let client of clients" [client]="client" (view)="viewClientDetailsById($event)"
      (edit)="editClientById($event)" (delete)="deleteClientById($event)"></app-card>
  </div>
</div>

<!-- Card View Pagination -->
<div class="card-pagination-container" *ngIf="viewMode === 'cards' && !isLoading && clients.length > 0">
  <mat-paginator [length]="totalCount" [pageSize]="pageSize" [pageIndex]="currentPage"
    [pageSizeOptions]="[5, 10, 25, 50]" (page)="onPageChange($event)" aria-label="Select page">
  </mat-paginator>
</div>

<!-- Table View -->
<div class="table-container" *ngIf="viewMode === 'table' && !isLoading && clients.length > 0">
  <app-generic-table [data]="clients" [headers]="headers" [keys]="keys" [actions]="['edit', 'view', 'delete']"
    (actionTriggered)="handleAction($event)">
  </app-generic-table>

  <!-- Table Pagination -->
  <div class="pagination-container">
    <mat-paginator [length]="totalCount" [pageSize]="pageSize" [pageIndex]="currentPage"
      [pageSizeOptions]="[5, 10, 25, 50]" (page)="onPageChange($event)" aria-label="Select page">
    </mat-paginator>
  </div>
</div>
