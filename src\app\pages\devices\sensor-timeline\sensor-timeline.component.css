/* Overlay stays the same */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background: rgba(0, 0, 0, 0.5);
  z-index: 5;
}

.timeline-popup.chat-style {
  background: #fff;
  width: 90%;
  max-width: 600px;
  height: 80vh;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  padding: 0;  
  color: #333;
  z-index: 1000;
  border: 3px solid #4caf50;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -45%);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px; /* add padding here */
  border-bottom: 2px solid #4caf50; 
  height: 12%;
  color: #4caf50; 
  border-radius: 10px 10px 0 0; 
  width: 100%; /* ensure full width */
  box-sizing: border-box; /* include padding in width */
}

/* Close button in header */
.close-btn {
  background: transparent;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #4caf50;
  transition: color 0.3s ease;
}

.close-btn:hover {
  color: #dddddd;
}

/* Chat body */
.chat-body {
  flex: 1;
  overflow-y: auto;
  padding: 16px 8px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-x: hidden;
  background-color: #f5f5f5;
  border-radius: 0 0 10px 10px; 


}

/* Individual message block */
.chat-message {
  display: flex;
  justify-content: flex-start;
}

.chat-message.actif {
  justify-content: flex-end;
}

.chat-message .bubble {
  background-color: #fff;
  padding: 10px 14px;
  border-radius: 12px;
  max-width: 75%;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  word-break: break-word;
}

/* Token label */
.token {
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: #9e9e9e;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  text-transform: capitalize;
}

.chat-message.enveille .token {
  background-color: #ff9800;
}

.chat-message.inactif .token {
  background-color: #f44336;
}
.chat-message.actif .token {
  background-color: #4caf50;
}


.message-time {
  font-size: 12px;
  font-weight: bold;
  color: #555;
  margin-bottom: 4px;
}

.message-log {
  font-size: 14px;
  color: #333;
}
.sensor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.sensor-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  background: #f3f3f3;
  padding: 10px;
  border-radius: 8px;
}

.sensor-icon {
  margin-right: 10px;
  color: #555;
}

.sensor-info {
  display: flex;
  flex-direction: column;
}

.sensor-label {
  font-weight: bold;
  font-size: 13px;
}

.sensor-value {
  font-size: 14px;
}

.boolean-true {
  color: green;
  font-weight: bold;
}

.boolean-false {
  color: red;
  font-weight: bold;
}
.chat-body {
  overflow-y: auto;
  scrollbar-width: auto;
  scrollbar-color: #4caf50 transparent; /* green thumb, light gray track */
}

/* Chrome, Edge, Safari */
.chat-body::-webkit-scrollbar {
  width: 10px;
  background-color: transparent;
}

.chat-body::-webkit-scrollbar-track {
  background: #e0e0e0; 
  border-radius: 4px;
}

.chat-body::-webkit-scrollbar-thumb {
  background-color: #4caf50;
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
}
.chat-body::-webkit-scrollbar-button {
  display: none;
  width: 0;
  height: 0;
}


