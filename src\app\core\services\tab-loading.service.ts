// tab-loading.service.ts
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface TabLoadingState {
  tabName: string;
  isLoading: boolean;
  loadingMessage?: string;
}

@Injectable({
  providedIn: 'root'
})
export class TabLoadingService {
  private loadingStates = new Map<string, BehaviorSubject<boolean>>();
  private loadingMessages = new Map<string, string>();
  private globalLoadingSubject = new BehaviorSubject<boolean>(false);
  private currentTabSubject = new BehaviorSubject<string>('');

  constructor() {}

  // Initialize loading state for a tab
  initializeTab(tabName: string): void {
    if (!this.loadingStates.has(tabName)) {
      this.loadingStates.set(tabName, new BehaviorSubject<boolean>(false));
    }
  }

  // Set loading state for a specific tab
  setTabLoading(tabName: string, isLoading: boolean, message?: string): void {
    this.initializeTab(tabName);
    
    const subject = this.loadingStates.get(tabName);
    if (subject) {
      subject.next(isLoading);
    }

    if (message) {
      this.loadingMessages.set(tabName, message);
    }

    this.updateGlobalLoading();
  }

  // Get loading state for a specific tab
  getTabLoading(tabName: string): Observable<boolean> {
    this.initializeTab(tabName);
    return this.loadingStates.get(tabName)!.asObservable();
  }

  // Get current loading message for a tab
  getTabMessage(tabName: string): string {
    return this.loadingMessages.get(tabName) || `Chargement de ${tabName}...`;
  }

  // Set current active tab
  setCurrentTab(tabName: string): void {
    this.currentTabSubject.next(tabName);
  }

  // Get current active tab
  getCurrentTab(): Observable<string> {
    return this.currentTabSubject.asObservable();
  }

  // Get global loading state (true if any tab is loading)
  getGlobalLoading(): Observable<boolean> {
    return this.globalLoadingSubject.asObservable();
  }

  // Check if current active tab is loading
  isCurrentTabLoading(): Observable<boolean> {
    return new Observable(observer => {
      this.currentTabSubject.subscribe(currentTab => {
        if (currentTab && this.loadingStates.has(currentTab)) {
          this.loadingStates.get(currentTab)!.subscribe(isLoading => {
            observer.next(isLoading);
          });
        } else {
          observer.next(false);
        }
      });
    });
  }

  // Update global loading state based on all tabs
  private updateGlobalLoading(): void {
    let anyLoading = false;
    this.loadingStates.forEach(subject => {
      if (subject.value) {
        anyLoading = true;
      }
    });
    this.globalLoadingSubject.next(anyLoading);
  }

  // Clear loading state for a tab
  clearTabLoading(tabName: string): void {
    this.setTabLoading(tabName, false);
  }

  // Clear all loading states
  clearAllLoading(): void {
    this.loadingStates.forEach((subject, tabName) => {
      subject.next(false);
    });
    this.loadingMessages.clear();
    this.updateGlobalLoading();
  }

  // Get all loading states (for debugging)
  getAllLoadingStates(): { [key: string]: boolean } {
    const states: { [key: string]: boolean } = {};
    this.loadingStates.forEach((subject, tabName) => {
      states[tabName] = subject.value;
    });
    return states;
  }
}