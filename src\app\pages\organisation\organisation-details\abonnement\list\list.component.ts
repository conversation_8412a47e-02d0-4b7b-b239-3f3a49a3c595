import { CommonModule } from '@angular/common';
import {
  Component,
  HostListener,
  OnInit,
  Output,
  EventEmitter,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { lastValueFrom } from 'rxjs';

import { Client } from '@app/core/models/client';
import { Licence } from '@app/core/models/licence';
import { Subscription } from '@app/core/models/subscription';

import { ClientApiService } from '@app/core/services/administrative/client.service';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { DetailComponent } from '../detail/detail.component';
import { ActivatedRoute } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS,
} from 'ng-angular-popup';
import { Lister } from '@app/shared/models/rule/Lister';
import { TabLoadingService } from '@app/core/services/tab-loading.service';
import { WhereParams } from '@app/shared/models/rule/WhereParams';
import { Pagination } from '@app/shared/models/rule/Pagination';
import { Sorting } from '@app/shared/models/rule/Sorting';

@Component({
  selector: 'app-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DetailComponent,
    MatIconModule,
    NgToastComponent,
    MatPaginatorModule,
  ],
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.css'],
})
export class ListComponent implements OnInit {
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    if (
      !target.closest('.dropdown-container') &&
      !target.closest('.modal-container')
    ) {
      if (this.showDropdown) {
        console.log('Click outside dropdown - closing');
      }
      this.showDropdown = false;
    }
  }

  // Main data arrays
  subscriptions: Subscription[] = [];
  clients: Client[] = [];
  licences: Licence[] = [];

  // Loading states
  isLoading: boolean = false;

  // Client filter
  clientId: string | null = null;

  // Pagination
  lister: Lister<any> = {
    Pagination: {
      CurrentPage: 0,
      PageSize: 5,
      TotalElement: 0,
      PageCount: 0,
      IsFirst: true,
      IsLast: false,
      StartIndex: 0,
    },
    FilterParams: [] as WhereParams[],
    SortParams: [] as Sorting[],
  };

  currentPage = 0;
  pageSize = 5;
  totalSubscriptions = 0;
  pageSizeOptions: number[] = [5, 10, 25, 50];

  // Search and filters
  searchQuery: string = '';
  statusFilter: string = 'All'; // You can add status filtering if needed
  hasSearchFilter: boolean = false;

  TOAST_POSITIONS = TOAST_POSITIONS;

  renewType: 'monthly' | 'yearly' = 'monthly';

  selectedSubscription: Subscription | null = null;
  isRenewing: { [subscriptionId: string]: boolean } = {};

  showDropdown: boolean = false;
  showDetailModal: boolean = false;
  showFormModal: boolean = false;
  showRenewModal: boolean = false;

  @Output() loadingChange = new EventEmitter<{
    isLoading: boolean;
    message?: string;
  }>();

  constructor(
    private subscriptionApiService: SubscriptionApiService,
    private clientApiService: ClientApiService,
    private licenceApiService: LicenceApiService,
    private route: ActivatedRoute,
    private toast: NgToastService,
    private tabLoadingService: TabLoadingService
  ) {}

  ngOnInit(): void {
    const hash = window.location.hash;
    const match = hash.match(/organisation-details\/([a-f0-9\-]+)/i);
    if (match && match[1]) {
      this.clientId = match[1];
    }
    console.log('Extracted clientId:', this.clientId);
    this.loadData();
  }

  async loadData(): Promise<void> {
    this.isLoading = true;
    this.tabLoadingService.setTabLoading(
      'abonnement',
      true,
      'Chargement des abonnements...'
    );
    this.loadingChange.emit({
      isLoading: true,
      message: 'Chargement des abonnements...',
    });

    try {
      await this.fetchSubscriptionsPage();

      await this.fetchRelatedDataForCurrentPage();
    } catch (error) {
      this.showError('Erreur lors du chargement des données', 'Erreur');
      console.error('loadData error:', error);
    } finally {
      this.isLoading = false;
      this.tabLoadingService.setTabLoading('abonnement', false);
      this.loadingChange.emit({ isLoading: false });
    }
  }

  async fetchSubscriptionsPage(): Promise<void> {
    const pagination: Pagination = {
      CurrentPage: this.currentPage,
      PageSize: this.pageSize,
      PageCount: 0,
      IsLast: false,
      IsFirst: false,
      StartIndex: 0,
      TotalElement: 0,
    };

    const filterParams: WhereParams[] = [];

    if (this.clientId) {
      filterParams.push({
        Column: 'ClientId',
        Op: 'eq',
        Value: this.clientId,
        AndOr: 'AND',
      });
    }

    if (this.searchQuery.trim() !== '') {
      filterParams.push({
        Column: 'Licence.Name',
        Op: 'contains',
        Value: this.searchQuery.trim(),
        AndOr: 'AND',
      });
    }

    if (this.statusFilter !== 'All') {
      filterParams.push({
        Column: 'Status',
        Op: 'eq',
        Value: this.statusFilter,
        AndOr: 'AND',
      });
    }

    const lister: Lister<any> = {
      Pagination: pagination,
      FilterParams: filterParams,
      SortParams: [],
    };

    return new Promise((resolve) => {
      this.subscriptionApiService.gatePage(lister).subscribe({
        next: (response) => {
          this.subscriptions = response.Content ?? [];

          if (response.Lister?.Pagination) {
            this.totalSubscriptions =
              response.Lister.Pagination.TotalElement ?? 0;
            this.lister.Pagination = {
              CurrentPage: response.Lister.Pagination.CurrentPage ?? 0,
              PageSize: response.Lister.Pagination.PageSize ?? 0,
              TotalElement: response.Lister.Pagination.TotalElement ?? 0,
              PageCount: response.Lister.Pagination.PageCount ?? 0,
              IsFirst: response.Lister.Pagination.IsFirst ?? false,
              IsLast: response.Lister.Pagination.IsLast ?? false,
              StartIndex: response.Lister.Pagination.StartIndex ?? 0,
            };
          } else {
            this.totalSubscriptions = this.subscriptions.length;
          }

          console.log('Fetched subscriptions:', this.subscriptions);
          console.log('Total subscriptions:', this.totalSubscriptions);
          resolve();
        },
        error: (err) => {
          console.error('Error fetching subscriptions:', err);
          this.showError('Échec du chargement des abonnements', 'Erreur');
          this.subscriptions = [];
          this.totalSubscriptions = 0;
          resolve();
        },
      });
    });
  }

  async fetchRelatedDataForCurrentPage(): Promise<void> {
    if (this.subscriptions.length === 0) {
      this.clients = [];
      this.licences = [];
      return;
    }

    try {
      const clientIds = [
        ...new Set(
          this.subscriptions
            .map((sub) => sub.ClientId)
            .filter((id) => id != null)
        ),
      ];

      const licenceIds = [
        ...new Set(
          this.subscriptions
            .map((sub) => sub.LicenceId)
            .filter((id) => id != null)
        ),
      ];

      await Promise.all([
        this.fetchClientsByIds(clientIds),
        this.fetchLicencesByIds(licenceIds),
      ]);
    } catch (error) {
      console.error('Error fetching related data:', error);
      this.clients = [];
      this.licences = [];
    }
  }

  fetchClientsByIds(clientIds: string[]): Promise<void> {
    if (clientIds.length === 0) {
      this.clients = [];
      return Promise.resolve();
    }

    const filterParams: WhereParams[] = clientIds.map((id, index) => ({
      Column: 'Id',
      Op: 'eq',
      Value: id,
      AndOr: index === 0 ? 'AND' : 'OR',
    }));

    const lister: Lister<any> = {
      Pagination: {
        CurrentPage: 0,
        PageSize: clientIds.length,
        TotalElement: 0,
        PageCount: 0,
        IsFirst: true,
        IsLast: false,
        StartIndex: 0,
      },
      FilterParams: filterParams,
      SortParams: [],
    };

    return new Promise((resolve) => {
      this.clientApiService.gatePage(lister).subscribe({
        next: (page) => {
          this.clients = page.Content ?? [];
          resolve();
        },
        error: (error) => {
          console.error('Error fetching clients by IDs:', error);
          this.clients = [];
          resolve();
        },
      });
    });
  }

  fetchLicencesByIds(licenceIds: string[]): Promise<void> {
    if (licenceIds.length === 0) {
      this.licences = [];
      return Promise.resolve();
    }

    const filterParams: WhereParams[] = licenceIds.map((id, index) => ({
      Column: 'Id',
      Op: 'eq',
      Value: id,
      AndOr: index === 0 ? 'AND' : 'OR',
    }));

    const lister: Lister<any> = {
      Pagination: {
        CurrentPage: 0,
        PageSize: licenceIds.length,
        TotalElement: 0,
        PageCount: 0,
        IsFirst: true,
        IsLast: false,
        StartIndex: 0,
      },
      FilterParams: filterParams,
      SortParams: [],
    };

    return new Promise((resolve) => {
      this.licenceApiService.gatePage(lister).subscribe({
        next: (page) => {
          this.licences = page.Content ?? [];
          resolve();
        },
        error: (error) => {
          console.error('Error fetching licences by IDs:', error);
          this.licences = [];
          resolve();
        },
      });
    });
  }

  onPageChange(event: PageEvent): void {
    this.isLoading = true;
    this.pageSize = event.pageSize;
    this.currentPage = event.pageIndex;
    this.loadData();
  }

  searchSubscriptions(): void {
    this.isLoading = true;
    this.currentPage = 0; // Reset to first page
    this.loadData().then(() => {
      this.hasSearchFilter = this.searchQuery.trim() !== '';
    });
  }

  onSearchKeyup(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.searchSubscriptions();
    } else if (event.key === 'Backspace' && this.searchQuery === '') {
      this.clearSearch();
    }
  }

  clearSearch(): void {
    this.searchQuery = '';
    this.currentPage = 0;
    this.statusFilter = 'All';
    this.hasSearchFilter = false;
    this.loadData();
  }

  updateFilteredSubscriptions(): void {
    this.currentPage = 0;
    this.loadData();
  }

  selectSubscription(sub: Subscription) {
    this.selectedSubscription = this.selectedSubscription === sub ? null : sub;
  }

  getClientName(clientId?: string): string {
    const client = this.clients.find((c) => c.Id === clientId);
    return client ? client.Name : 'Unknown Client';
  }

  getLicenceName(licenceId?: string): string {
    const licence = this.licences.find((l) => l.Id === licenceId);
    return licence ? licence.Name : 'Unknown Licence';
  }

  // New method to check if subscription can be renewed
  canRenewSubscription(subscription: Subscription): boolean {
    if (!subscription.Status) {
      return true; // Allow renewal if status is undefined/null
    }

    const status = subscription.Status.toLowerCase().trim();
    const blockedStatuses = ['en attente', 'resilié', 'résilié'];

    return !blockedStatuses.includes(status);
  }

  // New method to get appropriate title for the renew button
  getRenewButtonTitle(subscription: Subscription): string {
    if (!this.canRenewSubscription(subscription)) {
      return `Renouvellement non disponible - Statut: ${subscription.Status}`;
    }
    return "Renouveller l'abonnement";
  }

  onFormSubmit(updatedSubscription: Subscription): void {
    console.log('Form submitted:', updatedSubscription);
    this.loadData();
    this.closeFormModal();
  }

  openRenewModal(sub: Subscription): void {
    // Check if renewal is allowed before opening modal
    if (!this.canRenewSubscription(sub)) {
      this.showError(
        `Impossible de renouveler cet abonnement. Statut actuel: ${sub.Status}`,
        'Renouvellement non autorisé'
      );
      return;
    }

    this.selectedSubscription = sub;
    this.renewType =
      sub.PaymentFrequency?.toLowerCase() === 'monthly' ? 'monthly' : 'yearly';
    this.showRenewModal = true;
    document.body.classList.add('modal-open');
    event?.stopPropagation();
  }

  closeRenewModal(): void {
    this.showRenewModal = false;
    this.selectedSubscription = null;
    document.body.classList.remove('modal-open');
  }

  async confirmAndRenewSubscription(): Promise<void> {
    if (!this.selectedSubscription || !this.selectedSubscription.Id) {
      alert('No subscription selected for renewal or ID is missing.');
      return;
    }

    const subToRenew = this.selectedSubscription;
    this.isRenewing[subToRenew.Id] = true;
    console.log(this.renewType, ' renewType');

    try {
      const response = await lastValueFrom(
        this.subscriptionApiService.renewSubscription(
          subToRenew.Id.toUpperCase(),
          this.renewType
        )
      );

      await this.loadData();

      const newEndDateDisplay = response.subscription.DateFin
        ? new Date(response.subscription.DateFin).toLocaleDateString()
        : 'Date non disponible';

      this.showSuccess(
        `Abonnement renouvelé avec succès pour ${this.getClientName(
          subToRenew.ClientId
        )} - ${this.getLicenceName(
          subToRenew.LicenceId
        )}! Nouvelle date de fin: ${newEndDateDisplay}. Montant de la facture: ${
          response.facture.Total
        }€`,
        'Succés'
      );
      this.closeRenewModal();
    } catch (err: any) {
      console.error("Erreur lors du renouvellement de l'abonnement:", err);
      this.showError(
        `Échec du renouvellement de l'abonnement. Erreur: ${
          err.message || 'Erreur inconnue'
        }`,
        'Erreur'
      );
    } finally {
      this.isRenewing[subToRenew.Id] = false;
    }
  }

  toggleDropdown(sub: Subscription): void {
    console.log('Toggle dropdown called for', sub);
    if (sub) {
      if (this.selectedSubscription === sub) {
        this.showDropdown = !this.showDropdown;
      } else {
        this.selectedSubscription = sub;
        this.showDropdown = true;
      }
      console.log('showDropdown:', this.showDropdown);
    }
  }

  openDetailModal(): void {
    if (this.selectedSubscription) {
      this.showDetailModal = true;
      this.showDropdown = false;
      document.body.classList.add('modal-open');
    }
  }

  openFormModal(): void {
    if (this.selectedSubscription) {
      this.showFormModal = true;
      this.showDropdown = false;
      document.body.classList.add('modal-open');
    }
  }

  closeDetailModal(): void {
    this.showDetailModal = false;
    document.body.classList.remove('modal-open');
  }

  closeFormModal(): void {
    this.showFormModal = false;
    document.body.classList.remove('modal-open');
  }

  private showSuccess(message: string, title: string) {
    this.toast.success(message, title, 3000, false);
  }

  private showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }
}
