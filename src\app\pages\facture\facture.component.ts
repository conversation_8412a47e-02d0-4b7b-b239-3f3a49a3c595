import { Component, HostListener, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Subscription } from '@app/core/models/subscription';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { Client } from '@app/core/models/client';
import { Licence } from '@app/core/models/licence';
import { trigger, transition, style, animate } from '@angular/animations';
import { environment } from '@app/environments/environment';
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS,
} from 'ng-angular-popup';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
import { PageEvent, MatPaginatorModule } from '@angular/material/paginator';
import { Lister } from '@app/shared/models/rule/Lister';
import { NgxLoadingModule } from 'ngx-loading';
import { WhereParams } from '@app/shared/models/rule/WhereParams';
import { Pagination } from '@app/shared/models/rule/Pagination';
import { Sorting } from '@app/shared/models/rule/Sorting'; // Import your sorting type
import { FactureApiService } from '@app/core/services/administrative/facture.service';
import { Facture } from '@app/core/models/facture';
import { VwFactureDetails } from '@app/shared/models/vw_.FactureDetails';

interface InvoiceData {
  date: string;
  dueDate: string;
  invoiceNumber: string;
  recipient: {
    name: string;
    email: string;
    address: string;
    cityStateZip: string;
  };
  items: {
    description: string;
    unitPrice: number;
    total: number;
  }[];
}

@Component({
  selector: 'app-facture',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgToastComponent,
    MatPaginatorModule,
    NgxLoadingModule,
  ],
  templateUrl: './facture.component.html',
  styleUrls: ['./facture.component.css'],
  animations: [
    trigger('slideInRight', [
      transition(':enter', [
        style({ transform: 'translateX(100%)', opacity: 0 }),
        animate(
          '400ms cubic-bezier(0.23, 1, 0.32, 1)',
          style({ transform: 'translateX(0)', opacity: 1 })
        ),
      ]),
      transition(':leave', [
        animate(
          '300ms cubic-bezier(0.23, 1, 0.32, 1)',
          style({ transform: 'translateX(100%)', opacity: 0 })
        ),
      ]),
    ]),
  ],
})
export class FactureComponent implements OnInit {
  private apiUrl = environment.host + '/api/Pdf';

  lister: Lister<any> = {
    Pagination: {
      CurrentPage: 1,
      PageSize: 5,
      TotalElement: 0,
      PageCount: 0,
      IsFirst: true,
      IsLast: false,
      StartIndex: 0,
    },
    FilterParams: [] as WhereParams[],
    SortParams: [] as Sorting[],
  };

  clients: Client[] = [];
  licences: Licence[] = [];
  subscriptions: Subscription[] = [];
  allSubscriptionsData: Subscription[] = [];
  filteredInvoices: VwFactureDetails[] = [];
  allInvoices: VwFactureDetails[] = [];
  searchQuery: string = '';
  statusFilter: string = 'All';
  selectedInvoice: VwFactureDetails | { multi: true } | null = null;
  invoiceDetail: any = null;
  dropdownOpen: string | null = null;
  selectedInvoices: Set<string> = new Set();
  allSelected: boolean = false;
  isLoading: boolean = true;
  isDownloading: boolean = false;

  private originalFactures: Map<string, Facture> = new Map();

  summary = {
    Paid: 0,
    Pending: 0,
    Total: 0,
  };

  currentPage = 0;
  pageSize = 5;
  totalInvoices: number = 0;
  totalSubscriptions: number = 0;
  searchParam: string = '';
  hasSearchFilter: boolean = false;

  constructor(
    private clientApiService: ClientApiService,
    private licenceApiService: LicenceApiService,
    private subscriptionApiService: SubscriptionApiService,
    private facturenApiService: FactureApiService,
    private http: HttpClient,
    private toast: NgToastService
  ) {}

  ngOnInit() {
    this.loadData();
    this.loadSummary();
  }

  async loadData() {
    this.isLoading = true;
    try {
      // Step 1: Fetch current page of factures first
      await this.fetchFacturesPage();

      // Step 2: Get only the related data we need for this page
      await this.fetchRelatedDataForCurrentPage();

      this.applyFiltersAndPagination();
    } catch (error) {
      this.showError('Erreur lors du chargement des données', 'Erreur');
      console.error('loadData error:', error);
    } finally {
      this.isLoading = false;
    }
  }

  canShowPayButton(): boolean {
    return (
      this.invoiceDetail &&
      !this.invoiceDetail.isMultiple &&
      this.invoiceDetail.status === 'En attente'
    );
  }

  createDefaultLister<T>(): Lister<T> {
    return {
      Pagination: {
        CurrentPage: this.currentPage,
        PageSize: this.pageSize,
        TotalElement: 0,
        PageCount: 0,
        IsFirst: true,
        IsLast: false,
        StartIndex: 0,
      },
      FilterParams: [],
      SortParams: [],
    };
  }

  async fetchRelatedDataForCurrentPage(): Promise<void> {
    if (this.allInvoices.length === 0) {
      this.clients = [];
      this.licences = [];
      this.subscriptions = [];
      return;
    }

    try {
      // Step 2: Get unique subscription IDs from current page factures
      const subscriptionIds = [
        ...new Set(
          this.allInvoices
            .map((invoice) => invoice.subscriptionId)
            .filter((id) => id != null)
        ),
      ];

      // Step 3: Fetch only the subscriptions we need
      if (subscriptionIds.length > 0) {
        await this.fetchSubscriptionsByIds(subscriptionIds);

        // Step 4: Get unique client and licence IDs from the fetched subscriptions
        const clientIds = [
          ...new Set(
            this.subscriptions
              .map((sub) => sub.ClientId)
              .filter((id) => id != null)
          ),
        ];

        const licenceIds = [
          ...new Set(
            this.subscriptions
              .map((sub) => sub.LicenceId)
              .filter((id) => id != null)
          ),
        ];

        // Step 5: Fetch only the clients and licences we need
        await Promise.all([
          this.fetchClientsByIds(clientIds),
          this.fetchLicencesByIds(licenceIds),
        ]);
      } else {
        this.clients = [];
        this.licences = [];
        this.subscriptions = [];
      }
    } catch (error) {
      console.error('Error fetching related data:', error);
      // Set empty arrays as fallback
      this.clients = [];
      this.licences = [];
      this.subscriptions = [];
    }
  }

  fetchSubscriptionsByIds(subscriptionIds: string[]): Promise<void> {
    if (subscriptionIds.length === 0) {
      this.subscriptions = [];
      return Promise.resolve();
    }

    // Create filter parameters for the subscription IDs
    const filterParams: WhereParams[] = subscriptionIds.map((id, index) => ({
      Column: 'Id',
      Op: 'eq',
      Value: id,
      AndOr: index === 0 ? 'AND' : 'OR',
    }));

    const lister: Lister<any> = {
      Pagination: {
        CurrentPage: 0,
        PageSize: subscriptionIds.length, // Get all matching subscriptions
        TotalElement: 0,
        PageCount: 0,
        IsFirst: true,
        IsLast: false,
        StartIndex: 0,
      },
      FilterParams: filterParams,
      SortParams: [],
    };

    return new Promise((resolve) => {
      this.subscriptionApiService.gatePage(lister).subscribe({
        next: (page) => {
          this.subscriptions = page.Content ?? [];
          resolve();
        },
        error: (error) => {
          console.error('Error fetching subscriptions by IDs:', error);
          this.subscriptions = [];
          resolve();
        },
      });
    });
  }

  fetchClientsByIds(clientIds: string[]): Promise<void> {
    if (clientIds.length === 0) {
      this.clients = [];
      return Promise.resolve();
    }

    // Create filter parameters for the client IDs
    const filterParams: WhereParams[] = clientIds.map((id, index) => ({
      Column: 'Id',
      Op: 'eq',
      Value: id,
      AndOr: index === 0 ? 'AND' : 'OR',
    }));

    const lister: Lister<any> = {
      Pagination: {
        CurrentPage: 0,
        PageSize: clientIds.length, // Get all matching clients
        TotalElement: 0,
        PageCount: 0,
        IsFirst: true,
        IsLast: false,
        StartIndex: 0,
      },
      FilterParams: filterParams,
      SortParams: [],
    };

    return new Promise((resolve) => {
      this.clientApiService.gatePage(lister).subscribe({
        next: (page) => {
          this.clients = page.Content ?? [];
          resolve();
        },
        error: (error) => {
          console.error('Error fetching clients by IDs:', error);
          this.clients = [];
          resolve();
        },
      });
    });
  }

  fetchLicencesByIds(licenceIds: string[]): Promise<void> {
    if (licenceIds.length === 0) {
      this.licences = [];
      return Promise.resolve();
    }

    // Create filter parameters for the licence IDs
    const filterParams: WhereParams[] = licenceIds.map((id, index) => ({
      Column: 'Id',
      Op: 'eq',
      Value: id,
      AndOr: index === 0 ? 'AND' : 'OR',
    }));

    const lister: Lister<any> = {
      Pagination: {
        CurrentPage: 0,
        PageSize: licenceIds.length, // Get all matching licences
        TotalElement: 0,
        PageCount: 0,
        IsFirst: true,
        IsLast: false,
        StartIndex: 0,
      },
      FilterParams: filterParams,
      SortParams: [],
    };

    return new Promise((resolve) => {
      this.licenceApiService.gatePage(lister).subscribe({
        next: (page) => {
          this.licences = page.Content ?? [];
          resolve();
        },
        error: (error) => {
          console.error('Error fetching licences by IDs:', error);
          this.licences = [];
          resolve();
        },
      });
    });
  }

  fetchFacturesPage(): Promise<void> {
    const pagination: Pagination = {
      CurrentPage: this.currentPage,
      PageSize: this.pageSize,
      PageCount: 0,
      IsLast: false,
      IsFirst: false,
      StartIndex: 0,
      TotalElement: 0,
    };

    const filterParams: WhereParams[] = [];

    if (this.statusFilter !== 'All') {
      filterParams.push({
        Column: 'Status',
        Op: 'eq',
        Value: this.statusFilter,
        AndOr: 'AND',
      });
    }

    if (this.searchQuery.trim() !== '') {
      filterParams.push({
        Column: 'Client.Name',
        Op: 'contains',
        Value: this.searchQuery.trim(),
        AndOr: 'AND',
      });
    }

    const lister: Lister<any> = {
      Pagination: pagination,
      FilterParams: filterParams,
      SortParams: [],
    };

    return new Promise((resolve) => {
      this.facturenApiService.gatePage(lister).subscribe({
        next: (response) => {
          const factures: Facture[] = response.Content ?? [];

          // Store original factures for later use
          this.originalFactures.clear();
          factures.forEach((f) => {
            if (f.Id) {
              this.originalFactures.set(f.Id, f);
            }
          });

          // Update pagination info from server response
          if (response.Lister?.Pagination) {
            this.totalInvoices = response.Lister.Pagination.TotalElement ?? 0;
            this.lister.Pagination = {
              CurrentPage: response.Lister.Pagination.CurrentPage ?? 0,
              PageSize: response.Lister.Pagination.PageSize ?? 0,
              TotalElement: response.Lister.Pagination.TotalElement ?? 0,
              PageCount: response.Lister.Pagination.PageCount ?? 0,
              IsFirst: response.Lister.Pagination.IsFirst ?? false,
              IsLast: response.Lister.Pagination.IsLast ?? false,
              StartIndex: response.Lister.Pagination.StartIndex ?? 0,
            };
          } else {
            this.totalInvoices = factures.length;
          }

          this.allInvoices = factures.map(
            (f, idx): VwFactureDetails => ({
              factureId: f.Id,
              number: f.Number,
              status: f.Status,
              total: f.Total,
              createdAt: f.CreatedAt
                ? new Date(f.CreatedAt).toISOString()
                : undefined,
              subscriptionId: f.SubscriptionId,
              DateCreation: f.DateFacture,
              DateEcheance: f.DateEcheance,
              DatePaiement: f.DatePaiement,
              dateDebut: null,
              dateFin: null,
              price: undefined,
              paymentFrequency: undefined,
              clientId: undefined,
              clientName: undefined,
              clientCity: undefined,
              licenceId: undefined,
              licenceName: undefined,
            })
          );

          resolve();
        },
        error: (err) => {
          console.error('Error fetching factures:', err);
          this.showError('Échec du chargement des factures', 'Erreur');
          this.filteredInvoices = [];
          this.totalInvoices = 0;
          resolve();
        },
      });
    });
  }

  fetchClients(): Promise<void> {
    const safeFilterParams = this.buildFilterParams().filter(
      (param) =>
        param.Value !== null && param.Value !== undefined && param.Value !== ''
    );

    const lister: Lister<any> = {
      Pagination: {
        CurrentPage: this.currentPage,
        PageSize: this.pageSize,
        TotalElement: 0,
        PageCount: 0,
        IsFirst: true,
        IsLast: false,
        StartIndex: 0,
      },
      FilterParams: safeFilterParams.map((f) => ({
        Column: f.Column,
        Op: f.Op,
        Value: f.Value,
        AndOr: f.AndOr,
      })),
      SortParams: [],
    };

    return new Promise((resolve) => {
      this.clientApiService.gatePage(lister).subscribe({
        next: (page) => {
          this.clients = page.Content ?? [];
          resolve();
        },
        error: (error) => {
          console.error('Error fetching clients:', error);
          resolve();
        },
      });
    });
  }

  fetchLicences(): Promise<void> {
    const lister: Lister<any> = {
      Pagination: {
        CurrentPage: this.currentPage,
        PageSize: this.pageSize,
        TotalElement: 0,
        PageCount: 0,
        IsFirst: true,
        IsLast: false,
        StartIndex: 0,
      },
      FilterParams: [],
      SortParams: [],
    };
    return new Promise((resolve) => {
      this.licenceApiService.gatePage(lister).subscribe({
        next: (page) => {
          this.licences = page.Content ?? [];
          resolve();
        },
        error: (error) => {
          console.error('Error fetching licences:', error);
          resolve();
        },
      });
    });
  }

  fetchSubscriptions(): Promise<void> {
    const lister: Lister<any> = {
      Pagination: {
        CurrentPage: this.currentPage,
        PageSize: this.pageSize,
        TotalElement: 0,
        PageCount: 0,
        IsFirst: true,
        IsLast: false,
        StartIndex: 0,
      },
      FilterParams: [],
      SortParams: [],
    };

    return new Promise((resolve) => {
      this.subscriptionApiService.gatePage(lister).subscribe({
        next: (page) => {
          this.isLoading = false;
          this.subscriptions = page.Content ?? [];
          resolve();
        },
        error: (err) => {
          console.error('Error fetching subscriptions:', err);
          resolve();
        },
      });
    });
  }

  applyFiltersAndPagination() {
    // Now populate the related data for each invoice
    this.allInvoices = this.allInvoices.map((invoice) => {
      const sub = this.subscriptions.find(
        (s) => s.Id === invoice.subscriptionId
      );
      const client = this.clients.find((c) => c.Id === sub?.ClientId);
      const licence = this.licences.find((l) => l.Id === sub?.LicenceId);

      return {
        ...invoice,
        dateDebut: sub?.DateDebut ? new Date(sub.DateDebut) : null,
        dateFin: sub?.DateFin ? new Date(sub.DateFin) : null,
        price: sub?.Price === null ? undefined : sub?.Price,
        paymentFrequency: sub?.PaymentFrequency ?? undefined,
        clientId: client?.Id,
        clientName: client?.Name,
        clientCity: client?.City,
        licenceId: licence?.Id,
        licenceName: licence?.Name,
      };
    });

    // Since we're doing server-side filtering, no need for additional client-side filtering
    this.filteredInvoices = this.allInvoices;

    // Update selection state
    this.updateSelectAllState();
  }

  private buildFilterParams(): WhereParams[] {
    const filters: WhereParams[] = [];

    if (this.statusFilter !== 'All') {
      filters.push({
        Column: 'Facture.Status',
        Op: 'eq',
        Value: this.statusFilter,
        AndOr: 'AND',
      });
    }

    return filters;
  }

  generateAllInvoices() {}

  updateFilteredInvoices() {
    this.currentPage = 0;
    this.loadData();
  }

  onPageChange(event: PageEvent) {
    this.isLoading = true;
    this.selectedInvoices.clear();
    this.pageSize = event.pageSize;
    this.currentPage = event.pageIndex;

    // Load complete data including related entities
    this.loadData();
  }

  searchInvoices(): void {
    this.isLoading = true;
    this.currentPage = 0; // Reset to first page
    this.selectedInvoices.clear();

    // Load complete data including related entities
    this.loadData().then(() => {
      this.hasSearchFilter = this.searchQuery.trim() !== '';
    });
  }

  onSearchKeyup(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.searchInvoices();
    } else if (event.key === 'Backspace' && this.searchQuery === '') {
      this.clearSearch();
    }
  }

  clearSearch(): void {
    this.searchQuery = '';
    this.currentPage = 0;
    this.statusFilter = 'All';
    this.selectedInvoices.clear();
    this.hasSearchFilter = false;

    this.loadData();
  }

  formatDate(date: Date | string | null): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('fr-FR');
  }

  private convertToInvoiceData(invoice: VwFactureDetails): InvoiceData {
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 30);

    const relatedSubscription = this.subscriptions.find(
      (s) => s.Id === invoice.subscriptionId
    );
    const relatedClient = this.clients.find(
      (c) => c.Id === relatedSubscription?.ClientId
    );
    const relatedLicence = this.licences.find(
      (l) => l.Id === relatedSubscription?.LicenceId
    );

    return {
      date: invoice.createdAt
        ? new Date(invoice.createdAt).toLocaleDateString('fr-FR')
        : new Date().toLocaleDateString('fr-FR'),
      dueDate: dueDate.toLocaleDateString('fr-FR'),
      invoiceNumber: invoice.number || `FA${invoice.factureId || 'UNKNOWN'}`,
      recipient: {
        name: invoice.clientName || relatedClient?.Name || 'Client inconnu',
        email: relatedClient?.ContactEmail || '_',
        address: relatedClient?.Address || 'Adresse non disponible',
        cityStateZip:
          invoice.clientCity || relatedClient?.City
            ? `${invoice.clientCity || relatedClient?.City}, ${
                relatedClient?.BusinessSector || ''
              }`
            : 'Ville non disponible',
      },
      items: [
        {
          description: invoice.licenceName || relatedLicence?.Name || 'Licence',
          unitPrice: invoice.price || invoice.total || 0,
          total: invoice.price || invoice.total || 0,
        },
      ],
    };
  }

  async downloadInvoice(invoice: any) {
    try {
      this.isDownloading = true;
      const invoiceData = this.convertToInvoiceData(invoice);

      const response = await this.http
        .post(`${this.apiUrl}/generate-custom`, invoiceData, {
          responseType: 'blob',
          headers: new HttpHeaders({
            'Content-Type': 'application/json',
          }),
        })
        .toPromise();

      if (response) {
        this.downloadBlob(response, `facture-${invoice.factureId}.pdf`);
      }
    } catch (error) {
      console.error('Error downloading invoice:', error);
      this.showError('Erreur lors du téléchargement de la facture', 'Erreur');
    } finally {
      this.isDownloading = false;
      this.dropdownOpen = null;
    }
  }

  async downloadSelected() {
    if (this.selectedInvoices.size === 0) return;

    try {
      this.isDownloading = true;
      const selectedInvoicesList = this.filteredInvoices.filter((invoice) =>
        this.selectedInvoices.has(invoice.factureId!)
      );

      if (selectedInvoicesList.length === 1) {
        const invoice = selectedInvoicesList[0];
        const invoiceData = this.convertToInvoiceData(invoice);

        const response = await this.http
          .post(`${this.apiUrl}/generate-custom`, invoiceData, {
            responseType: 'blob',
            headers: new HttpHeaders({
              'Content-Type': 'application/json',
            }),
          })
          .toPromise();

        if (response) {
          saveAs(response, `facture-${invoice.factureId}.pdf`);
        }
        this.showSuccess('Téléchargement réussi', 'Succès');
      } else {
        const zip = new JSZip();

        for (const invoice of selectedInvoicesList) {
          const invoiceData = this.convertToInvoiceData(invoice);

          const response = await this.http
            .post(`${this.apiUrl}/generate-custom`, invoiceData, {
              responseType: 'blob',
              headers: new HttpHeaders({
                'Content-Type': 'application/json',
              }),
            })
            .toPromise();

          if (response) {
            zip.file(`facture-${invoice.factureId}.pdf`, response);
          }

          await new Promise((resolve) => setTimeout(resolve, 500));
        }

        this.showSuccess('Téléchargement réussi', 'Succès');
        const zipBlob = await zip.generateAsync({ type: 'blob' });
        saveAs(zipBlob, 'factures.zip');
      }
    } catch (error) {
      console.error('Error downloading selected invoices:', error);
      this.showError(
        'Erreur lors du téléchargement des factures sélectionnées',
        'Erreur'
      );
    } finally {
      this.isDownloading = false;
    }
  }

  private downloadBlob(blob: Blob, filename: string) {
    saveAs(blob, filename);
  }

  loadSummary() {
    this.facturenApiService.getTotalFacture().subscribe({
      next: (res: any) => {
        console.log('Raw summary response:', res);

        if (res) {
          this.summary = {
            Paid: res['Payé'] ?? 0,
            Pending: res['En attente'] ?? 0,
            Total: res['Total'] ?? 0,
          };
          console.log('Summary loaded:', this.summary);
        }
      },
      error: (err) => {
        console.error('Error fetching summary:', err);
      },
    });
  }

  get selectedTotal(): number {
    return this.filteredInvoices
      .filter((invoice) => this.selectedInvoices.has(invoice.factureId!))
      .reduce((sum, invoice) => sum + (invoice.price || 0), 0);
  }

  closeDetail() {
    this.selectedInvoice = null;
    this.invoiceDetail = null;
  }

  payNow() {
    if (
      this.selectedInvoice &&
      'factureId' in this.selectedInvoice &&
      this.invoiceDetail
    ) {
      this.activateInvoice(this.selectedInvoice);
    } else {
      this.showError('Aucune facture sélectionnée', 'Erreur');
    }
  }

  toggleDropdown(event: Event, invoiceId: string) {
    event.stopPropagation();
    this.dropdownOpen = this.dropdownOpen === invoiceId ? null : invoiceId;
  }

  @HostListener('document:click', ['$event'])
  onClick(event: MouseEvent) {
    if (!(event.target as HTMLElement).closest('.action-dropdown')) {
      this.dropdownOpen = null;
    }
  }

  previewInvoice(invoice: VwFactureDetails) {
    console.log('Preview invoice:', invoice.factureId);
    this.selectedInvoice = invoice;
    this.invoiceDetail = this.getInvoiceDetail(invoice);
    this.dropdownOpen = null;
  }

  activateInvoice(invoice: VwFactureDetails) {
    console.log('Activate invoice:', invoice.factureId);

    if (!invoice.factureId) {
      this.showError('Aucune facture trouvée pour cet élément', 'Erreur');
      return;
    }

    const originalFacture = this.originalFactures.get(invoice.factureId);
    if (!originalFacture) {
      this.showError('Aucune facture trouvée pour cet élément', 'Erreur');
      return;
    }

    // Find the original subscription
    const originalSubscription = this.subscriptions.find(s => s.Id === invoice.subscriptionId);
    if (!originalSubscription) {
      this.showError('Aucun abonnement trouvé pour cette facture', 'Erreur');
      return;
    }

    const updatedFacture: Facture = {
      ...originalFacture,
      Status: 'Payé',
      DatePaiement: new Date(),
    };

    const updatedSubscription: Subscription = {
      ...originalSubscription,
      Status: 'Payé'
    };

    console.log('Updating facture:', updatedFacture);

    this.facturenApiService.update(updatedFacture).subscribe({
      next: () => {
        invoice.status = 'Payé';
        invoice.DatePaiement = updatedFacture.DatePaiement;
        this.originalFactures.set(invoice.factureId!, updatedFacture);
        this.loadData();
        this.loadSummary();
        this.showSuccess('Facture activée avec succès', 'Succès');
      },
      error: (error) => {
        console.error('Error updating facture status:', error);
        this.showError("Erreur lors de l'activation de la facture", 'Erreur');
      },
    });

    this.subscriptionApiService.update(updatedSubscription).subscribe({
      next: () => {
        this.showSuccess('Abonnement activé avec succès', 'Succès');
      },
      error: (error) => {
        console.error('Error updating subscription status:', error);
        this.showError('Erreur lors de l\'activation de l\'abonnement', 'Erreur');
      },
    });

    this.dropdownOpen = null;
  }

  toggleSelectInvoice(invoiceId: string, event: Event) {
    event.stopPropagation();

    if (this.selectedInvoices.has(invoiceId)) {
      this.selectedInvoices.delete(invoiceId);
    } else {
      this.selectedInvoices.add(invoiceId);
    }

    this.updateSelectAllState();
  }

  toggleSelectAll(event: Event) {
    const checkbox = event.target as HTMLInputElement;
    this.allSelected = checkbox.checked;

    if (this.allSelected) {
      this.selectedInvoices.clear();
      this.filteredInvoices.forEach((invoice) => {
        this.selectedInvoices.add(invoice.factureId!);
      });
    } else {
      this.selectedInvoices.clear();
    }
  }

  updateSelectAllState() {
    if (this.filteredInvoices.length === 0) {
      this.allSelected = false;
      return;
    }

    this.allSelected = this.filteredInvoices.every((invoice) =>
      this.selectedInvoices.has(invoice.factureId!)
    );
  }

  getInvoiceDetail(invoice: VwFactureDetails): any {
    const relatedSubscription = this.subscriptions.find(
      (s) => s.Id === invoice.subscriptionId
    );
    const relatedClient = this.clients.find(
      (c) => c.Id === relatedSubscription?.ClientId
    );
    const relatedLicence = this.licences.find(
      (l) => l.Id === relatedSubscription?.LicenceId
    );

    const items = [
      {
        description: relatedLicence?.Name || invoice.licenceName || 'Service',
        price: invoice.price || invoice.total || 0,
        amount: invoice.price || invoice.total || 0,
      },
    ];

    const subtotal = items.reduce((sum, item) => sum + (item.amount || 0), 0);
    const total = subtotal;

    return {
      clientName: relatedClient?.Name || invoice.clientName || 'Client inconnu',
      date: invoice.createdAt ? new Date(invoice.createdAt) : new Date(),
      subject: `Facture pour ${
        relatedLicence?.Name || invoice.licenceName || 'Service'
      }`,
      billTo: relatedClient?.Name || invoice.clientName || 'Client inconnu',
      currency: 'EUR',
      items: items,
      subtotal: subtotal,
      total: total,
      status: invoice.status,
      factureId: invoice.factureId,
      number: invoice.number,
      DatePaiement: invoice.DatePaiement,
    };
  }

  previewSelected() {
    if (this.selectedInvoices.size === 0) return;

    const selected = this.filteredInvoices.filter((invoice) =>
      this.selectedInvoices.has(invoice.factureId!)
    );

    if (selected.length === 1) {
      this.previewInvoice(selected[0]);
    } else if (selected.length > 1) {
      this.selectedInvoice = { multi: true };
      this.invoiceDetail = this.getMultiInvoiceDetail(selected);
    }
  }

  getMultiInvoiceDetail(invoices: VwFactureDetails[]): any {
    const items = invoices.map((invoice) => {
      const relatedSubscription = this.subscriptions.find(
        (s) => s.Id === invoice.subscriptionId
      );
      const relatedClient = this.clients.find(
        (c) => c.Id === relatedSubscription?.ClientId
      );
      const relatedLicence = this.licences.find(
        (l) => l.Id === relatedSubscription?.LicenceId
      );

      return {
        description: `${
          relatedClient?.Name || invoice.clientName || 'Client'
        } - ${relatedLicence?.Name || invoice.licenceName || 'Service'}`,
        price: invoice.price || invoice.total || 0,
        amount: invoice.price || invoice.total || 0,
      };
    });

    const subtotal = items.reduce((sum, item) => sum + (item.amount || 0), 0);
    const total = subtotal;

    return {
      clientName: 'Clients multiples',
      date: new Date(),
      subject: 'Factures groupées',
      billTo: 'Clients multiples',
      currency: 'EUR',
      items: items,
      subtotal: subtotal,
      total: total,
      isMultiple: true,
    };
  }

  activateSelected() {
    if (this.selectedInvoices.size === 0) return;
    console.log(
      'Activating selected invoices:',
      Array.from(this.selectedInvoices)
    );
  }

  public showSuccess(message: string, title: string) {
    this.toast.info(message, title, 3000, false);
  }

  public showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }

  protected readonly TOAST_POSITIONS = TOAST_POSITIONS;
}
