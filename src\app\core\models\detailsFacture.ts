import { Facture } from "./facture";
import { Subscription } from "./subscription";
import { AuditModel } from "./models-audit/audit-model";
import { SubscribedOptions } from "./subscribedoptions";

export class DetailsFacture extends AuditModel{
    Price!: number;
    FactureId!: string;
    SubscriptionId!: string;
    SubscribedOptions!: SubscribedOptions;
    SubscribedOptionsId!: string;
    Subscription!: Subscription;
    Facture!: Facture;
}