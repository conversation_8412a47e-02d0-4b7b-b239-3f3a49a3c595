import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { Controller } from '@app/core/models/controller';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ClientLicenceControllerView } from '@app/shared/models/clientLicenceControllerView';
import { Lister } from '@app/core/models/util/page';

@Injectable({ providedIn: 'root' })
export class ControllerApiService extends ApiService<Controller> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('controller');
  }

  setBaseTopic(id: string): Observable<any> {
    return this.http.put<any>(
      `${this.baseUrl}controller/set-base-topic/${id}`,
      {}
    );
  }

  getAllLightByClient(clientId: string): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}controller/get-light-by-client/${clientId}`,
      {}
    );
  }

  canAddController(subscriptionId: string) {
    return this.http.get<boolean>(
      `${this.baseUrl}controller/subscription/${subscriptionId}/can-add`,
      {}
    );
  }
  getControllersByLocal(localId: string): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}controller/local/${localId}`,
      {}
    );
  }

  getClientControllers(
    clientId: string
  ): Observable<ClientLicenceControllerView[]> {
    const url = `${this.baseUrl}controller/client-controllers/${clientId}`;
    return this.http.get<ClientLicenceControllerView[]>(url);
  }

  getPaginatedClientControllers(clientId: string, lister: Lister) {
    return this.http.post<ClientLicenceControllerView[]>(
      `${this.baseUrl}Controller/paginate-controllers?clientId=${clientId}`,
      lister
    );
  }

  generateName(): Observable<string> {
    return this.http.get<string>(
      `${this.baseUrl}controller/generate-code`
    );
  }
}
