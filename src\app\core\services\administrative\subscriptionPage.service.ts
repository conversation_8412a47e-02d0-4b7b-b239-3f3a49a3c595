// subscriptionPage.service.ts - Enhanced
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiService } from '../api.service';
import { Observable } from 'rxjs';
import { Lister, Page } from '@app/core/models/util/page';
import { environment } from '@app/environments/environment';
import { SubscriptionWithDetails } from '@app/core/models/subscriptionPage';

@Injectable({ providedIn: 'root' })
export class SubscriptionPageApiService extends ApiService<SubscriptionWithDetails> {
  private apiUrl = `${environment.host}/api/subscription/summary`;
  
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('subscription/summary');
  }

  // Enhanced getPage method with better error handling
  getPage(lister: Lister): Observable<Page<SubscriptionWithDetails>> {
    return this.gatePage(lister);
  }

  // NEW: Get all subscriptions for filter extraction (fallback method)
  getAllForFilters(): Observable<SubscriptionWithDetails[]> {
    return this.http.get<SubscriptionWithDetails[]>(`${this.apiUrl}/all`);
  }
}