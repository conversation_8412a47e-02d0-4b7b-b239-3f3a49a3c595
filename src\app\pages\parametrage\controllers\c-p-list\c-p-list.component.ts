import {
  Component,
  Input,
  OnInit,
  OnChang<PERSON>,
  <PERSON>Chang<PERSON>,
  EventEmitter,
  Output,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { ControllerApiService } from '@app/core/services/administrative/controller.service';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { ClientLicenceControllerView } from '@app/shared/models/clientLicenceControllerView';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { Controller } from '@app/core/models/controller';
import { MatIconModule } from '@angular/material/icon';
import { FormsModule } from '@angular/forms';
import { NgToastService } from 'ng-angular-popup';
import { Lister, Pagination } from '@app/core/models/util/page';
import { CPFormComponent } from '../c-p-form/c-p-form.component';
import { CPDetailsComponent } from '../c-p-details/c-p-details.component';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';

@Component({
  selector: 'app-c-p-list',
  imports: [
    CommonModule,
    MatPaginatorModule,
    GenericTableComponent,
    MatIconModule,
    FormsModule,
    MatIconModule,
    FormsModule,
    CPFormComponent,
    CPDetailsComponent,
    NgxUiLoaderModule
  ],
  templateUrl: './c-p-list.component.html',
  styleUrl: './c-p-list.component.css',
})
export class CPListComponent {
  @Input() clientId: string = '';
  @Output() controllerDeleted = new EventEmitter<string>();

  controllers: ClientLicenceControllerView[] = [];
  allControllersData: ClientLicenceControllerView[] = []; // Store all data for search
  displayedControllers: ClientLicenceControllerView[] = [];
  selectedControllerEdit: Controller | null = null;
  selectedController: ClientLicenceControllerView | null = null;
  selectedControllerDetails: Controller | null = null;
  showDetailsModal: boolean = false;
  showCreateForm: boolean = false;
  isEditMode: boolean = false;
  searchParam: string = '';
  hasSearchFilter: boolean = false;
  totalCount = 0;

  isLoading = false;

  // Pagination properties
  currentPage = 0;
  pageSize = 5;
  totalControllers = 0;

  // Table configuration
  headers: string[] = [
    'Nom',
    'Modèle',
    'Numéro de série',
    'Adresse MAC',
    'Adresse IP',
    'État',
  ];
  keys: string[] = [
    'HostName',
    'Model',
    'SerialNumber',
    'MacAddress',
    'IpAddress',
    'State',
  ];

  constructor(
    private controllerService: ControllerApiService,
    private dialog: MatDialog,
    readonly toast: NgToastService,
    private ngxUiLoaderService: NgxUiLoaderService
  ) {}

  public showSuccess(message: string, title: string) {
    this.toast.info(message, title, 3000, false);
  }
  public showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }

  ngOnInit() {
    this.loadControllers();
  }

  ngOnChanges(changes: SimpleChanges) {
    this.loadControllers();
  }

  loadControllers() {
    this.ngxUiLoaderService.start();
    const apiPage = this.currentPage + 1;
    const pagination: Pagination = {
      CurrentPage: apiPage,
      PageSize: this.pageSize,
      totalElement: 0,
    };

    const request: Lister = {
      Pagination: pagination,
    };

    if (this.searchParam.trim()) {
      request.FilterParams = [
        {
          Column: 'HostName',
          Value: this.searchParam,
          Op: 'contains',
          AndOr: 'AND',
        },
      ];
    }

    this.isLoading = true;

    this.controllerService.gatePage(request).subscribe({
      next: (response: any) => {
        this.isLoading = false;
        this.controllers = (response?.Content ?? []).map((controller: any) => ({
          ...controller,
          StateDisplay: this.getControllerStateDisplay(controller.State),
        }));
        console.log('controllers :', this.controllers);
        if (response?.Lister?.pagination?.totalElement !== undefined) {
          this.totalCount = response.Lister.pagination.totalElement;
        } else if (response?.Lister?.Pagination?.TotalElement !== undefined) {
          this.totalCount = response.Lister.Pagination.TotalElement;
        } else {
          this.totalCount = Math.max(8, this.controllers.length);
        }
        this.ngxUiLoaderService.stop();
      },
      error(err) {
        console.log('Un erreur est survenue lors de chargement de données');
      },
    });
    this.ngxUiLoaderService.stop();
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadControllers();
  }

  handleTableAction(event: { action: string; row: any }): void {
    const { action, row } = event;

    switch (action) {
      case 'view':
        this.viewControllerDetails(row);
        break;
      case 'edit':
        this.editController(row);
        break;
      case 'delete':
        this.deleteController(row);
        break;
      default:
        console.warn('Unknown action:', action);
    }
  }

  viewControllerDetails(controller: Controller): void {
    console.log('Viewing controller details:', controller);
    this.selectedControllerDetails = controller;
    this.showDetailsModal = true;
    document.body.classList.add('modal-open');
  }

  editController(controllerView: Controller): void {
    console.log('Editing controller:', controllerView);

    const controllerId = controllerView.Id || controllerView.Id;
    if (!controllerId) {
      console.error('No controller ID found');
      return;
    }

    // Fetch the full Controller object by ID
    this.controllerService.getById(controllerId).subscribe({
      next: (controller: Controller) => {
        this.selectedControllerEdit = controller;
        this.isEditMode = true;
        this.showCreateForm = true;
        document.body.classList.add('modal-open');
      },
      error: (error) => {
        console.error('Error fetching controller for edit:', error);
        this.dialog.open(ConfirmationDialogComponent, {
          width: '400px',
          data: {
            title: 'Erreur',
            message: 'Erreur lors de la récupération du contrôleur',
            confirmText: 'OK',
            cancelText: '',
            type: 'danger',
          },
        });
      },
    });
  }

  deleteController(controllerView: Controller): void {
    const controllerName = controllerView.HostName || 'ce contrôleur';
    const controllerId = controllerView.Id || controllerView.Id;

    if (!controllerId) {
      console.error('No controller ID found for deletion');
      return;
    }

    // Confirmation dialog
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmation de suppression',
        message: `Êtes-vous sûr de vouloir supprimer "${controllerName}" ?`,
        type: 'danger',
        icon: 'warning',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.performDelete(controllerId, controllerName);
      }
    });
  }

  private performDelete(controllerId: string, controllerName: string): void {
    this.controllerService.delete(controllerId).subscribe({
      next: () => {
        // Remove from all data arrays
        this.allControllersData = this.allControllersData.filter(
          (c) => (c.ControllerId || c.Id) !== controllerId
        );

        // Reapply search filter
        this.loadControllers();

        // Emit deletion event
        this.controllerDeleted.emit(controllerId);
        this.showSuccess('Contrôleur supprimé avec succès', 'Information');
      },
      error: (err) => {
        console.error('Error deleting controller:', err);
        this.showError(
          'Erreur lors de la suppression du contrôleur, Essayer ultérieurement',
          'Erreur'
        );
      },
    });
  }

  addNewController(): void {
    console.log('Adding new controller for client:', this.clientId);
    this.selectedControllerEdit = null;
    this.isEditMode = false;
    this.showCreateForm = true;
    document.body.classList.add('modal-open');
  }

  closeAllModals(): void {
    this.showCreateForm = false;
    this.showDetailsModal = false;
    this.isEditMode = false;
    this.selectedController = null;
    this.selectedControllerEdit = null;
    document.body.classList.remove('modal-open');
  }

  // Close details modal
  onDetailsClosed(): void {
    this.showDetailsModal = false;
    this.selectedController = null;
    document.body.classList.remove('modal-open');
  }

  onControllerCreated(newController: Controller): void {
    console.log('New controller created:', newController);

    this.loadControllers();
    this.onFormClosed();
  }

  onControllerUpdated(updatedController: Controller): void {
    console.log('Controller updated:', updatedController);

    this.loadControllers();
    this.onFormClosed();
  }

  // Helper method to transform controller state for display
  private getControllerStateDisplay(state: string): string {
    switch (state) {
      case 'Actif':
        return 'Actif';
      case 'Inactif':
      case 'InActif':
        return 'Inactif';
      case 'Installé':
        return 'Installé';
      case 'Non installé':
        return 'Non installé';
      case 'Suspendu':
        return 'Suspendu';
      case 'Endommagé':
      case 'Endomagé': // fallback for typo
        return 'Endommagé';
      case 'En cours de réparation':
        return 'En cours de réparation';
      case 'Réparé':
        return 'Réparé';
      case "En cours d'installation":
        return "En cours d'installation";
      case 'Désinstallé':
        return 'Désinstallé';
      default:
        return 'Inconnu';
    }
  }

  onFormClosed(): void {
    this.showCreateForm = false;
    this.isEditMode = false;
    this.selectedController = null;
    this.selectedControllerEdit = null;
    document.body.classList.remove('modal-open');
  }

  searchControllers(): void {
    this.currentPage = 0; // Reset to first page when searching
    this.loadControllers();
  }

  clearSearch(): void {
    console.log('Clearing search');
    this.searchParam = '';
    this.hasSearchFilter = false;
    this.loadControllers();
  }
}
