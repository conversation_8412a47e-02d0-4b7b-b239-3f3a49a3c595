<div class="zwave-container">
    <!-- Header with connection status -->
    <header class="header">
        <div class="connection-status">
            <div class="status-indicator" [class]="connectionStatus">
                <mat-icon class="status-dot">lens</mat-icon>
                {{ connectionStatusText }}
            </div>

            <!-- MQTT Connection Controls -->
            <div class="mqtt-controls">
                <button *ngIf="connectionStatus === 'disconnected'" class="btn btn-primary" (click)="connectToMqtt()">
                    <mat-icon class="btn-icon">wifi</mat-icon>
                    Connect to MQTT
                </button>

                <button *ngIf="connectionStatus === 'connected'" class="btn btn-secondary"
                    (click)="disconnectFromMqtt()">
                    <mat-icon class="btn-icon">wifi_off</mat-icon>
                    Disconnect
                </button>
            </div>

            <div *ngIf="lastError" class="error-message">
                <mat-icon class="error-icon">warning</mat-icon>
                {{ lastError.message }}
            </div>
        </div>

        <div class="controller-info" *ngIf="controllerInfo">
            <h2>Z-Wave Controller (via MQTT)</h2>
            <p>Home ID: {{ controllerInfo.homeId | number:'1.0-0' }}</p>
            <p>Node ID: {{ controllerInfo.ownNodeId }}</p>
            <p>{{ controllerInfo.nodes.length || 0 }} devices</p>
            <p class="mqtt-info">
                <mat-icon class="info-icon">info</mat-icon>
                Connected via MQTT Gateway
            </p>
        </div>
    </header>

    <!-- Device Management Controls -->
    <section class="management-controls">
        <div class="control-group">
            <h3>Device Management</h3>
            <div class="button-group">
                <button class="btn btn-primary" [disabled]="!isConnected || inclusionActive" (click)="startInclusion()">
                    <mat-icon class="btn-icon">add</mat-icon>
                    Add Device
                </button>

                <button class="btn btn-secondary" [disabled]="!isConnected || inclusionActive"
                    (click)="startExclusion()">
                    <mat-icon class="btn-icon">remove</mat-icon>
                    Remove Device
                </button>

                <button *ngIf="inclusionActive" class="btn btn-warning" (click)="stopCurrentOperation()">
                    <mat-icon class="btn-icon">stop</mat-icon>
                    Stop
                </button>

                <button class="btn btn-outline" [disabled]="!isConnected" (click)="healNetwork()">
                    <mat-icon class="btn-icon">build</mat-icon>
                    Heal Network
                </button>

                <button class="btn btn-outline" [disabled]="!isConnected" (click)="refreshDevices()">
                    <mat-icon class="btn-icon">refresh</mat-icon>
                    Refresh
                </button>
            </div>
        </div>

        <div *ngIf="inclusionState" class="inclusion-status">
            <div class="inclusion-indicator">
                <div class="spinner"></div>
                <span>{{ inclusionState.active ? 'Waiting for device...' : 'Ready' }}</span>
            </div>
            <p class="inclusion-help">
                Press the inclusion button on your Z-Wave device or follow the device manual.
                Commands are sent via MQTT to the Z-Wave gateway.
            </p>
        </div>
    </section>

    <!-- Connection Help -->
    <section *ngIf="connectionStatus === 'disconnected'" class="connection-help">
        <div class="help-card">
            <mat-icon class="help-icon">help_outline</mat-icon>
            <h3>MQTT Connection Required</h3>
            <p>
                To control Z-Wave devices, you need to connect to your MQTT broker.
                Make sure your Z-Wave JS UI gateway is configured and publishing to MQTT.
            </p>
            <ul>
                <li>Verify MQTT broker is running (e.g., Mosquitto)</li>
                <li>Check Z-Wave JS UI MQTT gateway settings</li>
                <li>Ensure WebSocket support is enabled on MQTT broker</li>
            </ul>
        </div>
    </section>

    <!-- Filters -->
    <section class="filters" *ngIf="isConnected">
        <div class="filter-row">
            <div class="search-box">
                <input type="text" placeholder="Search devices..." [(ngModel)]="filters.search"
                    (input)="onFilterChange()" class="search-input">
                <mat-icon class="search-icon">search</mat-icon>
            </div>

            <select [(ngModel)]="filters.status" (change)="onFilterChange()" class="filter-select">
                <option value="all">All Status</option>
                <option value="alive">Online</option>
                <option value="asleep">Sleeping</option>
                <option value="dead">Offline</option>
                <option value="unknown">Unknown</option>
            </select>

            <select [(ngModel)]="filters.deviceType" (change)="onFilterChange()" class="filter-select">
                <option value="">All Types</option>
                <option *ngFor="let type of deviceTypes" [value]="type">{{ type }}</option>
            </select>

            <label class="checkbox-label">
                <input type="checkbox" [(ngModel)]="filters.showOffline" (change)="onFilterChange()">
                Show Offline
            </label>
        </div>
    </section>

    <!-- Device List -->
    <section class="device-list" *ngIf="isConnected">
        <div *ngIf="filteredDevices.length === 0" class="empty-state">
            <mat-icon class="empty-icon">devices</mat-icon>
            <h3>No devices found</h3>
            <p *ngIf="devices.length === 0">Add your first Z-Wave device by clicking "Add Device" above.</p>
            <p *ngIf="devices.length > 0">Try adjusting your filters to see more devices.</p>
        </div>

        <div class="device-grid">
            <div *ngFor="let device of filteredDevices; trackBy: trackByNodeId" class="device-card"
                [class.offline]="device.status === 'dead'" [class.sleeping]="device.status === 'asleep'">

                <!-- Device Header -->
                <div class="device-header">
                    <div class="device-info">
                        <h4 class="device-name">{{ device.name }}</h4>
                        <span class="device-type">{{ device.deviceType }}</span>
                        <span class="node-id">Node {{ device.nodeId }}</span>
                        <span class="mqtt-indicator" title="Controlled via MQTT">
                            <mat-icon class="mqtt-icon">wifi</mat-icon>
                            MQTT
                        </span>
                    </div>

                    <div class="device-status">
                        <span class="status-badge" [class]="device.status">
                            {{ getStatusText(device.status) }}
                        </span>
                        <div *ngIf="device.batteryLevel !== undefined" class="battery">
                            <mat-icon class="battery-icon" [class.low]="device.batteryLevel < 20">battery_std</mat-icon>
                            <span class="battery-level">{{ device.batteryLevel }}%</span>
                        </div>
                    </div>
                </div>

                <!-- Device Location -->
                <div *ngIf="device.location" class="device-location">
                    <mat-icon class="location-icon">location_on</mat-icon>
                    {{ device.location }}
                </div>

                <!-- Quick Controls -->
                <div class="quick-controls" *ngIf="device.capabilities.canSwitch || device.capabilities.canDim">
                    <div class="control-group" *ngIf="device.capabilities.canSwitch">
                        <button class="control-btn" [class.active]="getSwitchValue(device)"
                            [disabled]="!isConnected || device.status === 'dead'" (click)="toggleSwitch(device)">
                            <mat-icon class="control-icon">lightbulb</mat-icon>
                            {{ getSwitchValue(device) ? 'ON' : 'OFF' }}
                        </button>
                    </div>

                    <div class="control-group" *ngIf="device.capabilities.canDim">
                        <label class="dimmer-label">Brightness</label>
                        <input type="range" min="0" max="99" [value]="getDimLevel(device)"
                            [disabled]="!isConnected || device.status === 'dead'" (change)="setDimLevel(device, $event)"
                            class="dimmer-slider">
                        <span class="dimmer-value">{{ getDimLevel(device) }}%</span>
                    </div>
                </div>

                <!-- Sensor Values -->
                <div *ngIf="getSensorValues(device).length > 0" class="sensor-values">
                    <h5>Sensor Data</h5>
                    <div class="sensor-grid">
                        <div *ngFor="let value of getSensorValues(device)" class="sensor-item">
                            <span class="sensor-label">{{ value.label }}</span>
                            <span class="sensor-value">
                                {{ value.value }}
                                <span *ngIf="value.unit" class="sensor-unit">{{ value.unit }}</span>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Device Actions -->
                <div class="device-actions">
                    <button class="action-btn" [disabled]="!isConnected" (click)="refreshDevice(device)"
                        title="Refresh device info via MQTT">
                        <mat-icon class="action-icon">refresh</mat-icon>
                        Refresh
                    </button>

                    <button class="action-btn" [disabled]="!isConnected" (click)="healDevice(device)"
                        title="Heal device routes via MQTT">
                        <mat-icon class="action-icon">build</mat-icon>
                        Heal
                    </button>

                    <button class="action-btn" [disabled]="!isConnected" (click)="showDeviceDetails(device)">
                        <mat-icon class="action-icon">info</mat-icon>
                        Details
                    </button>

                    <button *ngIf="device.status === 'dead'" class="action-btn danger"
                        (click)="removeFailedDevice(device)" title="Remove failed device via MQTT">
                        <mat-icon class="action-icon">delete</mat-icon>
                        Remove Failed
                    </button>
                </div>

                <!-- Last Seen -->
                <div *ngIf="device.lastSeen" class="last-seen">
                    Last seen: {{ device.lastSeen | date:'short' }}
                </div>
            </div>
        </div>
    </section>

    <!-- MQTT Status Footer -->
    <footer class="mqtt-status" *ngIf="isConnected">
        <div class="status-info">
            <mat-icon class="mqtt-icon">wifi</mat-icon>
            <span>Connected via MQTT • Real-time updates enabled</span>
        </div>
    </footer>

    <!-- NgxUiLoader for loading states -->
    <ngx-ui-loader></ngx-ui-loader>

    <!-- NgToast component for notifications -->
    <ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
</div>