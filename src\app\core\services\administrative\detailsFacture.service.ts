import { Injectable } from "@angular/core";
import { ApiService } from "../api.service";
import { HttpClient } from "@angular/common/http";
import { DetailsFacture } from "@app/core/models/detailsFacture";

@Injectable({ providedIn: 'root' })
export class detailsFactureApiService extends ApiService<DetailsFacture> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("details-facture");
  }

}