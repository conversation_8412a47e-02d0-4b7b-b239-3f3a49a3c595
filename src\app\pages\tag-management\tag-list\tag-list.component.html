<!-- Tag List Component -->
<div class="local-management-container">
  <!-- Header Section -->
  <div class="header-section">
    <div class="page-title">
      <h1 class="title">
        <mat-icon class="title-icon">local_offer</mat-icon> Liste des Tags
      </h1>
    </div>
    <div class="actions">
      <button class="create-button" (click)="showCreateTagForm()">
        <mat-icon class="action-icon">add</mat-icon> Créer Tag
      </button>
    </div>
  </div>

  <!-- Search Section -->
  <div class="search-bar">
    <input
      type="text"
      [value]="searchTerm"
      (input)="searchTerm = $any($event.target).value"
      placeholder="Rechercher un tag"
      (keyup)="onSearchKeyup($event)"
    />
    <button class="search-button" (click)="filterTags()">
      <mat-icon>search</mat-icon>
    </button>
  </div>

  <div class="loading-spinner" *ngIf="isLoading">Chargement...</div>

  <!-- Beautiful Tag Counts Table -->
  <div class="table-view" *ngIf="!isLoading">
    <div class="beautiful-table-container">
      <div class="table-header-gradient"></div>

      <!-- Table Header -->
      <div class="table-header">
        <div class="header-cell tag-name-header">
          <span>Nom du Tag</span>
        </div>
        <div class="header-cell assignments-header">
          <span>Affectations par Type</span>
        </div>
        <div class="header-cell total-header">
          <span>Affectations</span>
        </div>
        <div class="header-cell actions-header">
          <span>Actions</span>
        </div>
      </div>

      <!-- Table Body -->
      <div class="table-body">
        <div class="no-data-message" *ngIf="transformedTags.length === 0">
          <mat-icon class="no-data-icon">inbox</mat-icon>
          <h3>Aucun tag trouvé</h3>
          <p>Il n'y a actuellement aucun tag avec des affectations.</p>
        </div>

        <div class="table-row" *ngFor="let tag of transformedTags; let i = index"
             [class.even-row]="i % 2 === 0">

          <!-- Tag Name Column -->
          <div class="table-cell tag-name-cell" data-label="Nom du Tag">
            <div class="tag-name-content">
              <div class="tag-icon-wrapper">
                <mat-icon class="tag-icon">local_offer</mat-icon>
              </div>
              <div class="tag-info">
                <span class="tag-name">{{ tag.TagName }}</span>
              </div>
            </div>
          </div>

          <!-- Assignments Column -->
          <div class="table-cell assignments-cell" data-label="Affectations par Type">
            <div class="assignments-badges">
              <div class="badge-item client-badge">
                <mat-icon class="badge-icon">people</mat-icon>
                <span class="badge-label">Clients</span>
                <span class="badge-count">{{ tag.ClientCount || 0 }}</span>
              </div>

              <div class="badge-item site-badge">
                <mat-icon class="badge-icon">location_city</mat-icon>
                <span class="badge-label">Sites</span>
                <span class="badge-count">{{ tag.SiteCount || 0 }}</span>
              </div>

              <div class="badge-item local-badge">
                <mat-icon class="badge-icon">room</mat-icon>
                <span class="badge-label">Locaux</span>
                <span class="badge-count">{{ tag.LocalCount || 0 }}</span>
              </div>
            </div>
          </div>

          <!-- Total Column -->
          <div class="table-cell total-cell" data-label="Total">
            <div class="total-badge-wrapper">
              <div class="badge-item total-badge">
                <mat-icon class="badge-icon">analytics</mat-icon>
                <span class="badge-label">Total</span>
                <span class="badge-count">{{ tag.TotalAssignments }}</span>
              </div>
            </div>
          </div>

          <!-- Actions Column -->
          <div class="table-cell actions-cell" data-label="Actions">
            <div class="actions-wrapper">
              <button class="action-btn edit-btn"
                      (click)="triggerAction('edit', tag)"
                      title="Modifier le tag">
                <mat-icon>edit</mat-icon>
              </button>
              <button class="action-btn delete-btn"
                      (click)="triggerAction('delete', tag)"
                      title="Supprimer le tag">
                <mat-icon>delete</mat-icon>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div class="pagination-container">
      <mat-paginator
        [length]="totalCount"
        [pageSize]="pageSize"
        [pageIndex]="currentPage"
        [pageSizeOptions]="[5, 10, 25, 50]"
        (page)="onPageChange($event)"
        aria-label="Select page">
      </mat-paginator>
    </div>
  </div>

  <!-- Create/Edit Tag Popup -->
  <div class="popup-overlay" *ngIf="showTagForm" (click)="hideTagForm()">
    <div class="popup-form" (click)="$event.stopPropagation()">
      <div class="popup-header">
        <h3>
          <mat-icon>{{ tagForm.get('id')?.value ? 'edit' : 'add' }}</mat-icon>
          {{ tagForm.get('id')?.value ? 'Modifier Tag' : 'Créer Tag' }}
        </h3>
        <button class="close-btn" (click)="hideTagForm()">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <form [formGroup]="tagForm" (ngSubmit)="onSubmitTag()" class="site-form">
        <div class="validation-errors" *ngIf="tagForm.invalid && (tagForm.touched || tagForm.dirty)">
          <div class="validation-errors-title">
            <mat-icon>error_outline</mat-icon>
            Erreurs de validation
          </div>
          <ul class="validation-errors-list">
            <li *ngIf="tagForm.get('nom')?.invalid">
              <mat-icon>error</mat-icon>
              Le nom du tag est requis (minimum 2 caractères)
            </li>
          </ul>
        </div>

        <div class="form-grid">
          <div class="form-group full-width">
            <label for="tagName">Nom du Tag <span class="required">*</span></label>
            <input
              id="tagName"
              type="text"
              formControlName="nom"
              placeholder="Entrez le nom du tag"
              required
            />
          </div>
        </div>

        <div class="form-actions">
          <button type="button" class="btn-cancel" (click)="hideTagForm()">
            Annuler
          </button>
          <button type="submit" class="btn-submit" [disabled]="!tagForm.valid || isSubmitting">
            {{ isSubmitting ? 'Enregistrement...' : (tagForm.get('id')?.value ? 'Modifier' : 'Créer') }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
</div>
