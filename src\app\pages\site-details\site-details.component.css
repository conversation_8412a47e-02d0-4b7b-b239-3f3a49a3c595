.local-details-header {
  padding: 16px 24px;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 24px;
}
.site-container{
  max-width: 98%;
  margin-left: 50px; /* Add this line to push content right of the sidebar */
  padding: 24px 24px 24px 0; /* Optional: add some padding for top/right/bottom */
  box-sizing: border-box;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  background: #f3f4f6;
  border-radius: 6px;
  color: #4b5563;
  cursor: pointer;
  transition: all 0.2s;
}

.back-button:hover {
  background: #e5e7eb;
}

.separator {
  color: #94a3b8;
}

.site-name {
  color: #64748b;
  font-weight: 500;
}

.local-name {
  font-weight: 600;
  color: #0f172a;
}

.local-info-section {
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.info-grid {
  display: grid;
  gap: 24px;
}

.images-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.image-card {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.image-card h3 {
  padding: 16px;
  margin: 0;
  background: var(--primary);
  color: white;
  font-size: 16px;
}

.image-wrapper {
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

.image-wrapper img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #9e9e9e;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.detail-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border: 1px solid #e0e0e0;
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.detail-header h3 {
  margin: 0;
  font-size: 16px;
  color: #2c3e50;
}

.detail-header mat-icon {
  color: var(--primary);
}

.detail-content {
  padding: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
}

.detail-item .label {
  color: #757575;
  font-weight: 500;
}

.detail-item .value {
  color: #2c3e50;
}

.site-details-container {
  padding: 24px;
  margin: 0 auto;
}

.local-details-section {
  margin-bottom: 48px;
}

.breadcrumb-nav {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.architecture-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin: 24px 0;
}

.floor-plan-container {
  width: 100%;
  min-height: 400px;
  background: #f8fafc;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
  background: white;
  border-radius: 12px;
}

.architecture-wrapper {
  position: relative;
  width: 100%;
  height: 600px;
  background: #f8fafc;
  border-radius: 8px;
  overflow: hidden;
}

.architecture-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.device-icons-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.device-position-marker {
  position: absolute;
  transform: translate(-50%, -50%);
  cursor: grab;
  pointer-events: all;
  z-index: 10;
  transition: all 0.3s ease; /* Add smooth transition for initial placement */
  animation: popIn 0.3s ease-out;
}

/* Optional: Add animation for initial placement */
@keyframes popIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.device-position-marker.dragging {
  cursor: grabbing;
  z-index: 20;
}

.device-position-marker:hover .device-tooltip {
  opacity: 1;
}

.device-tooltip {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.2s;
  pointer-events: none;
}

.device-tooltip::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgba(0, 0, 0, 0.8);
}

/* Make sure device icons are properly sized */
app-device-icon {
  display: block;
  width: 32px;
  height: 32px;
  transition: transform 0.2s ease;
}

.device-position-marker:hover app-device-icon {
  transform: scale(1.1);
}

.status-panels {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 16px;
}

.devices-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin: 24px 0;
}

.logs-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;
  gap: 16px;
}

.error-container {
  color: #ef4444;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .images-container,
  .details-grid {
    grid-template-columns: 1fr;
  }

  .site-details-container {
    padding: 16px;
  }

  .status-panels {
    grid-template-columns: 1fr;
  }

  .devices-section {
    padding: 16px;
    margin: 16px 0;
  }

  .section-title {
    font-size: 1.1rem;
  }

  .architecture-section {
    padding: 16px;
    margin: 16px 0;
  }

  .floor-plan-container {
    min-height: 300px;
  }

  .architecture-wrapper {
    height: 400px;
  }

  app-device-icon {
    width: 24px;
    height: 24px;
  }
}

::ng-deep .ngx-ui-loader-overlay {
  position: fixed;
  z-index: 99999;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(2px);
}

::ng-deep .custom-spinner .la-ball-spin {
  color: var(--primary-color);
}

::ng-deep .ngx-ui-loader-text {
  margin-top: 15px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
}
