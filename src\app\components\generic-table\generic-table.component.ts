import {
  Component,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  AfterViewInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
// import { MatPaginator, MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-generic-table',
  templateUrl: './generic-table.component.html',
  styleUrls: ['./generic-table.component.css'],
  standalone: true,
  imports: [CommonModule, MatIconModule, MatMenuModule, MatButtonModule],
})
export class GenericTableComponent implements AfterViewInit {
  @Input() headers: string[] = [];
  @Input() keys: string[] = [];
  @Input() data: any[] = [];
  @Input() actions: string[] = [];
  @Output() actionTriggered = new EventEmitter<{ action: string; row: any }>();

  ngAfterViewInit(): void {}

  // Keep the action handling methods
  triggerAction(action: string, row: any): void {
    this.actionTriggered.emit({ action, row });
  }

  logRow(row: any): void {
    console.log('Row details:', row);
  }

  getActionIcon(action: string): string {
    switch (action) {
      case 'view':
        return 'visibility';
      case 'edit':
        return 'edit';
      case 'delete':
        return 'delete';
       case 'refresh':
        return 'autorenew';
      default:
        return '';
    }
  }

  getActionClass(action: string): string {
    switch (action) {
      case 'view':
        return 'action-view';
      case 'edit':
        return 'action-edit';
      case 'delete':
        return 'action-delete';
      case 'refresh':
        return 'autorenew';
      default:
        return '';
    }
  }

  getActionLabel(action: string): string {
    switch (action) {
      case 'view':
        return 'Détails';
      case 'edit':
        return 'Modifier';
      case 'delete':
        return 'Supprimer';
      case 'refresh':
        return 'Actualiser';
      default:
        return action;
    }
  }

  getValue(row: any, key: string): any {
    if (key.split('.').length > 1) {
      const keys = key.split('.');
      return row[keys[0]] ? row[keys[0]][keys[1]] : null;
    }
    return row[key];
  }

  isStatusColumn(key: string): boolean {
    const statusKeys = ['status', 'state', 'statut', 'état', 'etat'];
    return statusKeys.some((statusKey) =>
      key.toLowerCase().includes(statusKey.toLowerCase())
    );
  }

  getStatusClass(key: string, value: any): string {
  if (!this.isStatusColumn(key) || !value) {
    return '';
  }

  const valueStr = value.toString().toLowerCase();

  if (['actif', 'active', 'activé', 'true', '1'].includes(valueStr)) {
    return 'status-active';
  }

  if (['inactif', 'inactive', 'désactivé', 'desactive', 'false', '0'].includes(valueStr)) {
    return 'status-inactive';
  }

  if (['installé', 'en cours d\'installation'].includes(valueStr)) {
    return 'status-installed';
  }

  if (['non installé'].includes(valueStr)) {
    return 'status-uninstalled';
  }

  if (['suspendu'].includes(valueStr)) {
    return 'status-suspended';
  }

  if (['endommagé','endomagé', 'endomage', 'défaillant'].includes(valueStr)) {
    return 'status-damaged';
  }

  if (['en cours de réparation'].includes(valueStr)) {
    return 'status-repairing';
  }

  if (['en maintenance'].includes(valueStr)) {
    return 'status-in-maintenance';
  }

  if (['réparé'].includes(valueStr)) {
    return 'status-repaired';
  }

  if (['désinstallé', 'retiré', 'retire'].includes(valueStr)) {
    return 'status-removed';
  }

  if (['erreur', 'error', 'échec', 'echec'].includes(valueStr)) {
    return 'status-error';
  }

  return '';
}

  getCellClass(key: string, value: any): string {
    return this.isStatusColumn(key) ? 'status-cell' : '';
  }
}
