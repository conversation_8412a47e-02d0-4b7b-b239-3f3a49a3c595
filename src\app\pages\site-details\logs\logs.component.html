<div class="logs-container">
  <!-- Modal -->
  <div class="log-modal-backdrop" *ngIf="selectedLog" (click)="closeLogDetails()">
    <div class="log-modal" (click)="$event.stopPropagation()">
      <div class="log-modal-header">
        <div class="modal-title-section">
          <mat-icon class="modal-icon">article</mat-icon>
          <h2>Détail du Journal</h2>
        </div>
        <button class="close-btn" (click)="closeLogDetails()" aria-label="Fermer la fenêtre de détails">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <div class="log-modal-body">
        <div class="detail-grid">
          <div class="detail-item">
            <mat-icon class="detail-icon">priority_high</mat-icon>
            <div class="detail-content">
              <span class="detail-label">Niveau</span>
              <span class="detail-value level-badge" [ngClass]="getLogLevelClass(selectedLog.level || 'info')">
                {{ selectedLog.level || "INFO" | uppercase }}
              </span>
            </div>
          </div>

          <div class="detail-item">
            <mat-icon class="detail-icon">schedule</mat-icon>
            <div class="detail-content">
              <span class="detail-label">Date</span>
              <span class="detail-value">
                {{ selectedLog.timestamp ? formatTimestamp(selectedLog.timestamp) : "—" }}
              </span>
            </div>
          </div>

          <div class="detail-item" *ngIf="selectedLog.topic">
            <mat-icon class="detail-icon">topic</mat-icon>
            <div class="detail-content">
              <span class="detail-label">Topic</span>
              <span class="detail-value topic-value">{{ selectedLog.topic }}</span>
            </div>
          </div>
        </div>

        <div class="summary-section">
          <div class="section-header">
            <mat-icon>summarize</mat-icon>
            <h3>Résumé</h3>
          </div>
          <div class="summary-container">
            <div *ngIf="isSummaryLoading" class="summary-loading">
              <div class="loading-spinner"></div>
              <span>Génération du résumé...</span>
            </div>
            <div *ngIf="!isSummaryLoading" class="summary-content">
              {{ selectedLog.summary || "Pas de message disponible" }}
            </div>
          </div>
        </div>

        <div *ngIf="selectedLog.payload && hasMetaData(selectedLog.payload)" class="metadata-section">
          <div class="section-header">
            <mat-icon>data_object</mat-icon>
            <h3>Données supplémentaires</h3>
          </div>
          <div class="metadata-grid">
            <ng-container *ngFor="let key of priorityMetaKeys">
              <div class="metadata-item"
                *ngIf="selectedLog.payload[key] !== undefined && selectedLog.payload[key] !== null">
                <span class="metadata-key">{{ key }}</span>
                <span class="metadata-value">{{ getMetaValue(selectedLog.payload[key]) }}</span>
              </div>
            </ng-container>

            <div *ngFor="let key of getOtherMetaKeys(selectedLog.payload)" class="metadata-item">
              <span class="metadata-key">{{ key }}</span>
              <span class="metadata-value">{{ getMetaValue(selectedLog.payload[key]) }}</span>
            </div>
          </div>
        </div>

        <div *ngIf="isRuleExecutionLog(selectedLog)" class="rule-execution-details">
          <div class="section-header">
            <mat-icon>rule</mat-icon>
            <h3>Détails de l'exécution de la règle</h3>
          </div>
          <div class="details-grid">
            <div *ngFor="let item of getRuleExecutionDetails(selectedLog)" class="detail-row">
              <span class="detail-label">{{ item.label }}</span>
              <span class="detail-value">{{ item.value }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Page Header -->
  <header class="page-header">
    <div class="header-content">
      <div class="header-icon">
        <mat-icon>description</mat-icon>
      </div>
      <div class="header-text">
        <h1 class="page-title">Journaux du Local</h1>
        <p class="page-subtitle">
          Visualisez et filtrez les journaux de votre système en temps réel
        </p>
      </div>
    </div>
  </header>

  <!-- Filter Form -->
  <div class="filter-card">
    <div class="filter-header">
      <mat-icon>tune</mat-icon>
      <h2>Filtres et Options</h2>
    </div>

    <form [formGroup]="logForm" class="filter-form">
      <div class="filter-grid">
        <div class="form-group">
          <label class="form-label" for="logType">
            <mat-icon>filter_list</mat-icon>
            Type de journal
          </label>
          <div class="select-wrapper">
            <select id="logType" formControlName="logType" class="form-control" required>
              <option value="Tout">Tout afficher</option>
              <option value="info">Information</option>
              <option value="warning">Avertissement</option>
              <option value="error">Erreur</option>
              <option value="debug">Debug</option>
            </select>
            <mat-icon class="select-arrow">expand_more</mat-icon>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label" for="textSearch">
            <mat-icon>search</mat-icon>
            Recherche
          </label>
          <div class="search-wrapper">
            <input id="textSearch" type="text" formControlName="textSearch" class="form-control"
              placeholder="Rechercher dans les messages..." />
            <mat-icon class="search-icon">search</mat-icon>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label" for="pageSize">
            <mat-icon>view_list</mat-icon>
            Nombre d'entrées
          </label>
          <div class="select-wrapper">
            <select id="pageSize" formControlName="pageSize" class="form-control">
              <option value="50">50 entrées</option>
              <option value="100">100 entrées</option>
              <option value="200">200 entrées</option>
              <option value="500">500 entrées</option>
            </select>
            <mat-icon class="select-arrow">expand_more</mat-icon>
          </div>
        </div>
      </div>

      <div class="filter-actions">
        <button type="button" class="btn btn-secondary" (click)="resetForm()">
          <mat-icon>refresh</mat-icon>
          Réinitialiser
        </button>
        <button type="button" class="btn btn-primary" (click)="fetchLogs()" [disabled]="isLoading">
          <mat-icon *ngIf="!isLoading">sync</mat-icon>
          <div *ngIf="isLoading" class="button-spinner"></div>
          {{ isLoading ? 'Chargement...' : 'Actualiser' }}
        </button>
      </div>
    </form>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-state">
    <div class="loading-content">
      <div class="main-spinner"></div>
      <h3>Chargement en cours</h3>
      <p>Récupération des journaux...</p>
    </div>
  </div>

  <!-- Log Results -->
  <div *ngIf="!isLoading && filteredLogs && filteredLogs.length > 0" class="logs-section">
    <div class="section-header">
      <mat-icon>list_alt</mat-icon>
      <h2>Journaux ({{ filteredLogs.length }})</h2>
    </div>

    <div class="log-grid">
      <div *ngFor="let log of filteredLogs; trackBy: trackByFn" class="log-card"
        [ngClass]="getLogLevelClass(log.level || 'info')" (click)="openLogDetails(log)" tabindex="0" role="button"
        aria-label="Voir détails du journal">
        <div class="log-header">
          <div class="log-level-section">
            <div class="level-indicator" [ngClass]="getLogLevelClass(log.level || 'info')"></div>
            <span class="log-level" [ngClass]="getLogLevelClass(log.level || 'info')">
              {{ log.level || "INFO" | uppercase }}
            </span>
          </div>
          <div class="log-timestamp">
            <mat-icon>schedule</mat-icon>
            <span>{{ log.timestamp ? formatTimestamp(log.timestamp) : "—" }}</span>
          </div>
        </div>

        <div class="log-content">
          <div class="log-message" [class.empty]="!log.message">
            <ng-container *ngIf="isRuleExecutionLog(log); else defaultMessage">
              <strong>{{ getRuleExecutionSummary(log) }}</strong>
            </ng-container>
            <ng-template #defaultMessage>
              <ng-container *ngIf="log.message && log.message.length > 0; else noMessage">
                {{ log.message.length > 120 ? (log.message | slice : 0 : 120) + "..." : log.message }}
              </ng-container>
              <ng-template #noMessage>
                <em>Pas de message disponible</em>
              </ng-template>
            </ng-template>
          </div>

          <!-- Inline Payload Preview -->
          <div *ngIf="log.payload" class="payload-preview">
            <ng-container *ngFor="let key of priorityMetaKeys; let i = index">
              <div class="meta-chip" *ngIf="log.payload[key] !== undefined && log.payload[key] !== null && i < 3">
                <span class="chip-key">{{ key }}</span>
                <span class="chip-value">{{ getMetaValue(log.payload[key]) }}</span>
              </div>
            </ng-container>
          </div>
        </div>

        <div class="log-footer">
          <div *ngIf="log.topic" class="log-topic">
            <mat-icon>topic</mat-icon>
            <span>{{ log.topic | slice : 0 : 30 }}{{ log.topic.length > 30 ? '...' : '' }}</span>
          </div>

          <div class="log-actions">
            <div *ngIf="log.payload && hasMetaData(log.payload)" class="meta-indicator"
              title="Données supplémentaires disponibles">
              <mat-icon>info</mat-icon>
            </div>
            <div class="view-indicator">
              <mat-icon>visibility</mat-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && (!filteredLogs || filteredLogs.length === 0)" class="empty-state">
    <div class="empty-content">
      <div class="empty-icon">
        <mat-icon>inbox</mat-icon>
      </div>
      <h3 class="empty-title">Aucun journal trouvé</h3>
      <p class="empty-description">
        Aucun journal ne correspond aux critères de recherche sélectionnés pour ce local.
      </p>
      <button class="btn btn-primary" (click)="resetForm()">
        <mat-icon>refresh</mat-icon>
        Réinitialiser les filtres
      </button>
    </div>
  </div>
</div>