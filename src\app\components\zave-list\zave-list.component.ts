import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule} from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil, distinctUntilChanged, debounceTime } from 'rxjs/operators';
import { MatIconModule } from '@angular/material/icon';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';
import { ZWaveMqttService } from '../../core/services/zwave.service'; // Updated import
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS
} from 'ng-angular-popup';
import {
  DeviceListItem,
  NodeStatus,
  ZWaveError,
  InclusionState,
  ControllerInfo,
  DeviceValue,
} from '../../shared/models/zwave/ZwaveModels';

interface FilterState {
  search: string;
  status: NodeStatus | 'all';
  deviceType: string;
  showOffline: boolean;
}

@Component({
  selector: 'app-zwave-list',
  standalone: true,
  imports: [CommonModule, FormsModule, NgxUiLoaderModule, MatIconModule, NgToastComponent],
  templateUrl: './zave-list.component.html', // Fixed typo in filename
  styleUrls: ['./zave-list.component.css'] // Fixed typo in filename
})
export class ZWaveListComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  TOAST_POSITIONS = TOAST_POSITIONS;
  
  // State
  devices: DeviceListItem[] = [];
  filteredDevices: DeviceListItem[] = [];
  connectionStatus: 'connected' | 'connecting' | 'disconnected' = 'disconnected';
  lastError: ZWaveError | null = null;
  inclusionState: InclusionState | null = null;
  controllerInfo: ControllerInfo | null = null;
  
  // Computed properties
  isConnected = false;
  inclusionActive = false;
  connectionStatusText = 'Disconnected';
  deviceTypes: string[] = [];
  
  // Filters - Initialize with safe defaults
  filters: FilterState = {
    search: '',
    status: 'all',
    deviceType: '',
    showOffline: true
  };

  constructor(
    private zwaveService: ZWaveMqttService, // Updated service
    private toast: NgToastService,
    private ngxLoader: NgxUiLoaderService
  ) {}

  ngOnInit(): void {
    this.subscribeToZWaveEvents();
    this.initializeFilters();
    
    // Initialize with safe defaults
    this.devices = [];
    this.filteredDevices = [];
    this.deviceTypes = [];
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private subscribeToZWaveEvents(): void {
    // Connection state
    this.zwaveService.getConnectionState()
      .pipe(takeUntil(this.destroy$))
      .subscribe(state => {
        this.connectionStatus = state;
        this.isConnected = state === 'connected';
        this.connectionStatusText = this.getConnectionStatusText(state);
      });

    // Error state
    this.zwaveService.getLastError()
      .pipe(takeUntil(this.destroy$))
      .subscribe(error => {
        this.lastError = error;
        if (error) {
          this.toast.warning(
            'Z-Wave Error',
            error.message
          );
        }
      });

    // Device list
    this.zwaveService.getDeviceList()
      .pipe(takeUntil(this.destroy$))
      .subscribe(devices => {
        this.devices = devices;
        this.updateDeviceTypes();
        this.applyFilters();
      });

    // Inclusion state
    this.zwaveService.getInclusionState()
      .pipe(takeUntil(this.destroy$))
      .subscribe(state => {
        this.inclusionState = state;
        this.inclusionActive = state?.active || false;
      });

    // Controller info - with proper null checking
    this.zwaveService.getController()
      .pipe(takeUntil(this.destroy$))
      .subscribe(controller => {
        this.controllerInfo = controller;
        // Handle the case where controller.nodes might be undefined
        if (controller && !controller.nodes) {
          // Initialize nodes array if it doesn't exist
          controller.nodes = [];
        }
      });

    // Load initial controller info when connected
    this.zwaveService.getConnectionState()
      .pipe(
        takeUntil(this.destroy$),
        distinctUntilChanged()
      )
      .subscribe(state => {
        if (state === 'connected') {
          this.loadControllerInfo();
        }
      });

    // Monitor MQTT-specific events
    this.subscribeToMqttEvents();
  }

  private subscribeToMqttEvents(): void {
    // Monitor Z-Wave events from MQTT
    this.zwaveService.getEvents()
      .pipe(takeUntil(this.destroy$))
      .subscribe(event => {
        this.handleZWaveEvent(event);
      });

    // Monitor driver ready state
    this.zwaveService.getDriverReady()
      .pipe(takeUntil(this.destroy$))
      .subscribe(ready => {
        if (ready) {
          this.toast.success(
            'Z-Wave Driver Ready',
            'Z-Wave network is ready for operation'
          );
        }
      });
  }

  private handleZWaveEvent(event: any): void {
    switch (event.event) {
      case 'node added':
        this.toast.success(
          'Device Added',
          `New device added to the network (Node ${event.nodeId || 'Unknown'})`
        );
        break;
      
      case 'node removed':
        this.toast.info(
          'Device Removed',
          `Device removed from the network (Node ${event.nodeId || 'Unknown'})`
        );
        break;
      
      case 'inclusion started':
        this.toast.info(
          'Inclusion Started',
          'Waiting for device to be added to the network'
        );
        break;
      
      case 'inclusion stopped':
        this.toast.info(
          'Inclusion Stopped',
          'Device inclusion mode deactivated'
        );
        break;
      
      case 'exclusion started':
        this.toast.info(
          'Exclusion Started',
          'Waiting for device to be removed from the network'
        );
        break;
      
      case 'exclusion stopped':
        this.toast.info(
          'Exclusion Stopped',
          'Device exclusion mode deactivated'
        );
        break;
      
      case 'node value updated':
        // Optional: Show toast for value updates (might be too verbose)
        console.log('Node value updated:', event);
        break;
    }
  }

  private initializeFilters(): void {
    // Debounce filter changes
    const filterChange$ = new Subject<void>();
    filterChange$
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(300)
      )
      .subscribe(() => this.applyFilters());
  }

  private updateDeviceTypes(): void {
    const types = new Set(this.devices.map(d => d.deviceType));
    this.deviceTypes = Array.from(types).sort();
  }

  private applyFilters(): void {
    this.filteredDevices = this.devices.filter(device => {
      // Search filter
      if (this.filters.search) {
        const search = this.filters.search.toLowerCase();
        const matchesSearch = 
          device.name.toLowerCase().includes(search) ||
          device.deviceType.toLowerCase().includes(search) ||
          device.nodeId.toString().includes(search) ||
          (device.location && device.location.toLowerCase().includes(search));
        
        if (!matchesSearch) return false;
      }

      // Status filter
      if (this.filters.status !== 'all' && device.status !== this.filters.status) {
        return false;
      }

      // Device type filter
      if (this.filters.deviceType && device.deviceType !== this.filters.deviceType) {
        return false;
      }

      // Show offline filter
      if (!this.filters.showOffline && device.status === NodeStatus.Dead) {
        return false;
      }

      return true;
    });
  }

  // Connection methods (new for MQTT)
  connectToMqtt(): void {
    this.zwaveService.connect();
    this.toast.info(
      'Connecting',
      'Attempting to connect to MQTT broker...'
    );
  }

  disconnectFromMqtt(): void {
    this.zwaveService.disconnect();
    this.toast.info(
      'Disconnected',
      'Disconnected from MQTT broker'
    );
  }

  // Event handlers
  onFilterChange(): void {
    this.applyFilters();
  }

  async startInclusion(): Promise<void> {
    try {
      this.ngxLoader.start();
      await this.zwaveService.startInclusion();
      this.toast.success(
        'Inclusion Started',
        'Device inclusion mode activated. Press the inclusion button on your device.'
      );
    } catch (error) {
      console.error('Failed to start inclusion:', error);
      this.toast.warning(
        'Inclusion Failed',
        'Could not start device inclusion mode'
      );
    } finally {
      this.ngxLoader.stop();
    }
  }

  async startExclusion(): Promise<void> {
    try {
      this.ngxLoader.start();
      await this.zwaveService.startExclusion();
      this.toast.success(
        'Exclusion Started',
        'Device exclusion mode activated. Press the exclusion button on your device.'
      );
    } catch (error) {
      console.error('Failed to start exclusion:', error);
      this.toast.warning(
        'Exclusion Failed',
        'Could not start device exclusion mode'
      );
    } finally {
      this.ngxLoader.stop();
    }
  }

  async stopCurrentOperation(): Promise<void> {
    try {
      this.ngxLoader.start();
      if (this.inclusionState?.active) {
        await this.zwaveService.stopInclusion();
        this.toast.info(
          'Inclusion Stopped',
          'Device inclusion mode deactivated'
        );
      } else {
        await this.zwaveService.stopExclusion();
        this.toast.info(
          'Exclusion Stopped',
          'Device exclusion mode deactivated'
        );
      }
    } catch (error) {
      console.error('Failed to stop operation:', error);
      this.toast.warning(
        'Operation Failed',
        'Could not stop current operation'
      );
    } finally {
      this.ngxLoader.stop();
    }
  }

  async healNetwork(): Promise<void> {
    try {
      this.ngxLoader.start();
      
      // Check if we have any devices to heal
      if (this.devices.length === 0) {
        this.toast.warning(
          'No Devices Found',
          'Please add some Z-Wave devices before healing the network'
        );
        return;
      }
      
      await this.zwaveService.healNetwork();
      this.toast.success(
        'Network Healing Started',
        'This process may take several minutes to complete'
      );
    } catch (error) {
      console.error('Failed to heal network:', error);
      this.toast.warning(
        'Network Healing Failed',
        'Could not start network healing process'
      );
    } finally {
      this.ngxLoader.stop();
    }
  }

  async refreshDevices(): Promise<void> {
    try {
      this.ngxLoader.start();
      await this.loadControllerInfo();
      this.toast.success(
        'Devices Refreshed',
        'Device list has been updated'
      );
    } catch (error) {
      console.error('Failed to refresh devices:', error);
      this.toast.warning(
        'Refresh Failed',
        'Could not refresh device list'
      );
    } finally {
      this.ngxLoader.stop();
    }
  }

  async toggleSwitch(device: DeviceListItem): Promise<void> {
    try {
      this.ngxLoader.start();
      const currentValue = this.getSwitchValue(device);
      
      if (currentValue) {
        await this.zwaveService.switchOff(device.nodeId);
        this.toast.success(
          `${device.name} Turned Off`,
          'Device switched off successfully'
        );
      } else {
        await this.zwaveService.switchOn(device.nodeId);
        this.toast.success(
          `${device.name} Turned On`,
          'Device switched on successfully'
        );
      }
    } catch (error) {
      console.error(`Failed to toggle switch for device ${device.nodeId}:`, error);
      this.toast.warning(
        `Failed to Control ${device.name}`,
        'Please check device connection and try again'
      );
    } finally {
      this.ngxLoader.stop();
    }
  }

  async setDimLevel(device: DeviceListItem, event: any): Promise<void> {
    try {
      this.ngxLoader.start();
      const level = parseInt(event.target.value);
      await this.zwaveService.setDimLevel(device.nodeId, level);
      this.toast.success(
        `${device.name} Brightness Set`,
        `Brightness set to ${level}%`
      );
    } catch (error) {
      console.error(`Failed to set dim level for device ${device.nodeId}:`, error);
      this.toast.warning(
        'Failed to Set Brightness',
        'Please check device connection and try again'
      );
    } finally {
      this.ngxLoader.stop();
    }
  }

  async refreshDevice(device: DeviceListItem): Promise<void> {
    try {
      this.ngxLoader.start();
      await this.zwaveService.refreshNode(device.nodeId);
      this.toast.success(
        `${device.name} Refreshed`,
        'Device information updated successfully'
      );
    } catch (error) {
      console.error(`Failed to refresh device ${device.nodeId}:`, error);
      this.toast.warning(
        `Failed to Refresh ${device.name}`,
        'Please check device connection and try again'
      );
    } finally {
      this.ngxLoader.stop();
    }
  }

  async healDevice(device: DeviceListItem): Promise<void> {
    try {
      this.ngxLoader.start();
      await this.zwaveService.healNode(device.nodeId);
      this.toast.success(
        `Healing ${device.name}`,
        'Device healing started - this may take a few minutes'
      );
    } catch (error) {
      console.error(`Failed to heal device ${device.nodeId}:`, error);
      this.toast.warning(
        `Failed to Heal ${device.name}`,
        'Please check device connection and try again'
      );
    } finally {
      this.ngxLoader.stop();
    }
  }

  async removeFailedDevice(device: DeviceListItem): Promise<void> {
    // For now, we'll use the browser confirm as NgToast doesn't have built-in confirmation
    // In a full implementation, you'd create a custom confirmation dialog
    if (confirm(`Are you sure you want to remove failed device "${device.name}"?`)) {
      try {
        this.ngxLoader.start();
        await this.zwaveService.removeFailedNode(device.nodeId);
        this.toast.success(
          `${device.name} Removed`,
          'Failed device removed from network successfully'
        );
      } catch (error) {
        console.error(`Failed to remove failed device ${device.nodeId}:`, error);
        this.toast.warning(
          `Failed to Remove ${device.name}`,
          'Please check device status and try again'
        );
      } finally {
        this.ngxLoader.stop();
      }
    }
  }

  showDeviceDetails(device: DeviceListItem): void {
    // This could open a modal or navigate to a detail page
    console.log('Show details for device:', device);
    this.toast.info(
      'Device Details',
      `Showing details for ${device.name} (Node ${device.nodeId})`
    );
    // TODO: Implement device details modal or navigation
  }

  private async loadControllerInfo(): Promise<void> {
    try {
      await this.zwaveService.getControllerInfo();
    } catch (error) {
      console.error('Failed to load controller info:', error);
      this.toast.warning(
        'Controller Error',
        'Failed to load controller information'
      );
    }
  }

  private getConnectionStatusText(state: string): string {
    switch (state) {
      case 'connected': return 'Connected';
      case 'connecting': return 'Connecting...';
      case 'disconnected': return 'Disconnected';
      default: return 'Unknown';
    }
  }

  getStatusText(status: NodeStatus): string {
    switch (status) {
      case NodeStatus.Alive: return 'Online';
      case NodeStatus.Asleep: return 'Sleeping';
      case NodeStatus.Dead: return 'Offline';
      case NodeStatus.Awake: return 'Awake';
      case NodeStatus.Unknown: return 'Unknown';
      default: return 'Unknown';
    }
  }

  getSwitchValue(device: DeviceListItem): boolean {
    const switchValue = device.values.find(v => 
      v.type === 'switch' && (v.label.toLowerCase().includes('switch') || v.label.toLowerCase().includes('target'))
    );
    return switchValue?.value === true;
  }

  getDimLevel(device: DeviceListItem): number {
    const dimValue = device.values.find(v => 
      v.type === 'dimmer' && (v.label.toLowerCase().includes('level') || v.label.toLowerCase().includes('target'))
    );
    return dimValue?.value || 0;
  }

  getSensorValues(device: DeviceListItem): DeviceValue[] {
    return device.values.filter(v => v.type === 'sensor' || v.type === 'meter');
  }

  trackByNodeId(index: number, device: DeviceListItem): number {
    return device.nodeId;
  }
}