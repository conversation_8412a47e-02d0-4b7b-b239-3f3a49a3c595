
// Fixed controller-server.component.ts
import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnInit,
  OnChanges,
  OnDestroy,
  SimpleChanges,
  EventEmitter,
  Output,
  HostListener,
} from '@angular/core';
import { ControllerServeur } from '@app/core/models/controllerServeur';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { PageEvent, MatPaginatorModule } from '@angular/material/paginator';
import { ControllerServeurApiService } from '@app/core/services/administrative/controllerserveur.service';
import { Client } from '@app/core/models/client';
import { Licence } from '@app/core/models/licence';
import { FormCsComponent } from '../form-cs/form-cs.component';
import { DetailsCsComponent } from '../details-cs/details-cs.component';
import { HierarchyComponent } from '../hierarchy/hierarchy.component';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import {
  FilterParam,
  Lister,
  Pagination,
  SortPage,
} from '@app/core/models/util/page';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { GroupedControllerServer } from '@app/shared/models/ControllerServerHierarchyDto';
import { HierarchyService } from '@app/shared/services/cs-hierarchy.service';
import { FormsModule } from '@angular/forms';
import { ControllerServerViewData } from '@app/shared/models/ControllerServerViewData';
import { NgToastService } from 'ng-angular-popup';
import { TabLoadingService } from '@app/core/services/tab-loading.service';

@Component({
  selector: 'app-controller-server',
  imports: [
    CommonModule,
    GenericTableComponent,
    MatPaginatorModule,
    FormCsComponent,
    DetailsCsComponent,
    MatIconModule,
    MatTooltipModule,
    FormsModule,
  ],
  templateUrl: './controller-server.component.html',
  styleUrl: './controller-server.component.css',
})
export class ControllerServerComponent implements OnInit, OnChanges, OnDestroy {
  @Input() controllerServeurs: ControllerServeur[] = [];
  @Input() clientId: string = '';
  @Input() client: Client | null = null;
  @Input() licences: Licence[] = [];
  @Output() controllerServerDeleted = new EventEmitter<string>();
  @Output() allControllerServeurs: GroupedControllerServer[] = [];
  @Output() loadingChange = new EventEmitter<{ isLoading: boolean; message?: string }>();

  // Data properties - simplified
  originalData: ControllerServerViewData[] = []; // Original data from API
  allControllerServersData: ControllerServerViewData[] = []; // Filtered data for display
  allControllerServers: ControllerServerViewData[] = []; // Keep for compatibility

  // Properties
  displayedControllerServeurs: ControllerServeur[] = [];
  allControllerServeursData: ControllerServeur[] = []; // Store all data for search
  isLoading: boolean = false;
  showCreateForm: boolean = false;
  showDetailsModal: boolean = false;
  showHierarchyModal: boolean = false;
  isEditMode: boolean = false;
  selectedControllerServer: ControllerServerViewData | null = null;
  selectedControllerServerEdit: ControllerServeur | null = null;
  searchParam: string = '';
  hasSearchFilter: boolean = false;
  lister: Lister = {};
  totalCount: number = 0;

  csHeaders: string[] = [
    'Nom',
    'Licence',
    'Nbr Contrôleurs',
    'Nbr Capteurs',
    'Zone Géographic',
    'Conditions Commerciales',
    'Déclencheur',
    'Action',
    'Evenement',
    'Statut',
  ];
  csKeys: string[] = [
    'Name',
    `LicenceName`,
    'MaxControllers',
    'MaxSensors',
    'GeographicZone',
    'CommercialCondition',
    'TriggerType',
    'ActionType',
    'EventType',
    'Status',
  ];

  pageSize: number = 5;
  currentPage: number = 0;

  constructor(
    private readonly controllerServerService: ControllerServeurApiService,
    private readonly subscriptionApiService: SubscriptionApiService,
    private readonly hierarchyService: HierarchyService,
    private dialog: MatDialog,
    readonly toast: NgToastService,
    private tabLoadingService: TabLoadingService // inject the service
  ) {}

  public showSuccess(message: string, title: string) {
    this.toast.info(message, title, 3000, false);
  }
  public showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }

  ngOnInit(): void {
    console.log('Controller Server Component initialized');
    console.log('Licences received:', this.licences);
    this.loadControllerServers();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['licences']) {
      console.log('Licences changed:', changes['licences'].currentValue);
    }
  }

  ngOnDestroy(): void {
    document.body.classList.remove('modal-open');
  }

  // loadControllerServers() {
  //   if (!this.clientId) {
  //     this.originalData = [];
  //     this.allControllerServersData = [];
  //     this.allControllerServers = [];
  //     this.updateDisplayedData();
  //     return;
  //   }
  //   this.updateMaxValues()
  //   this.isLoading = true;

  //   this.controllerServerService
  //     .getClientControllerServers(this.clientId)
  //     .subscribe({
  //       next: (controllers: ControllerServerViewData[]) => {
  //         console.log('ControllerServerViewData loaded:', controllers);

  //         // Store original data
  //         this.originalData = [...controllers];
  //         this.allControllerServers = [...controllers]; // Keep for compatibility

  //         // Apply search filter
  //         this.applySearchFilter();

  //         this.isLoading = false;
  //       },
  //       error: (error) => {
  //         console.error('Error loading controllers:', error);
  //         this.originalData = [];
  //         this.allControllerServersData = [];
  //         this.allControllerServers = [];
  //         this.updateDisplayedData();
  //         this.isLoading = false;
  //       },
  //     });
  // }

  loadControllerServers() {
    // Start loading
    this.tabLoadingService.setTabLoading('controllerServeurs', true, 'Chargement des contrôleurs serveurs...');
    this.loadingChange.emit({ isLoading: true, message: 'Chargement des contrôleurs serveurs...' });
    this.updateMaxValues();
    const pagination: Pagination = {
      CurrentPage: this.currentPage + 1,
      PageSize: this.pageSize,
    };
    this.lister.Pagination = pagination;

    const FilterParams: FilterParam[] = [];
    const SortParams: SortPage[] = [
      {
        Column: 'ControllerName',
        Sort: 'asc',
      },
    ];

    if (this.searchParam.trim().length > 0) {
      console.log(this.searchParam);
      this.csKeys.forEach((k) => {
        if (k != 'Status' && k != 'MaxControllers' && k != 'MaxSensors') {
          FilterParams.push({
            Column: k,
            Op: 'contains',
            Value: this.searchParam,
            AndOr: 'OR',
          });
        }
      });
      this.lister.FilterParams = FilterParams;
    } else {
      this.lister.FilterParams = [];
    }
    this.isLoading = true;
    console.log('Request for paginated controller servers:', this.lister);
    this.controllerServerService
      .getClientPaginatedControllerServers(this.clientId, this.lister)
      .subscribe({
        next: (response: any) => {
          this.lister = response.Lister;
          this.lister = response.Lister;
          if (response?.Lister?.pagination?.totalElement !== undefined) {
            this.totalCount = response.Lister.pagination.totalElement;
          } else if (response?.Lister?.Pagination?.TotalElement !== undefined) {
            this.totalCount = response.Lister.Pagination.TotalElement;
          } else {
            this.totalCount = Math.max(8, response.Content.length);
          }
          this.allControllerServersData = response.Content ?? [];
          console.log(
            'Controller Servers loaded:',
            this.allControllerServersData
          );
          this.isLoading = false;
          this.lister = response.Lister;
          // Stop loading
          this.tabLoadingService.setTabLoading('controllerServeurs', false);
          this.loadingChange.emit({ isLoading: false });
        },
        error: (error) => {
          console.error('Error loading controller servers:', error);
          this.originalData = [];
          this.allControllerServersData = [];
          this.allControllerServers = [];
          this.isLoading = false;
          // Stop loading
          this.tabLoadingService.setTabLoading('controllerServeurs', false, 'Erreur lors du chargement');
          this.loadingChange.emit({ isLoading: false, message: 'Erreur lors du chargement' });
        },
      });
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadControllerServers();
  }

  handleTableAction(event: { action: string; row: any }): void {
    const { action, row } = event;

    switch (action) {
      case 'view':
        this.viewControllerServerDetails(row);
        break;
      case 'edit':
        this.editControllerServer(row);
        break;
      case 'delete':
        this.deleteControllerServer(row.Id);
        break;
      default:
        console.warn('Unknown action:', action);
    }
  }

  updateMaxValues(): void {
    this.controllerServerService.updateMaxValues().subscribe({
      next: (response) => {
        console.log('Max values updated successfully:', response);
      },
      error: (error) => {
        console.error('Error updating max values:', error);
      },
    });
  }

  showHierarchy(): void {
    console.log(
      'Opening hierarchy modal with data:',
      this.allControllerServeurs
    );

    if (
      !this.allControllerServeurs ||
      this.allControllerServeurs.length === 0
    ) {
      return;
    }

    this.showHierarchyModal = true;
    document.body.classList.add('modal-open');
  }

  onHierarchyClosed(): void {
    this.showHierarchyModal = false;
    document.body.classList.remove('modal-open');
  }

  viewControllerServerDetails(
    controllerServer: ControllerServerViewData
  ): void {
    console.log('Viewing controller server details:', controllerServer);
    this.selectedControllerServer = controllerServer;
    this.showDetailsModal = true;
    document.body.classList.add('modal-open');
  }

  editControllerServer(controllerServer: ControllerServeur): void {
    this.selectedControllerServerEdit = controllerServer;
    this.isEditMode = true;
    this.showCreateForm = true;
    document.body.classList.add('modal-open');
  }

  deleteControllerServer(controllerServerId: string): void {
    const controllerServer =
      this.originalData.find((cs) => cs.Id === controllerServerId) ||
      this.allControllerServersData.find((cs) => cs.Id === controllerServerId);

    const serverName = controllerServer?.Name || 'ce contrôleur serveur';

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmation de suppression',
        message: `Êtes-vous sûr de vouloir supprimer "${serverName}" ?`,
        type: 'danger',
        icon: 'warning',
      },
    });
    
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.controllerServerService.delete(controllerServerId).subscribe({
          next: () => {
            // Remove from original data
            this.originalData = this.originalData.filter(
              (cs) => cs.Id !== controllerServerId
            );
            
            this.controllerServerDeleted.emit(controllerServerId);
            this.loadControllerServers();

            this.showSuccess(
              'Le Contrôleur serveur a été suprimé avec succes',
              'Information'
            );
          },
          error: (err) => {
            console.error('Error deleting controller server:', err);

            this.showError(
              'Erreur lors de la suppression de Contrôleur serveur, Essayer ultérieurement',
              'Erreur'
            );
          },
        });
      }
    });
  }

  addNewControllerServer(): void {
    this.selectedControllerServer = null;
    this.isEditMode = false;
    this.showCreateForm = true;
    document.body.classList.add('modal-open');
  }

  closeAllModals(): void {
    this.showCreateForm = false;
    this.showDetailsModal = false;
    this.showHierarchyModal = false;
    this.isEditMode = false;
    this.selectedControllerServer = null;
    document.body.classList.remove('modal-open');
  }

  onFormClosed(): void {
    this.showCreateForm = false;
    this.isEditMode = false;
    this.selectedControllerServer = null;
    document.body.classList.remove('modal-open');
  }

  onDetailsClosed(): void {
    this.showDetailsModal = false;
    this.selectedControllerServer = null;
    document.body.classList.remove('modal-open');
  }

  onControllerServerCreated(newControllerServer: ControllerServeur): void {
    // Add to original data (convert if needed)
    const newServerViewData =
      newControllerServer as any as ControllerServerViewData;
    this.originalData.push(newServerViewData);

    // Reapply search filter

    this.onFormClosed();
    this.loadControllerServers();
  }

  onControllerServerUpdated(updatedControllerServer: ControllerServeur): void {
    const id = updatedControllerServer.Id;
    const index = this.originalData.findIndex((cs) => cs.Id === id);

    if (index !== -1) {
      // Update original data (convert if needed)
      const updatedServerViewData =
        updatedControllerServer as any as ControllerServerViewData;
      this.originalData[index] = updatedServerViewData;

      // Reapply search filter
    }

    this.onFormClosed();
    this.loadControllerServers();
  }

  // Helper methods for template
  getTotalControllers(): number {
    return this.allControllerServeurs.reduce((total, server) => {
      return total + (server.ControllerServerControllers?.length || 0);
    }, 0);
  }

  getActiveControllers(): number {
    return this.allControllerServeurs.reduce((total, server) => {
      const activeCount =
        server.ControllerServerControllers?.filter(
          (csc) => csc.Controller?.State === true
        ).length || 0;
      return total + activeCount;
    }, 0);
  }
}
