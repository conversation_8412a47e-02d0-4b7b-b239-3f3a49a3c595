import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LocalRuleComponent } from './local-rule.component';

describe('LocalRuleComponent', () => {
  let component: LocalRuleComponent;
  let fixture: ComponentFixture<LocalRuleComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LocalRuleComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(LocalRuleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
