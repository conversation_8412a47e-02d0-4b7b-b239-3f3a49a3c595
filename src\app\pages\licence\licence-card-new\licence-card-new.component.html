<ngx-loading 
  [show]="isLoading" 
  [config]="{ 
    animationType: 'threeBounce', 
    backdropBackgroundColour: 'rgba(0,0,0,0.2)', 
    primaryColour: '#10b981',
    secondaryColour: '#10b981',
    tertiaryColour: '#10b981'
  }"></ngx-loading>

<div class="licence-cards-container">
  <!-- Content wrapper for consistent alignment -->
  <div class="content-wrapper">
    <!-- Header section -->
    <div class="header improved-header" style="background: #fff;">
      <h1 class="title improved-title" style="background-color: white;">Choisir un client</h1>
      <p class="subtitle improved-subtitle">
        Choisir un client pour pouvoir affecter un abonnement.
      </p>
    </div>
  </div>

  <!-- Content wrapper for consistent alignment -->
  <div class="content-wrapper">
    <!-- Client billing section -->
    <div class="client-billing-section">
      <div class="search-container">
        <!-- Search input with glass effect -->
        <div class="search-input-container" *ngIf="!selectedClient">
          <div class="search-input-glass-wrapper">
            <span class="search-input-glass-icon material-icons">search</span>
            <input
              type="text"
              class="search-input glass"
              [ngClass]="{'input-error': showChooseClientError}"
              [(ngModel)]="searchQuery"
              (focus)="showDropdown = true; filterClients()"
              (click)="showDropdown = true; filterClients()"
              (input)="filterClients()"
              placeholder="Rechercher un client..."
              [readonly]="selectedClient !== null"
              autocomplete="off"
            />
            <div class="choose-client-error" *ngIf="showChooseClientError">
              Veuillez choisir un client d'abord.
            </div>
            <div class="dropdown-glass"
                 *ngIf="showDropdown"
                 @slideInOut>
              <div 
                *ngFor="let client of filteredClients"
                class="dropdown-glass-item"
                (click)="selectClient(client)"
              >
                <div class="dropdown-glass-info">
                  <div class="dropdown-glass-name">{{ client.Name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Selected client card -->
        <div class="search-input-container" *ngIf="selectedClient" 
             style="display: flex; align-items: center; justify-content: center;">
          <div class="selected-client-container improved-client-card" @growIn>
            <div class="client-avatar-section">
            </div>
            <div class="client-details-section">
              <div class="client-name-row">
                <span class="client-name-large">{{ selectedClient.Name }}</span>
              </div>
              <div class="client-subtitle">Gérez les licences et options pour ce client</div>
            </div>
            <button class="clear-button improved-clear-btn" 
                    (click)="onClearSelection()" 
                    title="Désélectionner le client">
              <span class="material-icons">close</span>
            </button>
          </div>
        </div>

      </div>
    </div>

    <!-- MAIN GRID LAYOUT - ALL CARDS IN RESPONSIVE GRID -->
    <div class="license-grid improved-license-grid" *ngIf="isAffecterCard()">
      <ng-container *ngFor="let licence of licences; let i = index">
        <div 
          class="license-card improved-license-card"
          [style.animation-delay]="i * 0.1 + 's'"
        >
          <div class="card-content-wrapper">
            <!-- Card Header -->
            <div class="card-header improved-card-header">
              <h3 class="improved-title">{{ licence.Name }}</h3>
              <div class="card-description-wrapper">
                <p class="card-description improved-card-description" [title]="licence.Description">
                  {{ licence.Description || 'Aucune description disponible pour cette licence.' }}
                </p>
              </div>
            </div>

            <!-- Features List -->
            <div class="features-list-wrapper">
              <ul class="features-list improved-features-list">
                <li *ngFor="let option of getOptionsForLicence(licence); let j = index">
                  <div class="feature-item" [style.animation-delay]="(i * 0.1 + j * 0.05) + 's'">
                    <label class="custom-checkbox">
                      <input 
                        type="checkbox"
                        [checked]="isOptionChecked(licence, option.Id)"
                        (change)="toggleOption(licence, option.Id, $event)"
                        [disabled]="!selectedClient"
                      />
                      <span class="checkmark" [ngClass]="{
                        'checked-green': isOptionChecked(licence, option.Id),
                        'unchecked-red': !isOptionChecked(licence, option.Id)
                      }">
                        <span 
                          class="material-icons verified-icon"
                          *ngIf="isOptionChecked(licence, option.Id)"
                          style="color: var(--green-main); opacity: 1; transform: scale(1);"
                        >check_circle</span>
                        <span 
                          class="material-icons cancel-icon"
                          *ngIf="!isOptionChecked(licence, option.Id)"
                          style="color: var(--danger); opacity: 1; transform: scale(1);"
                        >cancel</span>
                      </span>
                    </label>
                    <div class="feature-details">
                      <span class="feature-name">{{ option.Name }}</span>
                    </div>
                  </div>
                </li>
              </ul>
            </div>

            <!-- License Actions -->
            <div class="license-actions">
              <button 
                class="select-btn"
                (click)="selectLicense(licence)"
                [disabled]="!selectedClient"
              >
                <span class="material-icons" style="margin-right: 0.5rem; font-size: 1.2rem;">add_circle</span>
                Affecter
              </button>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</div>

<!-- Confirmation Popup -->
<div class="overlay" *ngIf="showConfirmationPopup" @fadeInOut>
  <div class="confirmation-popup" @slideDown>
    <div class="popup-header">
      <h3>
        <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem; color: #10b981;">add_circle</span>
        Confirmer l'affectation de l'abonnement
      </h3>
      <button class="close-popup-btn" (click)="cancelLicenseApplication()">
        <span class="material-icons">close</span>
      </button>
    </div>
    
    <div class="popup-content">
      <div class="confirmation-message">
        <span class="material-icons confirmation-icon" style="color: #10b981;">help_outline</span>
        <p>
          Êtes-vous sûr de vouloir affecter la licence 
          <strong>{{ selectedLicenseForConfirmation?.Name }}</strong> 
          au client 
          <strong>{{ selectedClient?.Name }}</strong> ?
        </p>
      </div>
      
      <div class="client-license-summary">
        <div class="summary-item">
          <span class="label">Client :</span>
          <div class="client-info-summary">
            <span>{{ selectedClient?.Name }}</span>
          </div>
        </div>
        
        <div class="summary-item">
          <span class="label">Licence :</span>
          <span class="license-name">{{ selectedLicenseForConfirmation?.Name }}</span>
        </div>

        <div class="summary-item">
          <div class="frequence-payement improved-frequence-payement improved-frequence-inline">
            <label for="payment-frequency" style="font-weight: 600; color: #374151; font-size: 1.1rem; font-family: 'Lato', sans-serif;">
              Fréquence de paiement:
            </label>
            <select 
              id="payment-frequency"
              class="improved-select" 
              [(ngModel)]="selectedPaymentFrequency"
            >
              <option *ngFor="let freq of paymentFrequencies" [value]="freq.value">
                {{ freq.label }}
              </option>
            </select>
          </div>
        </div>

        <div class="summary-item">
          <span class="label">Total des options :</span>
          <span class="license-total">
            {{ selectedLicenseForConfirmation ? (getLicenceOptionsTotal(selectedLicenseForConfirmation) | number:'1.0-2') : '0.00' }} €
          </span>
        </div>

        <div class="summary-item" *ngIf="selectedLicenseForConfirmation">
          <span class="label">Options choisies :</span>
          <div class="chosen-options-list">
            <span *ngFor="let option of getSelectedOptionsForConfirmation()" 
                  class="option-tag"
                  style="
                    display: inline-block;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    border-radius: 12px;
                    padding: 6px 12px;
                    font-size: 0.9rem;
                    font-weight: 600;
                    border: 1px solid #059669;
                    box-shadow: 0 2px 8px rgba(16,185,129,0.2);
                  ">
              {{ option.Name }}
            </span>
          </div>
        </div>

        <div class="summary-item" *ngIf="selectedLicenseForConfirmation && selectedClient && clientSubscription">
          <span class="label">Début :</span>
          <span>{{ formatDate(clientSubscription.DateDebut) }}</span>
        </div>
        <div class="summary-item" *ngIf="selectedLicenseForConfirmation && selectedClient && clientSubscription">
          <span class="label">Fin :</span>
          <span>{{ formatDate(clientSubscription.DateFin) }}</span>
        </div>
      </div>
    </div>
    
    <div class="popup-actions">
      <button class="cancel-btn" (click)="cancelLicenseApplication()">
        Annuler
      </button>
      <button 
        class="confirm-btn" 
        type="button" 
        (click)="confirmLicenseApplication()"
        [disabled]="isConfirming"
      >
        <!-- Spinner when confirming -->
        <div class="btn-spinner" *ngIf="isConfirming">
          <div class="spinner-circle"></div>
        </div>
        <!-- Normal text when not confirming -->
        <span *ngIf="!isConfirming">Confirmer</span>
        <span *ngIf="isConfirming">Affectation en cours...</span>
      </button>
    </div>
  </div>
</div>

<!-- No Option Checked Popup -->
<div class="overlay" *ngIf="showNoOptionCheckedPopup" @fadeInOut>
  <div class="confirmation-popup" @slideDown>
    <div class="popup-header">
      <h3>
        <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem; color: #ef4444;">error_outline</span>
        Attention
      </h3>
      <button class="close-popup-btn" (click)="closeNoOptionCheckedPopup()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="popup-content">
      <div class="confirmation-message">
        <span class="material-icons confirmation-icon" style="color: #ef4444;">error_outline</span>
        <p>Vous devez cocher au moins une option pour continuer.</p>
      </div>
    </div>
    <div class="popup-actions">
      <button class="confirm-btn" (click)="closeNoOptionCheckedPopup()">
        Compris
      </button>
    </div>
  </div>
</div>

<!-- Same Client Error Popup -->
<div class="overlay" *ngIf="showSameClientError" @fadeInOut>
  <div class="confirmation-popup" @slideDown>
    <div class="popup-header">
      <h3>
        <span class="material-icons" style="vertical-align: middle; margin-right: 0.5rem; color: #ef4444;">error_outline</span>
        Abonnement déjà existant
      </h3>
      <button class="close-popup-btn" (click)="closeSameClientError()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="popup-content">
      <div class="confirmation-message">
        <span class="material-icons confirmation-icon" style="color: #ef4444;">error_outline</span>
        <p>Ce client possède déjà un abonnement identique avec les mêmes caractéristiques :</p>
        <ul style="margin-top: 10px; text-align: left;">
          <li>Même licence</li>
          <li>Même fréquence de paiement</li>
          <li>Même statut</li>
          <li>Mêmes options sélectionnées</li>
        </ul>
        <p style="margin-top: 10px;">Veuillez modifier la configuration ou choisir une autre licence.</p>
      </div>
    </div>
    <div class="popup-actions">
      <button class="confirm-btn" (click)="closeSameClientError()">
        Compris
      </button>
    </div>
  </div>
</div>

<!-- Success Notification -->
<div class="success-notification" *ngIf="showSuccessNotification" @slideDown>
  <div class="notification-content">
    <span class="material-icons success-icon">check_circle</span>
    <div class="notification-text">
      <h4>Licence affectée avec succès !</h4>
      <p>L'abonnement a été affecté avec succès au client.</p>
    </div>
    <button class="close-notification-btn" (click)="closeSuccessNotification()">
      <span class="material-icons">close</span>
    </button>
  </div>
</div>