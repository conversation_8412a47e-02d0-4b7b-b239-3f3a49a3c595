import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import {
  FormsModule,
  ReactiveFormsModule,
  FormGroup,
  FormControl,
  Validators,
} from '@angular/forms';
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';
import { Local } from '@app/core/models/local';
import { TypeLocal } from '@app/core/models/TypeLocal.1';
import { PageEvent } from '@angular/material/paginator';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import {
  LocalApiService,
  TypeLocalApiService,
} from '@app/core/services/administrative/local.service';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatIconModule } from '@angular/material/icon';
import { SiteApiService } from '@app/core/services/administrative/site.service';
import { Page, Lister, SortPage, FilterParam, Pagination } from '@app/core/models/util/page';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS,
} from 'ng-angular-popup';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';
import { finalize } from 'rxjs/operators';
import { TransactionApiService } from '@app/core/services/administrative/transaction.service';
import { Transaction } from '@app/core/models/transaction';

@Component({
  selector: 'app-local-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GenericTableComponent,
    MatPaginatorModule,
    MatIconModule,
    MatDialogModule,
    NgxUiLoaderModule,
    NgToastComponent,
  ],
  templateUrl: './local-management.component.html',
  styleUrls: ['./local-management.component.css'],
  animations: [
    trigger('tableRowAnimation', [
      state('void', style({ opacity: 0, transform: 'translateY(20px)' })),
      transition('void => *', animate('300ms ease-in')),
    ]),
    trigger('fadeIn', [
      state('void', style({ opacity: 0 })),
      transition('void => *', animate('400ms 300ms ease-in')),
    ]),
  ],
})
export class LocalManagementComponent implements OnInit {
  TOAST_POSITIONS = TOAST_POSITIONS;

  constructor(
    private readonly localService: LocalApiService,
    private readonly siteService: SiteApiService,
    private readonly typeLocalService: TypeLocalApiService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private dialog: MatDialog,
    private toast: NgToastService,
    private readonly transactionService: TransactionApiService,
    private ngxUiLoaderService: NgxUiLoaderService
  ) {}

  @ViewChild('editFormSection') editFormSection!: ElementRef;
  sites: any[] = [];
  typeLocals: TypeLocal[] = [];
  isLoading: boolean = true;
  viewMode: string = 'table';

  originalImageFileName: string | null = null;
  isImageModified = false;

  currentPage: number = 0;
  pageSize: number = 5;
  filteredLocals: any[] = [];
  totalCount: number = 0;

  showEditForm: boolean = false;
  selectedLocal: Local | null = null;
  imageLocalPreview: string | null = null;

  isSubmitting: boolean = false;
  searchTerm: string = '';
  imageLoaded: boolean = false;

  // Table configuration
  tableConfig = {
    keys: ['Nom', 'Etage', 'Capteurs', 'Capacite', 'Site', 'Type', 'NomClient'],
    headers: ['Nom', 'Étage', 'Capteurs', 'Capacité', 'Site', 'Type', 'Nom Client'],
  };  

  currentSort: SortPage[] = [{
    Column: 'Nom',
    Sort: 'desc'
  }];

  editLocalForm = new FormGroup({
    id: new FormControl(''),
    nom: new FormControl('', [Validators.required]),
    description: new FormControl(''),
    floor: new FormControl<number>(0),
    nombreCapteurs: new FormControl<number>(0),
    capacitePersonnes: new FormControl<number>(0),
    imageLocal: new FormControl(''),
    idSite: new FormControl('', [Validators.required]),
    typeLocalId: new FormControl('', [Validators.required]),
    latittude: new FormControl(''),
    longtitude: new FormControl('')
  });

  ngOnInit(): void {
    this.loadLocals();
    this.loadSites();
    this.loadTypeLocals();
  }

  loadLocals(): void {
    this.ngxUiLoaderService.start();
    this.isLoading = true;
    
    const request: Lister = {
      Pagination: {
        CurrentPage: this.currentPage + 1, // API expects 1-based index
        PageSize: this.pageSize
      },
      SortParams: this.currentSort,
      FilterParams: this.buildFilterParams()
    };
    
    this.localService.paginateWithClientInfo(request).pipe(
      finalize(() => {
        this.ngxUiLoaderService.stop();
        this.isLoading = false;
      })
    ).subscribe({
      next: (result: Page<Local>) => {
        this.filteredLocals = result.Content ?? [];
        
        // Update pagination info from response
        if (result.Lister?.Pagination) {
          this.totalCount = result.Lister?.Pagination?.TotalElement ?? 0;
          this.pageSize = result.Lister?.Pagination?.PageSize ?? this.pageSize;
          this.currentPage = (result.Lister?.Pagination?.CurrentPage ?? 1) - 1;

        } else {
          this.totalCount = this.filteredLocals.length;
        }
        
        this.loadSensorsCountForLocals(this.filteredLocals);
      },
      error: (error) => {
        this.showError('Erreur lors du chargement des locaux', 'Erreur');
        this.filteredLocals = [];
        this.totalCount = 0;
      }
    });
  }

  private buildFilterParams(): FilterParam[] {
    const filters: FilterParam[] = [];
    const searchTerm = this.searchTerm.trim();
  
    if (searchTerm) {
      const isNumeric = !isNaN(Number(searchTerm)) && searchTerm !== '';
  
      if (isNumeric) {
        // Search numeric fields
        filters.push(
          { Column: 'Etage', Value: searchTerm, Op: 'eq', AndOr: 'OR' },
          { Column: 'Capteurs', Value: searchTerm, Op: 'eq', AndOr: 'OR' },
          { Column: 'Capacite', Value: searchTerm, Op: 'eq', AndOr: 'OR' }
        );
      } else {
        // Search text fields
        filters.push(
          { Column: 'Nom', Value: searchTerm, Op: 'contains', AndOr: 'OR' },
          { Column: 'Site', Value: searchTerm, Op: 'contains', AndOr: 'OR' },
          { Column: 'Type', Value: searchTerm, Op: 'contains', AndOr: 'OR' },
          { Column: 'NomClient', Value: searchTerm, Op: 'contains', AndOr: 'OR' }
        );
      }
    }
  
    return filters;
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadLocals();
  }

  onSearchKeyup(event: KeyboardEvent): void {
    if (event.key === 'Enter' || this.searchTerm === '') {
      this.currentPage = 0;
      this.loadLocals();
    }
  }

  onSort(column: string): void {
    // Toggle sort direction if sorting the same column
    if (this.currentSort[0]?.Column === column) {
      this.currentSort = [{
        Column: column,
        Sort: this.currentSort[0].Sort === 'asc' ? 'desc' : 'asc'
      }];
    } else {
      // Default to descending when changing column
      this.currentSort = [{
        Column: column,
        Sort: 'desc'
      }];
    }
    this.loadLocals();
  }

  private loadSensorsCountForLocals(locals: any[]): void {
    this.transactionService.getAll().subscribe({
      next: (transactions: Transaction[]) => {
        const localCapteurMap = new Map<string, Set<string>>();

        transactions.forEach((t) => {
          if (!localCapteurMap.has(t.IdLocal)) {
            localCapteurMap.set(t.IdLocal, new Set());
          }
          localCapteurMap.get(t.IdLocal)?.add(t.IdCapteur);
        });

        locals.forEach((local) => {
          const capteurSet = localCapteurMap.get(local.LocalId) ?? new Set();
          local.Capteurs = capteurSet.size;
        });
      },
      error: (err) =>
        console.error(
          'Erreur lors du chargement des transactions pour capteurs:',
          err
        ),
    });
  }

  editLocal(id: string): void {
    const local = this.filteredLocals.find(l => l.LocalId === id);
    
    if (local) {
      this.selectedLocal = {
        Id: local.LocalId,
        Name: local.Nom,
        Floor: local.Etage,
        SensorsCount: local.Capteurs,
        Capacity: local.Capacite,
        IdSite: local.SiteId,
        TypeLocalId: local.Type,
        Description: local.Description,
        Latitude: local.Latitude,
        Longtitude: local.Longitude,
        ImageLocal: local.ImageLocal,
        Architecture2DImage: local.Architecture2DImage
      };
      
      this.showEditForm = true;
      this.isImageModified = false;
  
      this.originalImageFileName = 'Image actuelle';
      this.imageLocalPreview = null;
  
      // Charger l'image enregistrée
      this.localService.downloadImage(local.LocalId).subscribe({
        next: (res) => {
          if (res?.image) {
            this.imageLocalPreview = 'data:image/jpeg;base64,' + res.image;
            this.editLocalForm.patchValue({ imageLocal: this.imageLocalPreview });
          }
        },
        error: (err) => {
          console.warn('Erreur lors du chargement de l image du local :', err);
        }
      });
  
      this.editLocalForm.patchValue({
        id: local.LocalId,
        nom: local.Nom,
        description: local.Description ?? '',
        floor: local.Etage,
        nombreCapteurs: local.Capteurs,
        capacitePersonnes: local.Capacite,
        imageLocal: local.ImageLocal,
        idSite: local.SiteId,
        typeLocalId: local.Type,
        latittude: local.Latitude,
        longtitude: local.Longitude
      });
  
      setTimeout(() => {
        this.editFormSection?.nativeElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }, 100);
    }
  }

  deleteLocal(id: string): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmer la suppression',
        message: 'Êtes-vous sûr de vouloir supprimer ce local ?',
        icon: 'delete',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.localService.delete(id).subscribe({
          next: () => {
            this.loadLocals();
            this.showSuccess(
              'Le local a été supprimé avec succès',
              'Information'
            );
          },
          error: (error) => {
            this.showError('Erreur lors de la suppression du local', 'Erreur');
          },
        });
      }
    });
  }

  viewDetails(id: string): void {
    this.router.navigate(['/local-details/', id]);
  }

  hideEditLocalForm(): void {
    this.showEditForm = false;
    this.editLocalForm.reset();
    this.imageLocalPreview = null;
    this.isSubmitting = false;
    this.selectedLocal = null;
    this.originalImageFileName = null;
    this.isImageModified = false;
  }

  onEditImagesSelected(event: any): void {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];
      const reader = new FileReader();
      this.isImageModified = true;
      this.originalImageFileName = file.name;
  
      reader.onload = (e: any) => {
        this.imageLocalPreview = e.target.result;
        this.editLocalForm.patchValue({ imageLocal: e.target.result });
      };
      reader.readAsDataURL(file);
    }
  }

  submitEditForm(): void {
    if (this.editLocalForm.valid && this.selectedLocal) {
      this.isSubmitting = true;
      const formValues = this.editLocalForm.value;
  
      // Image : conserver l'image existante si non modifiée
      const imageData = this.isImageModified
        ? (formValues.imageLocal?.split(',')[1] ?? '')
        : this.selectedLocal.ImageLocal;
  
      const updatedLocal: Local = {
        ...this.selectedLocal,
        Name: formValues.nom ?? '',
        Floor: formValues.floor ?? 0,
        SensorsCount: formValues.nombreCapteurs ?? 0,
        Capacity: formValues.capacitePersonnes ?? 0,
        ImageLocal: imageData ?? (formValues.imageLocal?.split(',')[1] ?? ''),
        IdSite: formValues.idSite ?? this.selectedLocal.IdSite,
        TypeLocalId: formValues.typeLocalId ?? this.selectedLocal.TypeLocalId,
        Description: formValues.description ?? this.selectedLocal.Description,
      };
  
      console.log('Payload envoyé pour modification :', updatedLocal);
      // D'abord update les données du local
      this.localService.update(updatedLocal).subscribe({
        next: () => {
          // Puis upload l'image uniquement si elle est modifiée
          if (this.isImageModified && imageData && updatedLocal.Id) {
            this.localService.uploadImage(updatedLocal.Id, imageData).subscribe({
              next: () => {
                this.loadLocals();
                this.showSuccess('Le local a été modifié avec succès', 'Information');
                this.hideEditLocalForm();
                this.isSubmitting = false;
              },
              error: (err) => {
                this.showError("Erreur lors de l'envoi de l'image", 'Erreur');
                this.isSubmitting = false;
              }
            });
          } else {
            this.loadLocals();
            this.showSuccess('Le local a été modifié avec succès', 'Information');
            this.hideEditLocalForm();
            this.isSubmitting = false;
          }
        },
        error: (error) => {
          this.isSubmitting = false;
          this.showError('Erreur lors de la modification du local', 'Erreur');
        }
      });
    }
  }

  handleAction(event: { action: string; row: any }): void {
    const { action, row } = event;
    if (action === 'edit') {
      this.editLocal(row.LocalId);
    } else if (action === 'delete') {
      this.deleteLocal(row.LocalId);
    } else if (action === 'view') {
      this.viewDetails(row.LocalId); 
    }
  }

  private showSuccess(message: string, title: string) {
    this.toast.success(message, title, 3000, false);
  }

  private showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }

  onImageError(event: any): void {
    event.target.src = 'assets/images/placeholder.png';
  }

  onImageLoad(): void {
    this.imageLoaded = true;
  }

  loadSites(): void {
    this.isLoading = true;
    this.siteService.getAll().subscribe({
      next: (sites) => {
        this.sites = sites;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading sites:', error);
        this.isLoading = false;
      },
    });
  }

  loadTypeLocals(): void {
    this.isLoading = true;
    this.typeLocalService.getAll().subscribe({
      next: (typeLocals) => {
        this.typeLocals = typeLocals;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading typeLocals:', error);
        this.isLoading = false;
      },
    });
  }
}