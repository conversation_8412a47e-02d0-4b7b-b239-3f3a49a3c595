import { CommonModule } from '@angular/common';
import { Component, OnInit, HostListener } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { Client } from '@app/core/models/client';
import { Licence } from '@app/core/models/licence';
import { Option } from '@app/core/models/option';
import { LicenceOption } from '@app/core/models/licenceOption';
import { Subscription } from '@app/core/models/subscription';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { LicenceOptionApiService } from '@app/core/services/administrative/licenceOption.service';
import { forkJoin, of } from 'rxjs';
import { catchError, map, switchMap, finalize } from 'rxjs/operators';
import { MatIcon } from '@angular/material/icon';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS,
} from 'ng-angular-popup';
import { OptionApiService } from '@app/core/services/administrative/option.service';
import { Lister } from '@app/shared/models/rule/Lister';

interface DragData {
  option: Option;
  sourceLicenceId: string;
  sourceIndex: number;
}

@Component({
  selector: 'app-liste',
  standalone: true,
  imports: [FormsModule, CommonModule, MatIcon, NgToastComponent],
  templateUrl: './liste.component.html',
  styleUrls: ['./liste.component.css'],
})
export class ListeComponent implements OnInit {
  clients: Client[] = [];
  filteredClients: Client[] = [];
  licences: (Licence & { OptionsDisplay?: string; Options?: Option[] })[] = [];
  licenceOptions: LicenceOption[] = []; // Store all licence options for easier lookup
  assignedLicences: { [idClient: string]: string } = {};
  subscriptions: Subscription[] = [];
  clientSubscription: Subscription | null = null;

  isLoading = false;
  isUpdating = false;
  isDeleting = false;
  isSubmitting = false;
  isRefreshing: { [key: string]: boolean } = {};

  isModalOpen = false;
  modalMode: 'add' | 'edit' | 'view' = 'add';
  formData: any = {
    Name: '',
    Description: '',
    Options: [] as Option[],
    OriginalLicenceOptions: [] as LicenceOption[],
    OptionsToDelete: [] as Option[], // Track options marked for deletion
  };

  TOAST_POSITIONS = TOAST_POSITIONS;

  isDeleteModalOpen = false;
  itemToDelete: any = null;

  // Enhanced drag data
  dragData: DragData | null = null;
  isDragging = false;
  dragOverLicenceId: string | null = null;

  // Move/Copy modal
  isMoveModalOpen = false;
  moveModalData: {
    option: Option;
    sourceLicenceId: string;
    targetLicenceId: string;
    sourceLicenceName: string;
    targetLicenceName: string;
  } | null = null;

  public tableHeaders: string[] = ['Nom', 'Description', 'Options'];
  public tableKeys: string[] = ['Name', 'Description', 'OptionsDisplay'];
  public tableActions: string[] = ['view', 'edit', 'delete', 'refresh'];

  @HostListener('document:keydown.escape', ['$event'])
  handleEscapeKey(event: KeyboardEvent): void {
    this.onEscape();
  }

  constructor(
    private clientApiService: ClientApiService,
    private licenceApiService: LicenceApiService,
    private subscriptionApiService: SubscriptionApiService,
    private licenceOptionApiService: LicenceOptionApiService,
    private optionApiService: OptionApiService,
    private toast: NgToastService
  ) {}

  ngOnInit(): void {
    this.loadInitialData();
  }

  onEscape() {
    this.isModalOpen = false;
    this.isDeleteModalOpen = false;
    this.isMoveModalOpen = false;
  }

  trackByLicence(index: number, licence: Licence): string {
    return licence.Id || index.toString();
  }

  trackByOption(index: number, option: Option): string {
    return option.Id || index.toString();
  }

  loadInitialData(): void {
    this.isLoading = true;

    const lister: Lister<any> = {
      Pagination: {
        CurrentPage: 1,
        PageSize: 50,
        TotalElement: 0,
        PageCount: 0,
        IsFirst: true,
        IsLast: false,
        StartIndex: 0,
      },
      FilterParams: [],
      SortParams: [],
    };

    forkJoin([
      this.clientApiService.gatePage(lister),
      this.licenceApiService.gatePage(lister),
      this.subscriptionApiService.gatePage(lister),
      this.licenceOptionApiService.getAll(),
    ]).subscribe({
      next: ([clientPage, licencePage, subscriptionPage, licenceOptions]) => {
        this.clients = clientPage.Content ?? [];
        this.filteredClients = [...this.clients];
        this.subscriptions = subscriptionPage.Content ?? [];
        this.licenceOptions = licenceOptions;

        this.licences = (licencePage.Content ?? []).map((licence) => {
          const options = (licence.LicenceOptions || [])
            .filter((lo) => lo.Option)
            .map((lo) => lo.Option!);
          return {
            ...licence,
            Options: options,
            OptionsDisplay: this.formatOptionsForDisplay(options),
          };
        });
      },
      error: (err) => {
        console.error('Error loading data:', err);
      },
      complete: () => {
        this.isLoading = false;
      },
    });
  }

  onDragStart(
    event: DragEvent,
    option: Option,
    licenceId: string,
    index: number
  ) {
    this.dragData = {
      option: { ...option },
      sourceLicenceId: licenceId,
      sourceIndex: index,
    };
    this.isDragging = true;

    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.setData('text/plain', JSON.stringify(option));
    }

    const target = event.target as HTMLElement;
    target.classList.add('dragging');
  }

  onDragEnd(event: DragEvent) {
    this.isDragging = false;
    this.dragOverLicenceId = null;

    const target = event.target as HTMLElement;
    target.classList.remove('dragging');
  }

  onDragOver(event: DragEvent, licenceId?: string) {
    event.preventDefault();

    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }

    if (licenceId) {
      this.dragOverLicenceId = licenceId;
    }
  }

  onDragLeave(event: DragEvent, licenceId?: string) {
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
    const x = event.clientX;
    const y = event.clientY;

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      if (licenceId === this.dragOverLicenceId) {
        this.dragOverLicenceId = null;
      }
    }
  }

  onDrop(event: DragEvent, targetLicenceId: string, dropIndex?: number) {
    event.preventDefault();
    this.dragOverLicenceId = null;

    if (!this.dragData) {
      return;
    }

    const sourceLicenceId = this.dragData.sourceLicenceId;
    const option = this.dragData.option;

    if (sourceLicenceId === targetLicenceId) {
      const targetLicence = this.licences.find((l) => l.Id === targetLicenceId);
      const currentOptionsCount = targetLicence?.Options?.length || 0;
      const finalDropIndex =
        dropIndex !== undefined ? dropIndex : currentOptionsCount;

      this.reorderOption(
        sourceLicenceId,
        this.dragData.sourceIndex,
        finalDropIndex
      );
    } else {
      const sourceLicence = this.licences.find((l) => l.Id === sourceLicenceId);
      const targetLicence = this.licences.find((l) => l.Id === targetLicenceId);

      if (sourceLicence && targetLicence) {
        this.moveModalData = {
          option: option,
          sourceLicenceId: sourceLicenceId,
          targetLicenceId: targetLicenceId,
          sourceLicenceName: sourceLicence.Name,
          targetLicenceName: targetLicence.Name,
        };
        this.isMoveModalOpen = true;
      }
    }

    this.dragData = null;
    this.isDragging = false;
  }

  onDropInOptionsList(event: DragEvent, licenceId: string, dropIndex: number) {
    event.preventDefault();
    event.stopPropagation();

    if (!this.dragData) {
      return;
    }

    if (this.dragData.sourceLicenceId === licenceId) {
      this.reorderOption(licenceId, this.dragData.sourceIndex, dropIndex);
    }

    this.dragData = null;
    this.isDragging = false;
    this.dragOverLicenceId = null;
  }

  private async reorderOption(
    licenceId: string,
    fromIndex: number,
    toIndex: number
  ) {
    if (fromIndex === toIndex) {
      return;
    }

    const licence = this.licences.find((l) => l.Id === licenceId);
    if (!licence || !licence.Options) {
      return;
    }

    const options = [...licence.Options];
    const [movedOption] = options.splice(fromIndex, 1);
    options.splice(toIndex, 0, movedOption);

    licence.Options = options;
    licence.OptionsDisplay = this.formatOptionsForDisplay(options);

    try {
      await this.updateOptionOrder(licenceId, options);
      this.showSuccess(
        'Ordre des options mis à jour avec succès',
        'Réorganisation'
      );
    } catch (error) {
      console.error('Error updating option order:', error);
      this.showError(
        "Erreur lors de la mise à jour de l'ordre des options",
        'Erreur'
      );
      this.refreshSingleLicence(licence);
    }
  }

  private async updateOptionOrder(
    licenceId: string,
    orderedOptions: Option[]
  ): Promise<void> {
    const existingLicenceOptions = this.licenceOptions.filter(
      (lo) => lo.LicenceId === licenceId
    );

    const deletePromises = existingLicenceOptions.map((lo) =>
      this.licenceOptionApiService
        .delete(lo.Id!)
        .pipe(
          catchError((err) => {
            console.error(
              `Error deleting existing LicenceOption ${lo.Id}:`,
              err
            );
            return of(null);
          })
        )
        .toPromise()
    );

    await Promise.all(deletePromises);

    this.licenceOptions = this.licenceOptions.filter(
      (lo) => lo.LicenceId !== licenceId
    );

    const createPromises = orderedOptions.map(async (option, index) => {
      if (!option.Id) {
        console.warn(
          `Option ${option.Name} has no ID, cannot create LicenceOption link.`
        );
        return Promise.resolve();
      }
      try {
        const newLO = await this.licenceOptionApiService
          .create({
            LicenceId: licenceId,
            OptionId: option.Id,
          })
          .toPromise();
        if (newLO) {
          this.licenceOptions.push(newLO);
        }
      } catch (error) {
        console.error(
          `Failed to re-create LicenceOption for OptionId ${option.Id}:`,
          error
        );
        throw error;
      }
    });

    await Promise.all(createPromises);
  }

  confirmMove() {
    if (!this.moveModalData) return;
    this.executeMove(this.moveModalData);
    this.closeMoveModal();
  }

  confirmCopy() {
    if (!this.moveModalData) return;
    this.executeCopy(this.moveModalData);
    this.closeMoveModal();
  }

  closeMoveModal() {
    this.isMoveModalOpen = false;
    this.moveModalData = null;
  }

  private async executeMove(data: NonNullable<typeof this.moveModalData>) {
    try {
      const isOptionCurrentlyInUse = await this.isLicenceInUse(
        data.sourceLicenceId
      );

      if (isOptionCurrentlyInUse) {
        this.showError(
          'Impossible de déplacer une option si la licence source a des souscriptions actives ou en attente.',
          'Déplacement bloqué'
        );
        return;
      }

      await this.removeOptionFromLicence(data.sourceLicenceId, data.option.Id!);

      await this.addOptionToLicence(data.targetLicenceId, data.option);

      this.updateLocalStateAfterMove(data);

      this.showSuccess(
        `Option "${data.option.Name}" déplacée vers "${data.targetLicenceName}"`,
        'Déplacement réussi'
      );
    } catch (error) {
      console.error('Error moving option:', error);
      this.showError("Erreur lors du déplacement de l'option", 'Erreur');
      this.refreshSingleLicenceById(data.sourceLicenceId);
      this.refreshSingleLicenceById(data.targetLicenceId);
    }
  }

  private async executeCopy(data: NonNullable<typeof this.moveModalData>) {
    try {
      const copiedOption = await this.createOptionCopy(data.option);

      await this.addOptionToLicence(data.targetLicenceId, copiedOption);

      this.updateLocalStateAfterCopy(data, copiedOption);

      this.showSuccess(
        `Option "${data.option.Name}" copiée vers "${data.targetLicenceName}"`,
        'Copie réussie'
      );
    } catch (error) {
      console.error('Error copying option:', error);
      this.showError("Erreur lors de la copie de l'option", 'Erreur');
      this.refreshSingleLicenceById(data.targetLicenceId);
    }
  }

  private async isLicenceInUse(licenceId: string): Promise<boolean> {
    return this.subscriptions.some(
      (sub) =>
        sub.LicenceId === licenceId &&
        (sub.Status === 'Payé' || sub.Status === 'En attente')
    );
  }

  private async removeOptionFromLicence(
    licenceId: string,
    optionId: string
  ): Promise<void> {
    const licenceOption = this.licenceOptions.find(
      (lo) => lo.LicenceId === licenceId && lo.OptionId === optionId
    );

    if (licenceOption && licenceOption.Id) {
      await this.licenceOptionApiService.delete(licenceOption.Id).toPromise();
      this.licenceOptions = this.licenceOptions.filter(
        (lo) => lo.Id !== licenceOption.Id
      );
    } else {
      console.warn(
        `LicenceOption to remove not found for LicenceId: ${licenceId}, OptionId: ${optionId}`
      );
    }
  }

  private async addOptionToLicence(
    licenceId: string,
    option: Option
  ): Promise<void> {
    if (!option.Id) {
      console.error(
        `Cannot add option without an ID to LicenceOption: ${option.Name}`
      );
      throw new Error('Option ID is missing for LicenceOption creation');
    }

    const newLicenceOption: Partial<LicenceOption> = {
      LicenceId: licenceId,
      OptionId: option.Id,
    };

    const createdLO = await this.licenceOptionApiService
      .create(newLicenceOption as LicenceOption)
      .toPromise();
    if (createdLO) {
      this.licenceOptions.push(createdLO);
    }
  }

  private async createOptionCopy(originalOption: Option): Promise<Option> {
    const copyData = {
      Name: `${originalOption.Name}`,
      Price: originalOption.Price,
      Type: originalOption.Type,
      Value: originalOption.Value,
    };

    const result = await this.optionApiService.create(copyData).toPromise();
    if (!result) {
      throw new Error('Failed to create option copy: result is undefined');
    }
    return result;
  }

  private updateLocalStateAfterMove(
    data: NonNullable<typeof this.moveModalData>
  ) {
    const sourceLicence = this.licences.find(
      (l) => l.Id === data.sourceLicenceId
    );
    if (sourceLicence && sourceLicence.Options) {
      sourceLicence.Options = sourceLicence.Options.filter(
        (opt) => opt.Id !== data.option.Id
      );
      sourceLicence.OptionsDisplay = this.formatOptionsForDisplay(
        sourceLicence.Options
      );
    }

    const targetLicence = this.licences.find(
      (l) => l.Id === data.targetLicenceId
    );
    if (targetLicence) {
      if (!targetLicence.Options) {
        targetLicence.Options = [];
      }
      targetLicence.Options.push(data.option);

      targetLicence.OptionsDisplay = this.formatOptionsForDisplay(
        targetLicence.Options
      );
    }
  }

  private updateLocalStateAfterCopy(
    data: NonNullable<typeof this.moveModalData>,
    copiedOption: Option
  ) {
    const targetLicence = this.licences.find(
      (l) => l.Id === data.targetLicenceId
    );
    if (targetLicence) {
      if (!targetLicence.Options) {
        targetLicence.Options = [];
      }
      targetLicence.Options.push(copiedOption);
      targetLicence.OptionsDisplay = this.formatOptionsForDisplay(
        targetLicence.Options
      );
    }
  }

  addOption() {
    this.formData.Options.push({
      Name: '',
      Price: 0,
    });
  }

  removeOption(index: number) {
    const optionToRemove = this.formData.Options[index];

    // If this is an existing option (has an ID) in edit mode, add it to deletion queue
    if (this.modalMode === 'edit' && optionToRemove.Id) {
      // Move to deletion queue instead of deleting immediately
      if (!this.formData.OptionsToDelete) {
        this.formData.OptionsToDelete = [];
      }
      this.formData.OptionsToDelete.push(optionToRemove);
    }

    // Remove from form display
    this.formData.Options.splice(index, 1);
  }

  // Helper method to check if an option is marked for deletion
  isOptionMarkedForDeletion(option: Option): boolean {
    return (
      this.formData.OptionsToDelete?.some(
        (opt: Option) => opt.Id === option.Id
      ) || false
    );
  }

  private async isOptionUsedByAnySubscription(
    optionId: string
  ): Promise<boolean> {
    const relevantLicenceOptions = this.licenceOptions.filter(
      (lo) => lo.OptionId === optionId
    );

    const activeSubscriptions = this.subscriptions.filter(
      (sub) => sub.Status === 'Payé' || sub.Status === 'En attente'
    );

    const isUsed = relevantLicenceOptions.some((lo) => {
      const licenceHasActiveSub = activeSubscriptions.some(
        (sub) => sub.LicenceId === lo.LicenceId
      );

      if (licenceHasActiveSub && lo.Option) {
        if (lo.Option.Price > 0) {
          return true;
        }
      }
      return false;
    });

    return isUsed;
  }

  private showSuccess(message: string, title: string) {
    this.toast.success(message, title, 3000, false);
  }

  private showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }

  getOptionsCount(options: Option[]): string {
    if (!options || options.length === 0) return 'Aucune option';
    return `${options.length} option${options.length > 1 ? 's' : ''}`;
  }

  formatOptionsForDisplay(options: Option[]): string {
    if (!options || options.length === 0) return 'Aucune option';
    return options
      .map((opt) => {
        let base = `${opt.Name} (${opt.Price}€)`;
        if (opt.Type === 'max-controllers-server') {
          base += ` - Max: ${opt.Value}`;
        }
        if (opt.Type === 'max-controllers') {
          base += ` - Max: ${opt.Value}`;
        }
        return base;
      })
      .join(', ');
  }

  onActionTriggered(event: { action: string; row: any }) {
    switch (event.action) {
      case 'view':
        this.viewLicence(event.row);
        break;
      case 'edit':
        this.openEditModal(event.row);
        break;
      case 'delete':
        this.openDeleteModal(event.row);
        break;
      case 'refresh':
        this.refreshSingleLicence(event.row);
        break;
    }
  }

  refreshSingleLicence(licence: any) {
    this.isRefreshing[licence.Id] = true;

    this.licenceApiService.getById(licence.Id).subscribe({
      next: (updatedLicence: Licence) => {
        // Assume backend delivers LicenceOptions in a consistent order
        const options = (updatedLicence.LicenceOptions || [])
          .filter((lo) => lo.Option) // Ensure Option data is present
          .map((lo) => lo.Option!);
        const index = this.licences.findIndex(
          (l) => l.Id === updatedLicence.Id
        );

        if (index !== -1) {
          this.licences[index] = {
            ...updatedLicence,
            Options: options,
            OptionsDisplay: this.formatOptionsForDisplay(options),
          };
          // Update the global licenceOptions array as well
          this.licenceOptions = [
            ...this.licenceOptions.filter(
              (lo) => lo.LicenceId !== updatedLicence.Id
            ),
            ...(updatedLicence.LicenceOptions || []),
          ];
        }
      },
      error: (error) => {
        console.error('Error refreshing licence:', error);
      },
      complete: () => {
        this.isRefreshing[licence.Id] = false;
      },
    });
  }

  // Helper to refresh by ID
  private refreshSingleLicenceById(licenceId: string) {
    const licence = this.licences.find((l) => l.Id === licenceId);
    if (licence) {
      this.refreshSingleLicence(licence);
    }
  }

  viewLicence(row: any) {
    this.modalMode = 'view';
    this.formData = {
      ...row,
      Options: row.Options
        ? row.Options.map((opt: Option) => ({ ...opt }))
        : [],
      OptionsToDelete: [], // Initialize empty deletion queue
    };
    this.isModalOpen = true;
  }

  openEditModal(row: any) {
    console.log('Opening edit modal for:', row);
    this.modalMode = 'edit';
    this.formData = {
      Id: row.Id,
      Name: row.Name,
      Description: row.Description,
      Options: row.Options
        ? row.Options.map((opt: any) => ({
            ...opt,
            Name: opt.Name || '',
            Price: opt.Price || 0,
          }))
        : [],
      OriginalLicenceOptions: row.LicenceOptions ? [...row.LicenceOptions] : [],
      OptionsToDelete: [], // Initialize empty deletion queue
    };
    console.log('Form data set to:', this.formData);
    this.isModalOpen = true;
  }

  openDeleteModal(row: any) {
    this.itemToDelete = row;
    this.isDeleteModalOpen = true;
  }

  confirmDelete() {
    if (this.itemToDelete) {
      const hasBlockingSubscription = this.subscriptions.some(
        (sub) =>
          sub.LicenceId === this.itemToDelete.Id &&
          (sub.Status === 'Payé' || sub.Status === 'En attente')
      );

      if (hasBlockingSubscription) {
        this.showError(
          'Impossible de supprimer une licence avec une souscription active ou en attente.',
          'Suppression bloquée'
        );
        return;
      }

      this.isDeleting = true;

      this.licenceApiService.delete(this.itemToDelete.Id).subscribe({
        next: () => {
          this.licences = this.licences.filter(
            (lic) => lic.Id !== this.itemToDelete.Id
          );
          // Also remove associated licenceOptions
          this.licenceOptions = this.licenceOptions.filter(
            (lo) => lo.LicenceId !== this.itemToDelete.Id
          );
          this.showSuccess('Licence supprimée avec succès', 'Suppression');
          // Close the delete modal after successful deletion
          this.isDeleteModalOpen = false;
          this.itemToDelete = null;
        },
        error: (error) => {
          console.error('Error deleting licence:', error);
          this.showError(
            'Erreur lors de la suppression de la licence',
            'Erreur'
          );
          this.isDeleting = false;
        },
        complete: () => {
          this.isDeleting = false;
        },
      });
    }
  }

  refreshData() {
    this.isLoading = true;
    this.loadInitialData();
  }

  submitForm() {
    if (this.modalMode === 'add') {
      this.addLicence();
    } else if (this.modalMode === 'edit') {
      this.updateLicence();
    }
  }

  addLicence() {
    this.isSubmitting = true;

    const validOptions = this.formData.Options.filter(
      (opt: Option) => opt.Name.trim() !== ''
    );

    const licenceOptions = validOptions.map((opt: any) => ({
      Option: opt,
    }));

    const licenceData = {
      ...this.formData,
      LicenceOptions: licenceOptions,
    };

    this.licenceApiService.create(licenceData).subscribe({
      next: (newLicence: Licence) => {
        const options = (newLicence.LicenceOptions || [])
          .filter((lo) => lo.Option)
          .map((lo) => lo.Option!);

        this.licences.push({
          ...newLicence,
          Options: options,
          OptionsDisplay: this.formatOptionsForDisplay(options),
        });
        if (newLicence.LicenceOptions) {
          this.licenceOptions.push(...newLicence.LicenceOptions);
        }
        this.closeModal();
        this.showSuccess('Licence ajoutée avec succès', 'Ajout');
      },
      error: (error) => {
        console.error('Error adding licence:', error);
        this.showError("Erreur lors de l'ajout de la licence", 'Erreur');
        this.isSubmitting = false;
      },
      complete: () => {
        this.isSubmitting = false;
      },
    });
  }

  updateLicence() {
    this.isUpdating = true;
    this.isSubmitting = true;

    const licenceId = this.formData.Id;

    const currentOptionsInForm: Option[] = this.formData.Options.filter(
      (opt: Option) => opt.Name.trim() !== '' && opt.Price >= 0
    );

    const originalLicenceOptions: LicenceOption[] =
      this.formData.OriginalLicenceOptions || [];

    // Handle options marked for deletion
    const optionsToDelete: Option[] = this.formData.OptionsToDelete || [];

    // Process deletions with usage check
    // Alternative: Replace the deletionChecks with async/await for cleaner code

    const deletionChecks = optionsToDelete.map(async (optionToRemove) => {
      if (!optionToRemove.Id) return;

      const licenceOptionLink = this.licenceOptions.find(
        (lo) => lo.LicenceId === licenceId && lo.OptionId === optionToRemove.Id
      );

      if (!licenceOptionLink || !licenceOptionLink.Id) {
        console.warn(
          `LicenceOption link not found for option ${optionToRemove.Name} on licence ${licenceId}.`
        );
        return;
      }

      const optionIsUsed = await this.isOptionUsedByAnySubscription(
        optionToRemove.Id
      );

      if (optionIsUsed) {
        throw new Error(
          `Impossible de supprimer l'option "${optionToRemove.Name}" car elle est utilisée par une ou plusieurs souscriptions actives.`
        );
      }

      try {
        // Delete the LicenceOption link
        await this.licenceOptionApiService
          .delete(licenceOptionLink.Id!)
          .toPromise();

        // Remove from local arrays
        this.licenceOptions = this.licenceOptions.filter(
          (lo) => lo.Id !== licenceOptionLink.Id
        );

        // Check if option should be deleted globally
        const allLicenceOptions = await this.licenceOptionApiService
          .getAll()
          .toPromise();
        const isOptionStillLinkedToAnyLicence = allLicenceOptions?.some(
          (lo) => lo.OptionId === optionToRemove.Id
        );

        if (!isOptionStillLinkedToAnyLicence) {
          await this.optionApiService.delete(optionToRemove.Id!).toPromise();
        }
      } catch (error) {
        console.error(`Error deleting option ${optionToRemove.Name}:`, error);
        throw error;
      }
    });

    Promise.all(deletionChecks)
      .then(() => {
        // Continue with the rest of the update logic after deletions are processed

        // 1. LicenceOptions to delete (user removed some, excluding those already processed above)
        const remainingLicenceOptionsToDelete = originalLicenceOptions.filter(
          (originalLO: LicenceOption) =>
            !currentOptionsInForm.some(
              (formOpt: Option) => formOpt.Id === originalLO.OptionId
            ) &&
            !optionsToDelete.some(
              (deletedOpt: Option) => deletedOpt.Id === originalLO.OptionId
            )
        );

        const deleteCalls = remainingLicenceOptionsToDelete.map((lo) =>
          this.licenceOptionApiService.delete(lo.Id!).pipe(
            catchError((err) => {
              console.error(
                `Error deleting LicenceOption with ID ${lo.Id}:`,
                err
              );
              this.showError(
                `Échec de la suppression de l'option de liaison: ${lo.Option?.Name}`,
                'Erreur'
              );
              return of(null);
            })
          )
        );

        // 2. Update existing Options (with Id)
        const updateOptionCalls = currentOptionsInForm
          .filter((opt: Option) => !!opt.Id)
          .map((opt: Option) => {
            const payload: Option = {
              Id: opt.Id,
              Name: opt.Name,
              Price: opt.Price,
              Type: opt.Type,
              Value: opt.Value,
            };
            return this.optionApiService.update(payload).pipe(
              catchError((err) => {
                console.error(`Error updating Option with ID ${opt.Id}:`, err);
                this.showError(
                  `Échec de la mise à jour de l'option: ${opt.Name}`,
                  'Erreur'
                );
                return of(null);
              })
            );
          });

        // 3. Create new Options (without Id)
        const createOptionCalls = currentOptionsInForm
          .filter((opt: Option) => !opt.Id)
          .map((opt: Option) => {
            const payload: Option = {
              Name: opt.Name,
              Price: opt.Price,
              Type: opt.Type,
              Value: opt.Value,
              Id: opt.Id,
            };
            return this.optionApiService.create(payload).pipe(
              map((createdOpt: Option) => {
                opt.Id = createdOpt.Id;
                return createdOpt;
              }),
              catchError((err) => {
                console.error('Error creating Option:', err);
                this.showError(
                  `Échec de la création de l'option: ${opt.Name}`,
                  'Erreur'
                );
                return of(null);
              })
            );
          });

        forkJoin([...deleteCalls, ...updateOptionCalls, ...createOptionCalls])
          .pipe(
            switchMap(() => {
              // 4. Create LicenceOption links for all current Options (no 'Order' field)
              const licenceOptionCreateCalls = currentOptionsInForm.map(
                (opt: Option) => {
                  const existingLO = originalLicenceOptions.find(
                    (lo) =>
                      lo.OptionId === opt.Id &&
                      !optionsToDelete.some(
                        (deletedOpt: Option) => deletedOpt.Id === opt.Id
                      )
                  );

                  if (existingLO) {
                    // LicenceOption exists, no need to create again unless link was deleted
                    // If it was in `licenceOptionsToDelete`, it would have been deleted
                    // So if it's here, it implies it already exists and is fine.
                    return of(existingLO);
                  } else {
                    // Create new LicenceOption link
                    if (!opt.Id) {
                      // This should ideally not happen if createOptionCalls succeeded, but for safety
                      console.error(
                        `Option ${opt.Name} has no ID after creation attempt, cannot link.`
                      );
                      return of(null);
                    }
                    return this.licenceOptionApiService
                      .create({
                        LicenceId: licenceId,
                        OptionId: opt.Id,
                        // No 'Order' field
                      })
                      .pipe(
                        catchError((err) => {
                          console.error(
                            `Error creating LicenceOption for Option ID ${opt.Id}:`,
                            err
                          );
                          this.showError(
                            `Échec de la création de liaison pour l'option: ${opt.Name}`,
                            'Erreur'
                          );
                          return of(null);
                        })
                      );
                  }
                }
              );

              return forkJoin(licenceOptionCreateCalls).pipe(
                switchMap(() => this.licenceApiService.getById(licenceId)) // Fetch updated licence data including new LOs
              );
            }),
            switchMap((fetchedLicence: Licence) => {
              // 5. Update Licence main data (name, description)
              const finalUpdatePayload = {
                Id: licenceId,
                Name: this.formData.Name,
                Description: this.formData.Description,
                LicenceOptions: (fetchedLicence.LicenceOptions || []).map(
                  (lo) => ({
                    Id: lo.Id,
                    OptionId: lo.OptionId,
                    Option: lo.Option,
                    // No 'Order' field included here
                  })
                ),
              };
              return this.licenceApiService.update(finalUpdatePayload);
            }),
            map((updatedLicence: Licence) => {
              const options = (updatedLicence.LicenceOptions || [])
                .filter((lo) => lo.Option)
                .map((lo) => lo.Option!);

              const index = this.licences.findIndex(
                (l) => l.Id === updatedLicence.Id
              );

              if (index !== -1) {
                this.licences[index] = {
                  ...updatedLicence,
                  Options: options,
                  OptionsDisplay: this.formatOptionsForDisplay(options),
                };
              }
              // Update the global licenceOptions array as well
              this.licenceOptions = [
                ...this.licenceOptions.filter(
                  (lo) => lo.LicenceId !== updatedLicence.Id
                ),
                ...(updatedLicence.LicenceOptions || []),
              ];
              this.closeModal();
              this.showSuccess(
                'Licence mise à jour avec succès',
                'Mise à jour'
              );
            }),
            catchError((error) => {
              console.error('❌ Error updating licence or options:', error);
              this.showError(
                'Erreur lors de la mise à jour de la licence',
                'Erreur'
              );
              return of(null);
            }),
            finalize(() => {
              this.isUpdating = false;
              this.isSubmitting = false;
            })
          )
          .subscribe();
      })
      .catch((error) => {
        console.error('Error during option deletion checks:', error);
        this.showError(
          error.message || 'Erreur lors de la suppression des options',
          'Erreur'
        );
        this.isUpdating = false;
        this.isSubmitting = false;
      });
  }

  closeModal() {
    this.isModalOpen = false;
    this.formData = {
      Name: '',
      Description: '',
      Options: [] as Option[],
      OriginalLicenceOptions: [] as LicenceOption[],
      OptionsToDelete: [] as Option[], // Reset deletion queue
    };
  }

  private isOptionUsed(optionId: string): boolean {
    const licencesUsingOption = this.licenceOptions
      .filter((lo) => lo.OptionId === optionId)
      .map((lo) => lo.LicenceId);

    return this.subscriptions.some(
      (sub) =>
        licencesUsingOption.includes(sub.LicenceId) &&
        (sub.Status === 'Payé' || sub.Status === 'En attente')
    );
  }
}
