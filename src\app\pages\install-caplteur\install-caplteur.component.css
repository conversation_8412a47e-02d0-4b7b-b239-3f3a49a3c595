.create-button {
  background: linear-gradient(45deg, var(--primary), #81C784);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

/* Container principal */
.plan-editor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--background-light);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text-primary);
  
}

/* Header amélioré */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem;
  margin-left: 1rem;
  background: var(--background-white);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  z-index: 10;
  gap: 2rem;
}

.header-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0;
  font-size: 1.30rem;
  font-weight: 700;
  color: var(--text-primary);
}

.title-icon {
  font-size: 2rem !important;
  color: #63b18c;
}

/* Sélecteurs de contexte améliorés */
.context-selectors {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.50rem;
  min-width: 0;
}

.selector-group {
  position: relative;
  min-width: 0;


}

.selector-group.has-selection .dropdown-trigger {
  border-color: var(--success-color);
  background: rgba(5, 150, 105, 0.05);
}

.selector-group.has-selection .selected-text {
  color: var(--success-color);
  font-weight: 00;
}

.selector-label {
  display: block;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dropdown {
  position: relative;
  width: 100%;
  height: 50%;
}

.dropdown.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.dropdown-trigger {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.875rem 1rem;
  border: 2px solid var(--border-color);
  background: var(--background-white);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
  text-align: left;
  min-height: 3.25rem;
}

.dropdown-trigger:hover:not(:disabled) {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.03);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.dropdown.open .dropdown-trigger {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.08);
  box-shadow: var(--shadow-md);
}

.selected-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.dropdown-icon {
  margin-left: 0.5rem;
  color: var(--text-secondary);
  transition: transform 0.3s ease;
}

.dropdown.open .dropdown-icon {
  transform: rotate(180deg);
  color: var(--primary-color);
}

/* Menu dropdown amélioré */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--background-white);
  border: 2px solid var(--primary-color);
  border-top: none;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  max-height: 300px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: dropdownSlideIn 0.2s ease-out;
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.search-box {
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  background-color: #ffffff;
}

.search-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: calc(var(--border-radius) - 2px);
  font-size: 0.875rem;
  background: var(--background-white);
  transition: var(--transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dropdown-options {
  flex: 1;
  overflow-y: auto;
  max-height: 220px;
  background-color: #ffffff;
}

.dropdown-option {
  padding: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dropdown-option:hover {
  background: #d2d2d3;
  transform: translateX(4px);
}

.dropdown-option:last-child {
  border-bottom: none;
}

.option-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.option-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.option-code,
.option-type,
.option-floor {
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
  border-radius: 999px;
  font-weight: 500;
}

.option-meta {
  font-size: 0.75rem;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.option-area {
  font-weight: 600;
  color: var(--success-color);
}

.no-results {
  padding: 1rem;
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  font-size: 0.875rem;
}

/* Section des actions avec appairage */
.header-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-end;
  min-width: 300px;

}

.pairing-container {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  align-items: flex-end;
}

.buttons-container {
  display: flex;
  flex-direction: row;
  /* ou row si tu veux côte à côte */
  gap: 8px;
  /* réduit l'espace entre les éléments */
  max-height: 40px;
  /* ou la largeur désirée */
}



/* Bouton d'appairage amélioré */
.pairing-btn {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 8px;
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  background: var(--primary);
  color: white;

  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: var(--transition);
  overflow: hidden;
  min-width: 200px;
  max-height: 40px;
  border: 0px;
}



.pairing-btn:hover {
  background: #3f9978;
  background: linear-gradient(45deg, #81c784, var(--primary)) !important;
  transform: translateY(-2px);

}

.pairing-btn.active {
  background: #B91C1C;
 

}





.btn-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  z-index: 2;
  position: relative;
}

.btn-icon {
  font-size: 1.25rem !important;
}

/* Animations d'ondes pour l'appairage */
.pairing-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.wave {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: waveAnimation 2s infinite;
}

.wave-1 {
  animation-delay: 0s;
}

.wave-2 {
  animation-delay: 0.7s;
}

.wave-3 {
  animation-delay: 1.4s;
}

@keyframes waveAnimation {
  0% {
    width: 20px;
    height: 20px;
    opacity: 1;
  }

  100% {
    width: 200px;
    height: 200px;
    opacity: 0;
  }
}

/* Statut d'appairage */
.pairing-status {
  background: var(--background-white);
  border: 1px solid var(--primary-color);
  border-radius: 8px;
  text-align: center;


  box-shadow: var(--shadow-md);
  min-width: 280px;
  animation: slideInRight 0.3s ease-out;
  max-height: 40px;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.progress-container {
  display: flex;
  flex-direction: column;

}

.progress-bar {
  width: 100%;
  height: 6px;
  background: var(--background-light);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--success-color));
  border-radius: 3px;
  transition: width 0.3s ease;
  animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
  0% {
    background-position: -200px 0;
  }

  100% {
    background-position: 200px 0;
  }
}

.progress-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  padding-bottom: 5px;
  padding-left: 5px;
}

.devices-found {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 10px;
  background: rgba(5, 150, 105, 0.1);
  border-radius: var(--border-radius);
  color: var(--success-color);
  font-weight: 600;
  font-size: 0.875rem;
  animation: bounceIn 0.5s ease-out;
  max-height: 40px;
  border-radius: 8px;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Compteur de temps circulaire */
.time-remaining {
  display: flex;
  align-items: center;
  justify-content: center;
}

.countdown-circle {
  position: relative;
  width: 50px;
  height: 50px;

}

.countdown-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.countdown-bg {
  fill: none;
  stroke: var(--background-light);
  stroke-width: 3;
}

.countdown-progress {
  fill: none;
  stroke: var(--warning-color);
  stroke-width: 3;
  stroke-linecap: round;
  transition: stroke-dasharray 1s linear;
}

.countdown-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Boutons d'export */
.export-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  border: 2px solid var(--border-color);
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  font-size: 14px;
  font-weight: 500;
}

.export-btn:disabled {
  opacity: 0.5;
  cursor: auto;
}

.pairing-btn:disabled {
  opacity: 0.5;
  cursor: auto;
}


.export-btn:hover {
  background: linear-gradient(45deg, #81c784, var(--primary)) !important;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.export-btn.primary {
  background: var(--primary);
  border-color: var(--success-color);
  color: white;
}

.export-btn.primary:hover {
  background: var(--primary);

}

/* Corps de l'éditeur */
.editor-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Palette d'outils améliorée */
.tools-palette {
  width: 250px;
  background: var(--background-white);
  border-left: 1px solid var(--border-color);
  overflow-y: auto;
  padding: 1.5rem;
}

.palette-section {
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1.5rem;


}

.palette-section-right {
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1.5rem;
  padding-top: 1rem;

}

.palette-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0 0 1.25rem 0;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-primary);
}

.capteur-count {
  background: var(--primary-color);
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 999px;
  font-weight: 600;
}

/* Section capteurs avec drag & drop */
.capteurs-container {
  position: relative;
}

.tool-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Cartes de capteurs améliorées */
.capteur-item {
  transition: var(--transition);
  cursor: grab;
}

.capteur-item:active {
  cursor: grabbing;
}

.capteur-item.dragging {
  opacity: 0.7;
  transform: scale(1.05) rotate(3deg);
  z-index: 1000;
}

.capteur-card {
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--background-white);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.capteur-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--success-color));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.capteur-item:hover .capteur-card {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.capteur-item:hover .capteur-card::before {
  transform: scaleX(1);
}

.capteur-item.selected .capteur-card {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.05);
  box-shadow: var(--shadow-lg);
}

.capteur-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.capteur-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: rgba(59, 130, 246, 0.1);
}

.capteur-icon {
  font-size: 24px !important;
}

/* Badge de statut avec animations */
.status-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--secondary-color);
}

.status-badge.online .status-dot {
  background: var(--success-color);
  animation: pulseOnline 2s infinite;
}

.status-badge.warning .status-dot {
  background: var(--warning-color);
  animation: pulseWarning 1s infinite;
}

.status-badge.offline .status-dot {
  background: var(--secondary-color);
}

@keyframes pulseOnline {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

@keyframes pulseWarning {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.capteur-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  padding: 0.01rem;

  background: var(--background-white);
  border-radius: 6px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: var(--background-light);
  border-color: var(--primary-color);
  transform: scale(1.1);
}

.action-btn.info:hover {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
}

.action-btn.highlight:hover {
  background: rgba(251, 191, 36, 0.1);
  color: #F59E0B;
}

.action-btn.remove:hover {
  background: rgba(220, 38, 38, 0.1);
  color: var(--danger-color);
}

.capteur-info {
  margin-bottom: 0.75rem;
}

.capteur-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.capteur-type {
  font-size: 0.875rem;
  color: var(--primary-color);
  font-weight: 500;
  margin: 0 0 0.25rem 0;
}

.capteur-model {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin: 0 0 0.5rem 0;
}

.capteur-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.last-seen {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Indicateur de drag */
.drag-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: var(--background-light);
  border-radius: 6px;
  font-size: 0.8rem;
  color: var(--text-secondary);
  opacity: 0;
  transition: var(--transition);
  border: 1px dashed var(--border-color);
}

.capteur-item:hover .drag-indicator {
  opacity: 1;
}

.capteur-item.dragging .drag-indicator {
  opacity: 0;
}

/* Nouveaux capteurs */
.new-capteurs-container {
  position: relative;
}

.new-capteur-item {
  cursor: grab;
  transition: var(--transition);
}

.new-capteur-item:active {
  cursor: grabbing;
}

.new-capteur-card {
  padding: 1rem;
  border: 2px dashed var(--success-color);
  border-radius: var(--border-radius);
  background: linear-gradient(135deg, rgba(5, 150, 105, 0.05), rgba(5, 150, 105, 0.1));
  transition: var(--transition);
  position: relative;
  text-align: center;
  overflow: hidden;
}

.new-capteur-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--success-color), var(--primary-color), var(--success-color));
  background-size: 200% 200%;
  border-radius: var(--border-radius);
  z-index: -1;
  animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {

  0%,
  100% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }
}

.new-capteur-item:hover .new-capteur-card {
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-lg);
  border-style: solid;
}

.new-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--success-color);
  color: white;
  font-size: 0.6rem;
  padding: 0.25rem 0.5rem;
  border-radius: 999px;
  font-weight: 700;
  letter-spacing: 0.5px;
  animation: newBadgePulse 2s infinite;
}

@keyframes newBadgePulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }
}

/* Animation de slide-in pour les nouveaux capteurs */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in {
  animation: slideInLeft 0.5s ease-out forwards;
}

/* États vides */
.empty-state {
  text-align: center;
  padding: 2rem 1rem;
  color: var(--text-secondary);
}

.empty-state mat-icon {
  font-size: 3rem !important;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state p {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

.searching-state {
  text-align: center;
  padding: 2rem 1rem;
  color: var(--text-secondary);
}

.searching-animation {
  position: relative;
  display: inline-block;
  margin-bottom: 1rem;
}

.searching-icon {
  font-size: 2.5rem !important;
  color: var(--primary-color);
  animation: searchPulse 2s infinite;
}

@keyframes searchPulse {

  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.searching-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.search-wave {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  opacity: 0;
  animation: searchWave 2s infinite;
}

.search-wave:nth-child(1) {
  animation-delay: 0s;
}

.search-wave:nth-child(2) {
  animation-delay: 0.7s;
}

.search-wave:nth-child(3) {
  animation-delay: 1.4s;
}

@keyframes searchWave {
  0% {
    width: 20px;
    height: 20px;
    opacity: 0.8;
  }

  100% {
    width: 80px;
    height: 80px;
    opacity: 0;
  }
}



/* Flash animation pour nouveau capteur détecté */
.new-device-flash {
  position: absolute;
  top: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--success-color);
  opacity: 0;
}

.new-device-flash.flash-animation {
  animation: flashNotification 1s ease-out;
}

@keyframes flashNotification {
  0% {
    opacity: 0;
    transform: scale(0);
  }

  50% {
    opacity: 1;
    transform: scale(3);
  }

  100% {
    opacity: 0;
    transform: scale(1);
  }
}

/* Capteurs installés */
.installed-capteurs {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.installed-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background: rgba(5, 150, 105, 0.05);
  border: 1px solid rgba(5, 150, 105, 0.2);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.installed-item:hover {
  background: rgba(5, 150, 105, 0.1);
  transform: translateX(4px);
}

.installed-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.installed-info .name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.installed-actions {
  display: flex;
  gap: 0.001;
}

/* Zone de canvas améliorée */
.canvas-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--background-light);
  max-height: 500px;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: var(--background-white);
  border-bottom: 1px solid var(--border-color);
}

.canvas-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.canvas-title {
  font-weight: 700;
  color: var(--text-primary);
  font-size: 0.70rem;
}

.plan-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.plan-details {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 0.70rem;
}

.plan-meta {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.canvas-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  max-height: 500px;
  overflow: hidden;
  transition: var(--transition);
}

.canvas-wrapper.drag-over {
  background: rgba(59, 130, 246, 0.05);
}

/* Indicateur de zone de drop */
.drop-zone-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(59, 130, 246, 0.1);
  border: 3px dashed var(--primary-color);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  animation: dropZonePulse 1s infinite;
}

@keyframes dropZonePulse {

  0%,
  100% {
    opacity: 0.8;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

.drop-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: var(--background-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  color: var(--primary-color);
  font-weight: 600;
  text-align: center;
}

.drop-zone-content mat-icon {
  font-size: 4rem !important;
  animation: bounce 1s infinite;
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-10px);
  }

  60% {
    transform: translateY(-5px);
  }
}

#canvas {
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  background: white;
  max-width: 100%;
  max-height: 100%;
  transition: var(--transition);
}

#canvas:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Overlay d'information */
.canvas-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--background-white);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  text-align: center;
  max-width: 400px;
  border: 2px solid var(--border-color);
}

.overlay-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.overlay-content mat-icon {
  font-size: 3rem !important;
  color: var(--primary);
}

.overlay-content h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 700;
  color: var(--text-primary);
}

.overlay-content ol {
  text-align: left;
  margin: 0;
  padding-left: 1rem;
  color: var(--text-secondary);
  line-height: 1;
}

.overlay-content li {
  margin-bottom: 0.5rem;
}

/* Popup de détails du capteur */
.capteur-details-popup {
  position: fixed;
  background-color: #ffffff;
  border: 2px solid var(--primary-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: 1001;
  min-width: 320px;
  max-width: 400px;
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background: rgba(59, 130, 246, 0.05);
}

.capteur-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.capteur-title h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.capteur-title mat-icon {
  font-size: 1.5rem !important;
}

.close-btn {
  padding: 0.5rem;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: 4px;
  transition: var(--transition);
}

.close-btn:hover {
  background: var(--background-light);
  color: var(--text-primary);
}

.popup-content {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 1.5rem;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.detail-grid {
  display: grid;
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--background-light);
  border-radius: 6px;
}

.detail-item .label {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.detail-item .value {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  text-align: right;
}

.detail-item .value.status {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.detail-item .value.status.online {
  color: var(--success-color);
}

.detail-item .value.status.offline {
  color: var(--danger-color);
}

.detail-item .value.mono {
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  background: var(--background-white);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.sensor-readings {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.reading-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-light);
  border-radius: 6px;
}

.reading-value {
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
}

.reading-time {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.detail-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.detail-actions .action-btn {
  flex: 1;
  justify-content: center;
  padding: 0.75rem 1rem;
  font-weight: 600;
  font-size: 0.875rem;
}

.detail-actions .action-btn.primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.detail-actions .action-btn.primary:hover {
  background: #2563EB;
  border-color: #2563EB;
}

.detail-actions .action-btn.secondary {
  color: var(--danger-color);
  border-color: rgba(220, 38, 38, 0.3);
}

.detail-actions .action-btn.secondary:hover {
  background: rgba(220, 38, 38, 0.1);
  border-color: var(--danger-color);
}

/* Overlay backdrop */
.overlay-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/* Notifications de succès */
.success-notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: var(--success-color);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: 2000;
  font-weight: 600;
  transform: translateX(100%);
  transition: transform 0.3s ease-out;
}

.success-notification.show {
  transform: translateX(0);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .context-selectors {
    grid-template-columns: repeat(2, 1fr);
  }

  .tools-palette {
    width: 280px;
  }
}

@media (max-width: 1024px) {
  .editor-body {
    flex-direction: column;
    overflow: hidden;
  }

  .tools-palette {
    width: 100%;
    max-height: 300px;
    overflow-x: auto;
    display: flex;
    gap: 1rem;
    padding: 1rem;
  }

  .palette-section {
    min-width: 250px;
    margin-bottom: 0;
    border-bottom: none;
    border-right: 1px solid var(--border-color);
    padding-right: 1rem;
    padding-bottom: 0;
  }

  .palette-section:last-child {
    border-right: none;
    padding-right: 0;
  }

  .canvas-wrapper {
    padding: 1rem;
  }

  #canvas {
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 500px);
  }
}

@media (max-width: 768px) {
  .editor-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-actions {
    align-items: stretch;
    min-width: auto;
  }

  .context-selectors {
    grid-template-columns: 1fr;
  }

  .pairing-container {
    align-items: stretch;
  }

  .tools-palette {
    flex-direction: column;
    max-height: 400px;
  }

  .palette-section {
    min-width: auto;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    padding-right: 0;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
  }

  .capteur-details-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: calc(100vw - 2rem);
    max-width: 400px;
  }
}

@media (max-width: 480px) {
  .editor-header {
    padding: 1rem;
  }

  .editor-title {
    font-size: 1.5rem;
  }

  .canvas-wrapper {
    padding: 0.5rem;
  }

  #canvas {
    max-width: calc(100vw - 1rem);
    max-height: calc(100vh - 450px);
  }

  .capteur-card {
    padding: 0.75rem;
  }

  .detail-actions {
    flex-direction: column;
  }

  .success-notification {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    transform: translateY(-100%);
  }

  .success-notification.show {
    transform: translateY(0);
  }
}

/* Animations générales */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus styles pour l'accessibilité */
button:focus-visible,
.dropdown-trigger:focus-visible,
.search-input:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Scrollbar personnalisée */
.dropdown-options::-webkit-scrollbar,
.popup-content::-webkit-scrollbar,
.tools-palette::-webkit-scrollbar {
  width: 6px;
}

.dropdown-options::-webkit-scrollbar-track,
.popup-content::-webkit-scrollbar-track,
.tools-palette::-webkit-scrollbar-track {
  background: var(--background-light);
}

.dropdown-options::-webkit-scrollbar-thumb,
.popup-content::-webkit-scrollbar-thumb,
.tools-palette::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.dropdown-options::-webkit-scrollbar-thumb:hover,
.popup-content::-webkit-scrollbar-thumb:hover,
.tools-palette::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}


/* Styles pour la popup de résultat d'installation */

/* Overlay */
.installation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

/* Popup principale */
.installation-result-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  z-index: 2001;
  min-width: 500px;
  max-width: 700px;
  max-height: 120vh;
  overflow: hidden;
  animation: slideInScale 0.4s ease-out;
}



@keyframes slideInScale {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }

  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Header */
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--background-light), var(--background-white));
  border-bottom: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.popup-header.success {
  background: linear-gradient(135deg, rgba(5, 150, 105, 0.1), rgba(34, 197, 94, 0.05));
  border-bottom-color: rgba(5, 150, 105, 0.2);
}

.popup-header.error {
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.1), rgba(239, 68, 68, 0.05));
  border-bottom-color: rgba(220, 38, 38, 0.2);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-icon {
  font-size: 2.5rem !important;
  width: 2.5rem !important;
  height: 2.5rem !important;
  animation: statusIconPulse 2s infinite;
}

@keyframes statusIconPulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

.status-text h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.timestamp {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.close-btn {
  padding: 0.5rem;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: 6px;
  transition: var(--transition);
}

.close-btn:hover {
  background: var(--background-light);
  color: var(--text-primary);
}

/* Contenu principal */
.popup-content {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

/* Résumé des statistiques */
.installation-summary {
  margin-bottom: 1.5rem;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.summary-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: var(--border-radius);
  border: 2px solid var(--border-color);
  background: var(--background-white);
  transition: var(--transition);
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.summary-card.total {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.05);
}

.summary-card.success {
  border-color: var(--success-color);
  background: rgba(5, 150, 105, 0.05);
}

.summary-card.error {
  border-color: var(--danger-color);
  background: rgba(220, 38, 38, 0.05);
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--background-light);
}

.summary-card.total .card-icon {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
}

.summary-card.success .card-icon {
  background: rgba(5, 150, 105, 0.1);
  color: var(--success-color);
}

.summary-card.error .card-icon {
  background: rgba(220, 38, 38, 0.1);
  color: var(--danger-color);
}

.card-content {
  display: flex;
  flex-direction: column;
}

.card-number {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.card-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

/* Barre de progression */
.progress-section {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--background-light);
  border-radius: var(--border-radius);
}

.progress-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--text-primary);
}

.progress-percentage {
  font-size: 1.125rem;
  font-weight: 700;
}

.progress-bar-container {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: var(--background-white);
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.progress-fill {
  height: 100%;
  border-radius: 6px;
  transition: width 1s ease-out;
  position: relative;
  overflow: hidden;
}

.progress-fill.success {
  background: linear-gradient(90deg, var(--success-color), #10b981);
}

.progress-fill.error {
  background: linear-gradient(90deg, var(--danger-color), #f87171);
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

/* Détails de l'installation */
.installation-details h3,
.installation-details h4 {
  margin: 0 0 1rem 0;
  font-weight: 700;
  color: var(--text-primary);
}

.installation-details h3 {
  font-size: 1.25rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--border-color);
  margin-bottom: 1.5rem;
}

.installation-details h4 {
  font-size: 1rem;
  margin-top: 1.5rem;
}

/* Informations du contexte */
.context-info {
  display: grid;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.info-item mat-icon {
  color: var(--primary-color);
  font-size: 1.25rem !important;
}

.info-item .label {
  font-weight: 600;
  color: var(--text-secondary);
  min-width: 80px;
}

.info-item .value {
  font-weight: 500;
  color: var(--text-primary);
  flex: 1;
}

/* Liste des capteurs */
.capteur-items {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-height: 200px;
  overflow-y: auto;
}

.capteur-item-result {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--background-white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.capteur-item-result:hover {
  background: var(--background-light);
}

.capteur-info-confirmation {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.capteur-icon-small {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: var(--background-light);
}

.capteur-details {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.capteur-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.capteur-type {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.installation-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.installation-status .status-icon {
  font-size: 1.25rem !important;
}

.installation-status .status-icon.success {
  color: var(--success-color);
}

.installation-status .status-icon.error {
  color: var(--danger-color);
}

.installation-status .status-text {
  font-weight: 600;
  font-size: 0.875rem;
}

/* Messages d'erreur */
.error-details {
  margin-top: 1.5rem;
}

.error-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.error-item {
  display: flex;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(220, 38, 38, 0.05);
  border: 1px solid rgba(220, 38, 38, 0.2);
  border-radius: var(--border-radius);
}

.error-item mat-icon {
  color: var(--danger-color);
  font-size: 1.25rem !important;
  flex-shrink: 0;
}

.error-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.error-title {
  font-weight: 600;
  color: var(--danger-color);
  font-size: 0.875rem;
}

.error-message {
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Message de succès */
.success-message {
  margin-top: 1.5rem;
}

.success-content {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(5, 150, 105, 0.05);
  border: 1px solid rgba(5, 150, 105, 0.2);
  border-radius: var(--border-radius);
}

.success-content mat-icon {
  color: var(--success-color);
  font-size: 2rem !important;
  flex-shrink: 0;
}

.message-text h4 {
  margin: 0 0 0.5rem 0;
  color: var(--success-color);
  font-weight: 700;
}

.message-text p {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.5;
  font-size: 0.875rem;
}

/* Actions de la popup */
.popup-actions {
  padding: 1.5rem;
  background: var(--background-light);
  border-top: 1px solid var(--border-color);
  max-height: 30px;

}

.actions-success,
.actions-error {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  background: var(--background-white);
  font-size: 0.875rem;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.action-btn.primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.action-btn.primary:hover {
  background: #2563EB;
  border-color: #2563EB;
}

.action-btn.secondary {
  color: var(--text-secondary);
  border-radius: 10px;
}

.action-btn.secondary:hover {
  background: var(--background-light);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.action-btn.warning {
  background: var(--warning-color);
  border-color: var(--warning-color);
  color: white;
}

.action-btn.warning:hover {
  background: #ea580c;
  border-color: #ea580c;
}

.action-btn.danger {
  color: var(--danger-color);
  border-color: rgba(220, 38, 38, 0.3);
}

.action-btn.danger:hover {
  background: rgba(220, 38, 38, 0.1);
  border-color: var(--danger-color);
}

/* Responsive */
@media (max-width: 768px) {
  .installation-result-popup {
    min-width: auto;
    width: calc(100vw - 2rem);
    max-width: none;
    margin: 1rem;
  }

  .summary-cards {
    grid-template-columns: 1fr;
  }

  .actions-success,
  .actions-error {
    flex-direction: column;
  }

  .action-btn {
    justify-content: center;
  }

  .popup-header {
    padding: 1rem;
  }

  .popup-content {
    padding: 1rem;
  }

  .popup-actions {
    padding: 1rem;
  }
}

/* Scrollbar pour le contenu */
.popup-content::-webkit-scrollbar,
.capteur-items::-webkit-scrollbar {
  width: 6px;
}

.popup-content::-webkit-scrollbar-track,
.capteur-items::-webkit-scrollbar-track {
  background: var(--background-light);
}

.popup-content::-webkit-scrollbar-thumb,
.capteur-items::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.popup-content::-webkit-scrollbar-thumb:hover,
.capteur-items::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* Animations supplémentaires */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/* Animation pour les cartes de résumé */
.summary-card {
  animation: slideInUp 0.5s ease-out forwards;
}

.summary-card:nth-child(1) {
  animation-delay: 0.1s;
}

.summary-card:nth-child(2) {
  animation-delay: 0.2s;
}

.summary-card:nth-child(3) {
  animation-delay: 0.3s;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation pour les éléments de liste */
.capteur-item-result {
  animation: fadeInSlide 0.3s ease-out forwards;
}

.capteur-item-result:nth-child(1) {
  animation-delay: 0.1s;
}

.capteur-item-result:nth-child(2) {
  animation-delay: 0.15s;
}

.capteur-item-result:nth-child(3) {
  animation-delay: 0.2s;
}

.capteur-item-result:nth-child(4) {
  animation-delay: 0.25s;
}

.capteur-item-result:nth-child(5) {
  animation-delay: 0.3s;
}

@keyframes fadeInSlide {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* États de chargement */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  text-align: center;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--background-light);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Effet de brillance pour les boutons de succès */
.action-btn.primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.action-btn.primary:hover::before {
  left: 100%;
}

/* Style pour les badges de statut dans les listes */
.status-badge-small {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 999px;
  font-size: 0.75rem;
  font-weight: 600;
}

.status-badge-small.success {
  background: rgba(5, 150, 105, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(5, 150, 105, 0.3);
}

.status-badge-small.error {
  background: rgba(220, 38, 38, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(220, 38, 38, 0.3);
}

/* Amélioration visuelle pour les sections */
.installation-details>div {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.installation-details>div:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

/* Effet de hover pour les éléments interactifs */
.info-item:hover,
.capteur-item-result:hover {
  background: var(--background-light);
  transform: translateX(2px);
}

/* Animation de succès globale */
.popup-header.success::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--success-color), #10b981, var(--success-color));
  background-size: 200% 100%;
  animation: successGlow 3s ease-in-out infinite;
}

@keyframes successGlow {

  0%,
  100% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }
}

/* Animation d'erreur globale */
.popup-header.error::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--danger-color);
  animation: errorPulse 1s ease-in-out infinite;
}

@keyframes errorPulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

/* Focus styles pour l'accessibilité */
.action-btn:focus-visible,
.close-btn:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Transition douce pour les changements d'état */
* {
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
}


/* Animation container pour l'appairage */
.pairing-animation-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  padding-top: 80px;



  min-height: 200px;
  overflow: hidden;
}

/* Hub central avec animation */
.pairing-hub {
  position: relative;
  width: 50px;
  height: 50px;
  background: linear-gradient(45deg, var(--primary), #26976e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  animation: hubPulse 2s ease-in-out infinite;
}

.pairing-hub mat-icon {
  font-size: 2.5rem !important;
  color: white;
}

/* Ondes radio qui émanent du hub */
.pairing-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-20%, -20%);
  pointer-events: none;
}

.radio-wave {
  position: absolute;
  border: 3px solid rgba(0, 247, 53, 0.4);
  border-radius: 50%;
  animation: radioWaveExpand 3s ease-out infinite;
}

.radio-wave:nth-child(1) {
  animation-delay: 0s;
}

.radio-wave:nth-child(2) {
  animation-delay: 1s;
}

.radio-wave:nth-child(3) {
  animation-delay: 2s;
}

/* Indicateurs de capteurs en orbite */
.sensor-indicators {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 180px;
  height: 180px;
}

.sensor-indicator {
  position: absolute;
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, #10B981, #059669);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
  opacity: 0;
  animation: sensorOrbit 8s linear infinite;
}

.sensor-indicator:nth-child(1) {
  animation-delay: 0s;
  animation-name: sensorOrbit1;
}

.sensor-indicator:nth-child(2) {
  animation-delay: 2s;
  animation-name: sensorOrbit2;
}

.sensor-indicator:nth-child(3) {
  animation-delay: 4s;
  animation-name: sensorOrbit3;
}

.sensor-indicator:nth-child(4) {
  animation-delay: 6s;
  animation-name: sensorOrbit4;
}

/* Particules flottantes */
.pairing-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, var(--primary), var(--primary));
  border-radius: 50%;
  animation: particleFloat 4s ease-in-out infinite;
}

/* Status text animé */
.pairing-status-text {
  text-align: center;
  margin-top: 1rem;
}

.pairing-message {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  animation: textFade 2s ease-in-out infinite;
}

.pairing-progress-dots {
  display: inline-flex;
  gap: 4px;
}

.progress-dot {
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
  animation: dotPulse 1.5s ease-in-out infinite;
}

.progress-dot:nth-child(1) {
  animation-delay: 0s;
}

.progress-dot:nth-child(2) {
  animation-delay: 0.3s;
}

.progress-dot:nth-child(3) {
  animation-delay: 0.6s;
}

/* Compteur de capteurs trouvés */
.sensors-found-counter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(16, 185, 129, 0.1);
  border: 2px solid rgba(16, 185, 129, 0.3);
  border-radius: 8px;
  color: #059669;
  font-weight: 600;
  animation: counterAppear 0.5s ease-out;
}

.counter-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #047857;
  animation: numberGlow 1s ease-in-out infinite;
}

/* ==========================================
// 2. Keyframes CSS
// ========================================== */

@keyframes hubPulse {

  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  }

  50% {
    transform: scale(1.1);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.5);
  }
}

@keyframes radioWaveExpand {
  0% {
    width: 80px;
    height: 80px;
    opacity: 0.8;
    transform: translate(-50%, -50%);
  }

  100% {
    width: 300px;
    height: 300px;
    opacity: 0;
    transform: translate(-50%, -50%);
  }
}

@keyframes sensorOrbit1 {
  0% {
    top: -20px;
    left: 70px;
    opacity: 0;
    transform: scale(0.5);
  }

  25% {
    top: 70px;
    left: 160px;
    opacity: 1;
    transform: scale(1);
  }

  50% {
    top: 160px;
    left: 70px;
    opacity: 1;
    transform: scale(1);
  }

  75% {
    top: 70px;
    left: -20px;
    opacity: 1;
    transform: scale(1);
  }

  100% {
    top: -20px;
    left: 70px;
    opacity: 0;
    transform: scale(0.5);
  }
}

@keyframes sensorOrbit2 {
  0% {
    top: 70px;
    left: 160px;
    opacity: 0;
    transform: scale(0.5);
  }

  25% {
    top: 160px;
    left: 70px;
    opacity: 1;
    transform: scale(1);
  }

  50% {
    top: 70px;
    left: -20px;
    opacity: 1;
    transform: scale(1);
  }

  75% {
    top: -20px;
    left: 70px;
    opacity: 1;
    transform: scale(1);
  }

  100% {
    top: 70px;
    left: 160px;
    opacity: 0;
    transform: scale(0.5);
  }
}

@keyframes sensorOrbit3 {
  0% {
    top: 160px;
    left: 70px;
    opacity: 0;
    transform: scale(0.5);
  }

  25% {
    top: 70px;
    left: -20px;
    opacity: 1;
    transform: scale(1);
  }

  50% {
    top: -20px;
    left: 70px;
    opacity: 1;
    transform: scale(1);
  }

  75% {
    top: 70px;
    left: 160px;
    opacity: 1;
    transform: scale(1);
  }

  100% {
    top: 160px;
    left: 70px;
    opacity: 0;
    transform: scale(0.5);
  }
}

@keyframes sensorOrbit4 {
  0% {
    top: 70px;
    left: -20px;
    opacity: 0;
    transform: scale(0.5);
  }

  25% {
    top: -20px;
    left: 70px;
    opacity: 1;
    transform: scale(1);
  }

  50% {
    top: 70px;
    left: 160px;
    opacity: 1;
    transform: scale(1);
  }

  75% {
    top: 160px;
    left: 70px;
    opacity: 1;
    transform: scale(1);
  }

  100% {
    top: 70px;
    left: -20px;
    opacity: 0;
    transform: scale(0.5);
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(100px) translateX(0px) scale(0);
    opacity: 0;
  }

  10% {
    opacity: 1;
    transform: translateY(80px) translateX(10px) scale(1);
  }

  90% {
    opacity: 1;
    transform: translateY(-80px) translateX(-10px) scale(1);
  }

  100% {
    transform: translateY(-100px) translateX(0px) scale(0);
    opacity: 0;
  }
}

@keyframes textFade {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

@keyframes dotPulse {

  0%,
  100% {
    transform: scale(1);
    opacity: 0.5;
  }

  50% {
    transform: scale(1.5);
    opacity: 1;
  }
}

@keyframes counterAppear {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes numberGlow {

  0%,
  100% {
    text-shadow: 0 0 5px rgba(4, 120, 87, 0.5);
  }

  50% {
    text-shadow: 0 0 20px rgba(4, 120, 87, 0.8);
  }
}




/* Styles pour la popup de confirmation */

/* Overlay de confirmation */
.confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1500;
  animation: fadeIn 0.3s ease-out;
}

/* Popup de confirmation principale */
.confirmation-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--background-white);
  border-radius: 16px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  z-index: 1501;
  min-width: 600px;
  max-width: 800px;
  max-height: 85vh;
  overflow: hidden;
  animation: slideInScale 0.4s ease-out;
}

/* Header de confirmation */
.confirmation-header {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 2rem;
  background-color:#F9FAFB;
  border-bottom: 1px solid rgba(251, 191, 36, 0.2);
  position: relative;
}

.confirmation-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--warning-color), #fbbf24, var(--warning-color));
  background-size: 200% 100%;
  animation: warningGlow 3s ease-in-out infinite;
}

@keyframes warningGlow {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: rgba(251, 191, 36, 0.2);
  flex-shrink: 0;
}

.warning-icon {
  font-size: 1.5rem !important;
  color: var(--warning-color);
  animation: warningPulse 2s infinite;
}

@keyframes warningPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.header-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
}

.header-content p {
  margin: 0;
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Contenu de confirmation */
.confirmation-content {
  padding: 1.5rem 2rem;
  max-height: 50vh;
  overflow-y: auto;
  background-color: #ffffff;
}

.installation-preview h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
}

/* Aperçu du contexte */
.context-preview {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--background-light);
  border-radius: var(--border-radius);
}

.context-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.context-item mat-icon {
  color: var(--primary-color);
  font-size: 1.25rem !important;
}

.context-details {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.context-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.context-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Statistiques d'installation */
.installation-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-white);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.stat-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary);
}

/* Résumé des types de capteurs */
.capteur-types-summary {
  margin-bottom: 1.5rem;
}

.capteur-types-summary h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.type-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.type-tag {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 999px;
  font-size: 0.8rem;
  font-weight: 500;
}

.type-tag mat-icon {
  font-size: 1rem !important;
}

/* Liste détaillée des capteurs */
.capteurs-preview {
  margin-bottom: 1.5rem;
}

.capteurs-preview h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.capteur-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 200px;
  overflow-y: auto;
  padding: 0.5rem;
  background: var(--background-light);
  border-radius: var(--border-radius);
}

.capteur-preview-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background: var(--background-white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.capteur-preview-item:hover {
  background: var(--background-light);
  transform: translateX(4px);
}

.capteur-preview-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: var(--background-light);
}

.capteur-preview-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  flex: 1;
  margin-left: 0.75rem;
}

.capteur-preview-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.capteur-preview-type {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.capteur-preview-status {
  display: flex;
  align-items: center;
}

.status-ready {
  padding: 0.25rem 0.75rem;
  background: rgba(5, 150, 105, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(5, 150, 105, 0.3);
  border-radius: 999px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Avertissements */
.installation-warnings {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: var(--border-radius);
}

.warning-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.warning-item.important {
  color: var(--warning-color);
  font-weight: 600;
}

.warning-item mat-icon {
  color: var(--primary-color);
  font-size: 0.5rem !important;
}

.warning-item.important mat-icon {
  color: var(--warning-color);
}

/* Actions de confirmation */
.confirmation-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  background-color: #f9fafb;
  border-top: 1px solid var(--border-color);
}

.confirmation-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  background: var(--background-white);
  font-size: 0.875rem;
  position: relative;
  overflow: hidden;
  
}

.confirmation-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.confirmation-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.confirmation-btn.cancel {
  color: var(--text-secondary);
  background-color: #f9fafb;
  border-radius: 10px;
}

.confirmation-btn.cancel:hover {
  background: var(--background-light);
  border-color: var(--danger-color);
  color: var(--danger-color);
}

.confirmation-btn.confirm {
 background-color: var(--primary);
  border-color: var(--success-color);
  color: white;
}

.confirmation-btn.confirm:hover {
  background: #047857;
  border-color: #047857;
}

.confirmation-btn.confirm::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.confirmation-btn.confirm:hover::before {
  left: 100%;
}

/* Popup de traitement */
.processing-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 1600;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.processing-popup {
  background: var(--background-white);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  min-width: 400px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  animation: slideInScale 0.4s ease-out;
}

.processing-animation {
  margin-bottom: 1.5rem;
}

.spinner-container {
  position: relative;
  display: inline-block;
  width: 80px;
  height: 80px;
}

.installation-spinner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 4px solid var(--background-light);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem !important;
  color: var(--primary-color);
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.processing-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.processing-content p {
  margin: 0 0 1.5rem 0;
  color: var(--text-secondary);
}

/* Étapes de progression */
.processing-progress {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  text-align: left;
}

.progress-step {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--background-light);
  border-radius: var(--border-radius);
  transition: var(--transition);
  opacity: 0.5;
}

.progress-step.active {
  opacity: 1;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  transform: translateX(4px);
}

.progress-step mat-icon {
  color: var(--text-secondary);
  font-size: 1.25rem !important;
}

.progress-step.active mat-icon {
  color: var(--primary-color);
  animation: stepPulse 1s infinite;
}

@keyframes stepPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.progress-step span {
  font-weight: 500;
  color: var(--text-secondary);
}

.progress-step.active span {
  color: var(--text-primary);
  font-weight: 600;
}

/* Notifications d'erreur */
.error-notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: var(--danger-color);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: 2500;
  transform: translateX(100%);
  transition: transform 0.3s ease-out;
  max-width: 400px;
}

.error-notification.show {
  transform: translateX(0);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.notification-content mat-icon {
  font-size: 1.5rem !important;
}

/* Responsive pour la popup de confirmation */
@media (max-width: 768px) {
  .confirmation-popup {
    min-width: auto;
    width: calc(100vw - 2rem);
    max-width: none;
    margin: 1rem;
    max-height: 90vh;
  }
  
  .confirmation-header {
    padding: 1.5rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .header-content h2 {
    font-size: 1.5rem;
  }
  
  .confirmation-content {
    padding: 1rem 1.5rem;
  }
  
  .context-preview {
    grid-template-columns: 1fr;
  }
  
  .installation-stats {
    grid-template-columns: 1fr;
  }
  
  .confirmation-actions {
    flex-direction: column;
    padding: 1rem 1.5rem;
  }
  
  .confirmation-btn {
    justify-content: center;
  }
  
  .processing-popup {
    min-width: auto;
    width: calc(100vw - 2rem);
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .error-notification {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    transform: translateY(-100%);
    max-width: none;
  }
  
  .error-notification.show {
    transform: translateY(0);
  }
}

@media (max-width: 480px) {
  .confirmation-header {
    padding: 1rem;
  }
  
  .header-icon {
    width: 48px;
    height: 48px;
  }
  
  .warning-icon {
    font-size: 1.5rem !important;
  }
  
  .header-content h2 {
    font-size: 1.25rem;
  }
  
  .confirmation-content {
    padding: 1rem;
  }
  
  .confirmation-actions {
    padding: 1rem;
  }
  
  .type-tags {
    justify-content: center;
  }
  
  .capteur-list {
    max-height: 150px;
  }
  
  .processing-popup {
    padding: 1rem;
  }
  
  .spinner-container {
    width: 60px;
    height: 60px;
  }
  
  .spinner-icon {
    font-size: 1.5rem !important;
  }
  
  .processing-content h3 {
    font-size: 1.25rem;
  }
}

/* Animations supplémentaires */
.capteur-preview-item {
  animation: fadeInSlideUp 0.3s ease-out forwards;
}

.capteur-preview-item:nth-child(1) { animation-delay: 0.1s; }
.capteur-preview-item:nth-child(2) { animation-delay: 0.15s; }
.capteur-preview-item:nth-child(3) { animation-delay: 0.2s; }
.capteur-preview-item:nth-child(4) { animation-delay: 0.25s; }
.capteur-preview-item:nth-child(5) { animation-delay: 0.3s; }

@keyframes fadeInSlideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card {
  animation: slideInUp 0.4s ease-out forwards;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }

/* Effet de hover global */
.confirmation-popup * {
  transition: all 0.2s ease;
}

/* Scrollbar personnalisée pour le contenu */
.confirmation-content::-webkit-scrollbar,
.capteur-list::-webkit-scrollbar {
  width: 6px;
}

.confirmation-content::-webkit-scrollbar-track,
.capteur-list::-webkit-scrollbar-track {
  background: var(--background-light);
}

.confirmation-content::-webkit-scrollbar-thumb,
.capteur-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.confirmation-content::-webkit-scrollbar-thumb:hover,
.capteur-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* États de focus pour l'accessibilité */
.confirmation-btn:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Animation de pulsation pour les éléments importants */
.warning-item.important {
  animation: importantPulse 3s infinite;
}

@keyframes importantPulse {
  0%, 100% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(251, 191, 36, 0.1);
  }
}

/* Effet de brillance sur les boutons de confirmation */
.confirmation-btn.confirm:not(:disabled):hover {
  box-shadow: 0 0 20px rgba(5, 150, 105, 0.3);
}

/* Animation d'apparition progressive pour les sections */
.installation-preview > * {
  animation: fadeInUp 0.5s ease-out forwards;
}

.installation-preview > *:nth-child(1) { animation-delay: 0.1s; }
.installation-preview > *:nth-child(2) { animation-delay: 0.2s; }
.installation-preview > *:nth-child(3) { animation-delay: 0.3s; }
.installation-preview > *:nth-child(4) { animation-delay: 0.4s; }
.installation-preview > *:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}