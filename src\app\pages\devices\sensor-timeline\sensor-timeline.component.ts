import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  ViewChild
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIcon } from '@angular/material/icon';
import { CustomLog } from '@app/core/models/log';

@Component({
  selector: 'app-sensor-timeline',
  standalone: true,
  imports: [CommonModule, MatIcon],
  templateUrl: './sensor-timeline.component.html',
  styleUrls: ['./sensor-timeline.component.css']
})
export class SensorTimelineComponent implements AfterViewInit, OnChanges {
  @Output() closed = new EventEmitter<void>();
  @ViewChild('chatBody') chatBody!: ElementRef;
  @Input() name : string = "";
  @Input() timelineData: CustomLog[] = [];
  
  displayData: Array<{
    start: string;
    end: string;
    status: string;
    log: string;
    sensorData?: any;
  }> = [];

  ngAfterViewInit() {
    this.scrollToBottom();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['timelineData']) {
      this.transformTimelineData();
    }
  }

  closeTimeline(): void {
    this.closed.emit();
  }

  private transformTimelineData(): void {
    this.displayData = (this.timelineData || []).map((entry: any) => ({
    start: entry.StatusStart
      ? `${new Date(entry.StatusStart).toLocaleDateString('fr-FR')} ${new Date(entry.StatusStart).toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        })}`
      : '??:??',

    end: entry.StatusEnd
      ? `${new Date(entry.StatusEnd).toLocaleDateString('fr-FR')} ${new Date(entry.StatusEnd).toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        })}`
      : '??:??',


      status: entry.Status.toLowerCase(),
      log: entry.Message ? this.parseMessage(entry.Message) : '',
      sensorData: entry.Message ? this.safeParse(entry.Message) : undefined,
    }));

    this.scrollToBottom();
  }

  private scrollToBottom(): void {
    setTimeout(() => {
      if (this.chatBody) {
        this.chatBody.nativeElement.scrollTop = this.chatBody.nativeElement.scrollHeight;
      }
    });
  }

  private parseMessage(msg: string): string {
    try {
      const obj = JSON.parse(msg);
      return JSON.stringify(obj, null, 2);
    } catch {
      return msg;
    }
  }

  private safeParse(msg: string): any | undefined {
    try {
      return JSON.parse(msg);
    } catch {
      return undefined;
    }
  }

getSensorArray(sensorData: any): any[] {
  if (!sensorData) return [];

  const knownSensors = [
    { key: 'battery', label: 'Battery', icon: 'battery_full', type: 'number', unit: '%' },
    { key: 'battery_low', label: 'Battery Low', icon: 'battery_alert', type: 'boolean' },
    { key: 'device_temperature', label: 'Device Temperature', icon: 'device_thermostat', type: 'number', unit: '°C' },
    { key: 'temperature', label: 'Temperature', icon: 'thermostat', type: 'number', unit: '°C' },
    { key: 'humidity', label: 'Humidity', icon: 'water_drop', type: 'number', unit: '%' },
    { key: 'linkquality', label: 'Link Quality', icon: 'wifi', type: 'number' },
    { key: 'power_outage_count', label: 'Power Outages', icon: 'power', type: 'number' },
    { key: 'pressure', label: 'Pressure', icon: 'speed', type: 'number', unit: 'hPa' },
    { key: 'trigger_count', label: 'Trigger Count', icon: 'touch_app', type: 'number' },
    { key: 'voltage', label: 'Voltage', icon: 'flash_on', type: 'number', unit: 'mV' },
    { key: 'water_leak', label: 'Water Leak', icon: 'water_drop', type: 'boolean' },
    { key: 'state', label: 'State', icon: 'toggle_on', type: 'string' },
    { key: 'motor_speed', label: 'Motor Speed', icon: 'speed', type: 'string' },
    { key: 'position', label: 'Position', icon: 'pin_drop', type: 'number', unit: '%' },
    { key: 'update', label: 'Update State', icon: 'system_update_alt', type: 'string' },
    { key: 'action', label: 'Action', icon: 'notifications_active', type: 'string' },
    { key: 'angle', label: 'Angle', icon: 'rotate_right', type: 'number', unit: '°' },
    { key: 'angle_x', label: 'Angle X', icon: 'rotate_left', type: 'number', unit: '°' },
    { key: 'angle_x_absolute', label: 'Angle X Absolute', icon: 'straighten', type: 'number', unit: '°' },
    { key: 'angle_y', label: 'Angle Y', icon: 'rotate_left', type: 'number', unit: '°' },
    { key: 'angle_y_absolute', label: 'Angle Y Absolute', icon: 'straighten', type: 'number', unit: '°' },
    { key: 'angle_z', label: 'Angle Z', icon: 'rotate_left', type: 'number', unit: '°' },
    { key: 'strength', label: 'Strength', icon: 'fitness_center', type: 'number' },
    { key: 'vibration', label: 'Vibration', icon: 'vibration', type: 'boolean' },
    { key: 'x_axis', label: 'X Axis', icon: 'straighten', type: 'number' },
    { key: 'y_axis', label: 'Y Axis', icon: 'straighten', type: 'number' },
    { key: 'z_axis', label: 'Z Axis', icon: 'straighten', type: 'number' },
    { key: 'current', label: 'Current', icon: 'power', type: 'number', unit: 'A' },
    { key: 'operation_mode', label: 'Operation Mode', icon: 'settings', type: 'string' },
    { key: 'power', label: 'Power', icon: 'power', type: 'number', unit: 'W' },
    { key: 'side', label: 'Side', icon: 'swap_horiz', type: 'number' }
  ];

  // Create a map for quick lookup
  const knownMap = new Map(knownSensors.map(s => [s.key, s]));

  const result: any[] = [];

  for (const key of Object.keys(sensorData)) {
    let sensor = knownMap.get(key);

    let value = sensorData[key];
    // If value is nested object and key is 'update', extract state string
    if (key === 'update' && value && typeof value === 'object') {
      value = value.state || 'Unknown';
    }

    if (!sensor) {
      
      sensor = {
        key,
        label: key.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase()), 
        icon: 'device_unknown',
        type: typeof value
      };
    }

    if (value === undefined || value === null) continue;

    result.push({
      ...sensor,
      value
    });
  }

  return result;
}




}
