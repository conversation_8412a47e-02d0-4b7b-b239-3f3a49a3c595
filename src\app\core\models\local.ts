import { Client } from "./client";
import { AuditModel } from "./models-audit/audit-model";
import { Site } from "./site";
import { TypeLocal } from "./TypeLocal.1";

export class Local extends AuditModel {
  Name!: string;
  Description!: string;
  Floor!: number;
  SensorsCount!: number;
  Capacity!: number;
  Architecture2DImage!: string;
  ImageLocal!: string; 
  Latitude!: number;
  Longtitude!: number;
  IdSite!: string;
  TypeLocalId!: string;
  Site?: Site & { Client?: Client };
  TypeLocal?: TypeLocal;
}


