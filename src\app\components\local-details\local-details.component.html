<div class="local-details-container">
  <div class="site-info-section" *ngIf="!isLoading && local">
    <div class="breadcrumb-nav">
      <button class="back-button" (click)="goBack()">
        <i class="material-icons">arrow_back</i>
      </button>
      <span class="breadcrumb-text">Détails du local: {{ local.Name }}</span>
    </div>

    <div class="info-section">
      <div class="site-images-container">
        <div class="logo-container">
          <ng-container *ngIf="downloadedImageBase64; else noImage">
            <img [src]="'data:image/jpeg;base64,' + downloadedImageBase64" [alt]="local.Name" class="site-logo"
              (error)="onImageError($event)" />
          </ng-container>
          <ng-template #noImage>
            <div class="no-image-placeholder">
              <i class="material-icons">location_city</i>
            </div>
          </ng-template>
        </div>

        <div class="site-stats">
          <div class="stat-item">
            <div class="stat-value">{{ local.Floor }}</div>
            <div class="stat-label">Étage</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ local.Capacity || 0 }}</div>
            <div class="stat-label">Capacité</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ capteurCount || 0 }}</div>
            <div class="stat-label">Capteurs</div>
          </div>
        </div>
      </div>

      <div class="site-info-container">
        <div class="site-header">
          <h2 class="site-name">{{ local.Name }}</h2>
          <div class="site-type">
            Type Local : {{ local.TypeLocal?.Nom || "Type non défini" }}
          </div>
        </div>

        <div class="info-grid">
          <!-- First Row -->
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">description</i> Description
            </div>
            <div class="info-value description-text">
              {{ local.Description || "Non spécifié" }}
            </div>
          </div>

          <div class="info-item" *ngIf="currentSite">
            <div class="info-label">
              <i class="material-icons">business</i> Site
            </div>
            <div class="info-value">
              {{ currentSite.Name }}
              <div *ngIf="currentSite.Address" class="address-value">
                {{ currentSite.Address }}
              </div>
            </div>
          </div>

          <!-- Second Row -->
          <div class="info-item">
            <div class="info-label">
              <mat-icon>sensor_occupied</mat-icon>
              Nombre de capteurs
              <button mat-icon-button (click)="toggleSensorPopup()" class="sensor-info-btn"
                matTooltip="Afficher la liste des capteurs" [class.active]="showSensorPopup">
                <mat-icon>info_outline</mat-icon>
              </button>
            </div>
            <div class="info-value">
              {{ capteurCount }} capteur{{ capteurCount > 1 ? "s" : "" }}
            </div>
          </div>

          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">location_on</i> Coordonnées
            </div>
            <div class="info-value">
              <div *ngIf="local.Latitude && local.Longtitude">
                Lat: {{ local.Latitude | number : "1.4-4" }}, Long:
                {{ local.Longtitude | number : "1.4-4" }}
              </div>
              <div *ngIf="!local.Latitude || !local.Longtitude">
                Coordonnées GPS non spécifiées
              </div>
            </div>
          </div>

          <!-- Third Row -->
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">calendar_today</i> Dates
            </div>
            <div class="info-value">
              <div *ngIf="local.CreatedAt">
                <strong>Créé le:</strong>
                {{ local.CreatedAt | date : "mediumDate" }}
              </div>
              <div *ngIf="local.LastUpdatedAt">
                <strong>Modifié le:</strong>
                {{ local.LastUpdatedAt | date : "mediumDate" }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="content-tabs-section">
    <mat-tab-group [(selectedIndex)]="selectedTabIndex" class="local-tabs" animationDuration="300ms"
      dynamicHeight="true" (selectedTabChange)="onTabSelectionChange($event)">
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon class="tab-icon">design_services</mat-icon>
          <span class="tab-label">Plan d'Architecture</span>
        </ng-template>

        <div class="tab-content">
          <div class="architecture-plan-section">
            <!-- Architecture Loading Indicator -->
            <div class="architecture-loading-container" *ngIf="isArchitectureLoading">
              <div class="spinner"></div>
              <p class="loading-text">
                Chargement du plan d'architecture<span class="dots">...</span>
              </p>
            </div>

            <!-- Architecture Canvas -->
            <div class="canvas-container">
              <canvas id="canvas" width="900" height="500"></canvas>
            </div>

            <!-- No Architecture Message -->
            <div class="no-architecture-message" *ngIf="!isArchitectureLoading && !architecture2DData">
              <mat-icon>architecture</mat-icon>
              <h3>Aucun plan d'architecture disponible</h3>
              <p>Ce local ne contient pas de plan d'architecture.</p>
            </div>
          </div>
        </div>
      </mat-tab>

      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon class="tab-icon">rule</mat-icon>
          <span class="tab-label">Règles Appliquées</span>
        </ng-template>

        <div class="tab-content">
          <div class="applied-rules-section">
            <div class="rules-header"></div>
            <app-applied-rules-list [localId]="localId">
            </app-applied-rules-list>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>


  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner"></div>
    <p class="loading-text">
      Chargement des détails du local<span class="dots">...</span>
    </p>
  </div>

  <!-- Beautiful Sensor Popup Overlay -->
  <div class="sensor-popup-overlay" *ngIf="showSensorPopup" (click)="closeSensorPopup()">
    <div class="sensor-popup-container" (click)="$event.stopPropagation()">
      <div class="sensor-popup-header">
        <div class="popup-title">
          <mat-icon>sensors</mat-icon>
          <span>Détails des Capteurs et Contrôleurs</span>
        </div>
        <button mat-icon-button (click)="closeSensorPopup()" class="close-popup-btn">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <div class="sensor-popup-body">
        <!-- Controllers Section -->
        <div class="section-container" *ngIf="controllerDetails.length > 0">
          <div class="section-header">
            <mat-icon>developer_board</mat-icon>
            <h3>Contrôleurs Associés</h3>
          </div>
          <div class="controller-list">
            <div class="controller-card" *ngFor="let controller of controllerDetails"
              [title]="'Cliquez pour plus de détails'" (click)="toggleControllerDetails(controller.id)">
              <div class="controller-main">
                <div class="controller-icon">
                  <mat-icon>memory</mat-icon>
                </div>
                <div class="controller-info">
                  <div class="controller-name">{{ controller.model }}</div>
                </div>
                <div class="controller-arrow">
                  <mat-icon>{{
                    controller.showDetails ? "expand_less" : "expand_more"
                    }}</mat-icon>
                </div>
              </div>
              <div class="controller-details" *ngIf="controller.showDetails">
                <div class="detail-row">
                  <span class="detail-label">Adresse IP:</span>
                  <span class="detail-value">{{
                    controller.ipAddress || "Non spécifié"
                    }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">MAC:</span>
                  <span class="detail-value">{{
                    controller.macAddress || "Non spécifié"
                    }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Dernière connexion:</span>
                  <span class="detail-value">
                    {{
                    controller.lastConnection
                    ? (controller.lastConnection | date : "medium")
                    : "Inconnue"
                    }}
                  </span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">État:</span>
                  <span class="detail-value" [class.active]="controller.state" [class.inactive]="!controller.state">
                    {{ controller.state ? "Actif" : "Inactif" }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sensors Section -->
        <div class="section-container">
          <div class="section-header">
            <mat-icon>sensor_occupied</mat-icon>
            <h3>Capteurs Associés</h3>
          </div>
          <div class="sensor-count-badge">
            <mat-icon>device_hub</mat-icon>
            <span>{{ capteurCount }} capteur{{ capteurCount > 1 ? "s" : "" }}</span>
          </div>

          <div class="sensor-list-container" *ngIf="capteurNames && capteurNames.length > 0; else noSensors">
            <div class="sensor-card" *ngFor="let capteur of capteurNames; let i = index"
              [title]="'Cliquez pour plus de détails'" [class.expanded]="capteur.showDetails">
              <div class="sensor-main" (click)="toggleSensorDetails(capteur.id)">
                <div class="sensor-icon" [style.background]="getSensorColor(capteur.typeCapteurName)">
                  <mat-icon>sensors</mat-icon>
                </div>
                <div class="sensor-info">
                  <div class="sensor-name">{{ capteur.name }}</div>
                  <div class="sensor-meta">
                    <span class="sensor-type">{{ capteur.typeCapteurName }}</span>
                    <span class="sensor-controller">{{
                      capteur.controllerName
                      }}</span>
                  </div>
                </div>
                <div class="sensor-arrow">
                  <mat-icon>{{
                    capteur.showDetails ? "expand_less" : "expand_more"
                    }}</mat-icon>
                </div>
              </div>
              <div class="sensor-details" *ngIf="capteur.showDetails">
                <div class="detail-row" data-type="type">
                  <span class="detail-label" data-type="type">Type:</span>
                  <span class="detail-value">{{ capteur.typeCapteurName }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Contrôleur:</span>
                  <span class="detail-value">{{ capteur.controllerName }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Dernière activité:</span>
                  <span class="detail-value">
                    {{
                    getLastActivity(capteur.id)
                    ? (getLastActivity(capteur.id) | date : "medium")
                    : "Inconnue"
                    }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <ng-template #noSensors>
            <div class="no-sensors-message">
              <mat-icon>sensors_off</mat-icon>
              <h3>Aucun capteur associé</h3>
              <p>Ce local ne contient actuellement aucun capteur.</p>
            </div>
          </ng-template>
        </div>
      </div>
    </div>
  </div>
</div>
