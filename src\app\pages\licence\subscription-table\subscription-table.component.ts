import { Compo<PERSON>, OnIni<PERSON>, ViewChild, <PERSON>ementRef, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { Client } from '@app/core/models/client';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { Licence } from '@app/core/models/licence';
import { SubscriptionWithDetails } from '@app/core/models/subscriptionPage';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { NgxLoadingModule } from 'ngx-loading';
import { Option } from '@app/core/models/option';
import { SubscriptionPageApiService } from '@app/core/services/administrative/subscriptionPage.service';
import { Router } from '@angular/router';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { FilterParam, Lister, Pagination, SortPage, Page } from '@app/core/models/util/page';
import { fork<PERSON>oin } from 'rxjs';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';

@Component({
  selector: 'app-subscription-table',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgxLoadingModule,
    MatPaginatorModule,
  ],
  templateUrl: './subscription-table.component.html',
  styleUrls: ['./subscription-table.component.css'],
  animations: [
    trigger('fadeInOut', [
      state('void', style({
        opacity: 0,
        transform: 'scale(0.9)'
      })),
      state('*', style({
        opacity: 1,
        transform: 'scale(1)'
      })),
      transition('void <=> *', animate('300ms ease-in-out'))
    ]),
    trigger('slideDown', [
      state('void', style({
        transform: 'translateY(-20px)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void <=> *', animate('400ms ease-out'))
    ]),
    trigger('cardSlide', [
      transition(':enter', [
        style({ transform: 'translateX(100%)', opacity: 0 }),
        animate('0.4s ease-out', style({ transform: 'translateX(0)', opacity: 1 }))
      ]),
      transition(':leave', [
        animate('0.3s ease-in', style({ transform: 'translateX(-100%)', opacity: 0 }))
      ])
    ]),
    trigger('tableSlide', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(20px)' }),
        animate('0.4s ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ])
  ]
})
export class SubscriptionTableComponent implements OnInit {
  // Add Math reference for template
  Math = Math;
  
  constructor(
    private clientApiService: ClientApiService,
    private licenceApiService: LicenceApiService,
    private subscriptionApiService: SubscriptionApiService,
    private subscriptionPageApiService: SubscriptionPageApiService,
    private router: Router
  ) {}

  // Search and filter properties
  searchQuery = '';
  currentSearchTerm = ''; 
  selectedStatusFilter: string = '';
  selectedLicenceFilter: string = '';
  selectedFrequencyFilter: string = '';

  // Pagination - Following site-management pattern
  pageSize = 6;
  currentPage = 0;
  totalCount = 0;

  // UI state
  isLoading = false;
  showCannotModifyResiliePopup = false;
  showCannotCancelPendingPopup = false;
  showCancelPopup = false;
  showCancelNotification = false;
  
  // Data arrays
  subscriptions: SubscriptionWithDetails[] = [];
  filteredTableRows: any[] = [];
  subscriptionTableRows: any[] = [];
  clients: Client[] = [];
  filteredClients: Client[] = [];
  licences: Licence[] = [];
  
  // FIXED: Dynamic filter options
  availableStatuses: string[] = [];
  availableLicences: string[] = [];
  availableFrequencies: string[] = [];
  
  // Cancel popup data
  cancelClientName: string = '';
  cancelLicenceName: string = '';
  cancelSubscriptionRow: any = null;
  licenceToCancel: Licence | null = null;
  
  selectedClient: Client | null = null;

  // Statistics
  totalSubscriptions = 0;
  pendingSubscriptions = 0;
  paidSubscriptions = 0;
  cancelledSubscriptions = 0;

  // View mode
  viewMode: 'cards' | 'table' = 'cards';

  // Other properties
  modifyingLicence: Licence | null = null;
  oldLicence: Licence | null = null;
  selectedLicenseForConfirmation: Licence | null = null;
  licenceWithUnsavedChanges: Licence | null = null;
  oldOptions: Option[] = [];

  showSubscriptionSelectionPopup = false;
  showConfirmSubscriptionPopup = false;
  subscriptionSearchQuery = '';
  selectedStatusFilterPopup = '';
  filteredSubscriptionsForPopup: any[] = [];
  selectedSubscriptionForModification: any = null;

  cardMode: 'add' | 'modify' = 'add';
  request: Lister = {};

  // Table resizing
  @ViewChild('tableContainer', { static: false }) tableContainer!: ElementRef;
  @ViewChild('cardsContainer', { static: false }) cardsContainer!: ElementRef;
  @ViewChild('resizableTable', { static: false }) resizableTable!: ElementRef;
  @ViewChild('leftIndicator', { static: false }) leftIndicator!: ElementRef;
  @ViewChild('rightIndicator', { static: false }) rightIndicator!: ElementRef;

  resizingColumn: number | null = null;
  startX: number = 0;
  startWidth: number = 0;
  columnWidths: number[] = [180, 140, 100, 100, 90, 90, 120, 130];

  touchStartX: number = 0;
  touchStartY: number = 0;
  isSwipeMode: boolean = false;

  // Pagination computed properties - Following site-management pattern
  get hasPagination(): boolean {
    return Math.ceil(this.totalCount / this.pageSize) > 1; // Show pagination when there's more than 1 page
  }

  get tableFirstItem(): number {
    if (this.totalCount === 0) return 0;
    return this.currentPage * this.pageSize + 1;
  }

  get tableLastItem(): number {
    return Math.min((this.currentPage + 1) * this.pageSize, this.totalCount);
  }

  ngOnInit(): void {
    this.resetStatistics();
    this.loadInitialData();
    this.applyColumnWidths();
  }

  // FIXED: Better initial data loading with proper pagination calculation
  loadInitialData(): void {
    this.isLoading = true;

    // Load statistics first - this gives us the TOTAL count from database
    this.subscriptionApiService.getCounts().subscribe({
      next: (data: { TotalAbonnements: number; EnAttente: number; Payes: number; Resilies: number }) => {
        this.totalSubscriptions = data.TotalAbonnements;
        this.pendingSubscriptions = data.EnAttente;
        this.paidSubscriptions = data.Payes;
        this.cancelledSubscriptions = data.Resilies;
        
        // Initialize totalCount from database stats - Following site-management pattern
        this.totalCount = data.TotalAbonnements;

        console.log('Initial pagination from database stats:', {
          totalFromDB: data.TotalAbonnements,
          pageSize: this.pageSize,
          totalCount: this.totalCount,
          shouldShowPagination: this.hasPagination
        });
      },
      error: (error) => {
        console.error('Error loading statistics:', error);
      }
    });

    // Load reference data for filters
    forkJoin({
      clients: this.clientApiService.getAll(),
      licences: this.licenceApiService.getAll()
    }).subscribe({
      next: (data) => {
        this.clients = data.clients || [];
        this.licences = data.licences || [];
        
        // Extract licence names for filter dropdown
        this.availableLicences = [...new Set(
          this.licences
            .map(l => l.Name)
            .filter(name => name && name.trim())
        )].sort();
        
        console.log('Loaded licences for filter:', this.availableLicences);
        
        // Load subscriptions page
        this.loadSubscriptionsPage();
      },
      error: (error) => {
        console.error('Error loading reference data:', error);
        // Still try to load subscriptions
        this.loadSubscriptionsPage();
      }
    });
  }

  // FIXED: Search and filter creation with proper AND/OR logic
  createLister(): Lister {
    const lister: Lister = {};
    
    // Set up pagination - Following site-management pattern
    const pagination: Pagination = {
      CurrentPage: this.currentPage + 1, // API expects 1-based pagination
      PageSize: this.pageSize,
    };
    lister.Pagination = pagination;

    // Set up sorting
    const sortParams: SortPage[] = [{
      Column: 'DateDebut',
      Sort: 'desc',
    }];
    lister.SortParams = sortParams;

    // Set up filtering with proper AND/OR logic
    const filterParams: FilterParam[] = [];

    // FIXED: Global search filter with exact and partial matching
    if (this.currentSearchTerm.trim()) {
      const searchTerm = this.currentSearchTerm.trim();
      
      // Search filters - these should be OR'd together
      const searchFilters: FilterParam[] = [
        // Exact matches
        {
          Column: 'ClientName',
          Op: 'eq',
          Value: searchTerm,
          AndOr: 'OR',
        },
        // Partial matches (LIKE)
        {
          Column: 'ClientName',
          Op: 'contains',
          Value: `%${searchTerm}%`,
          AndOr: 'OR',
        }
      ];
      
      filterParams.push(...searchFilters);
    }

    // FIXED: Status filter - must be AND with search results
    if (this.selectedStatusFilter) {
      filterParams.push({
        Column: 'Status',
        Op: 'eq',
        Value: this.selectedStatusFilter,
        AndOr: filterParams.length > 0 ? 'AND' : 'AND',
      });
    }

    // FIXED: Licence filter - must be AND with previous filters
    if (this.selectedLicenceFilter) {
      filterParams.push({
        Column: 'LicenceName',
        Op: 'contains',
        Value: this.selectedLicenceFilter,
        AndOr: filterParams.length > 0 ? 'AND' : 'AND',
      });
    }

    // FIXED: Payment frequency filter - must be AND with previous filters
    if (this.selectedFrequencyFilter) {
      filterParams.push({
        Column: 'PaymentFrequency',
        Op: 'eq',
        Value: this.selectedFrequencyFilter,
        AndOr: filterParams.length > 0 ? 'AND' : 'AND',
      });
    }

    if (filterParams.length > 0) {
      lister.FilterParams = filterParams;
    }

    console.log('Created lister with filters:', {
      search: this.currentSearchTerm,
      status: this.selectedStatusFilter,
      licence: this.selectedLicenceFilter,
      frequency: this.selectedFrequencyFilter,
      filterParams: filterParams
    });
    
    return lister;
  }

  // FIXED: Load subscriptions with corrected pagination logic
  loadSubscriptionsPage(): void {
    this.isLoading = true;
    const lister = this.createLister();
    
    // Always log the API request for debugging
    this.logApiRequest(lister);
    
    this.subscriptionPageApiService.getPage(lister).subscribe({
      next: (result: Page<SubscriptionWithDetails>) => {
        console.log('API Response received:', result);
        
        // FIXED: Better data extraction
        this.subscriptions = result.Content || [];
        
        // Update totalCount from backend response - Following site-management pattern
        if (result.Lister?.Pagination?.totalElement !== undefined) {
          this.totalCount = result.Lister.Pagination.totalElement;
        } else if (result.Lister?.Pagination?.TotalElement !== undefined) {
          this.totalCount = result.Lister.Pagination.TotalElement;
        } else {
          this.totalCount = Math.max(0, this.subscriptions.length);
        }

        console.log('Updated pagination from backend response:', {
          totalCount: this.totalCount,
          currentPage: this.currentPage,
          pageSize: this.pageSize,
          subscriptionsLoaded: this.subscriptions.length,
          hasFilters: !!(this.selectedStatusFilter || this.selectedLicenceFilter || this.selectedFrequencyFilter || this.currentSearchTerm)
        });
        
        // Extract filter options from current data
        this.extractFilterOptionsFromData();
        
        // Update table rows
        this.updateSubscriptionTableRows();
        this.filteredTableRows = [...this.subscriptionTableRows];
        
        // Validate filter results
        if (this.selectedStatusFilter) {
          const matchingStatuses = this.filteredTableRows.filter(row => row.Status === this.selectedStatusFilter);
          console.log(`Status filter validation: ${matchingStatuses.length}/${this.filteredTableRows.length} rows match "${this.selectedStatusFilter}"`);
          
          if (matchingStatuses.length !== this.filteredTableRows.length) {
            console.warn('⚠️ Status filter may not be working correctly on server side');
            console.log('Expected status:', this.selectedStatusFilter);
            console.log('Actual statuses found:', [...new Set(this.filteredTableRows.map(row => row.Status))]);
          }
        }
        
        console.log('Final results:', {
          subscriptions: this.subscriptions.length,
          tableRows: this.filteredTableRows.length,
          filterOptions: {
            statuses: this.availableStatuses,
            licences: this.availableLicences,
            frequencies: this.availableFrequencies
          }
        });
        
        setTimeout(() => this.applyColumnWidths(), 100);
        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Error fetching subscriptions page:', error);
        this.isLoading = false;
        this.subscriptions = [];
        this.filteredTableRows = [];
        this.totalCount = 0;
      }
    });
  }

  // Add the debugging methods to the main component
  logApiRequest(lister: Lister): void {
    console.log('=== API REQUEST DEBUG ===');
    console.log('Full Lister Object:', JSON.stringify(lister, null, 2));
    
    if (lister.FilterParams) {
      console.log('Filter Parameters:');
      lister.FilterParams.forEach((filter, index) => {
        console.log(`  ${index + 1}. ${filter.Column} ${filter.Op} "${filter.Value}" (${filter.AndOr})`);
      });
    }
    
    if (lister.Pagination) {
      console.log('Pagination:', {
        page: lister.Pagination.CurrentPage,
        size: lister.Pagination.PageSize
      });
    }
    
    if (lister.SortParams) {
      console.log('Sorting:', lister.SortParams);
    }
    console.log('=== END API REQUEST DEBUG ===');
  }

  // NEW: Method to refresh database pagination
  refreshDatabasePagination(): void {
    console.log('🔄 Refreshing database pagination...');
    
    this.subscriptionApiService.getCounts().subscribe({
      next: (data: { TotalAbonnements: number; EnAttente: number; Payes: number; Resilies: number }) => {
        // Update statistics
        this.totalSubscriptions = data.TotalAbonnements;
        this.pendingSubscriptions = data.EnAttente;
        this.paidSubscriptions = data.Payes;
        this.cancelledSubscriptions = data.Resilies;
        
        // Update totalCount based on current filters - Following site-management pattern
        if (!this.hasActiveFilters()) {
          // No filters active - use total database count
          this.totalCount = data.TotalAbonnements;
          console.log('Updated totalCount from database (no filters):', {
            totalCount: this.totalCount
          });
        }
        // If filters are active, keep the filtered totalCount from API

        console.log('Database stats refreshed:', {
          total: data.TotalAbonnements,
          pending: data.EnAttente,
          paid: data.Payes,
          cancelled: data.Resilies,
          currentPagination: {
            totalCount: this.totalCount,
            currentPage: this.currentPage,
            pageSize: this.pageSize
          }
        });
      },
      error: (error) => {
        console.error('Error refreshing database pagination:', error);
      }
    });
  }

  // NEW: Helper method to check if any filters are active
  private hasActiveFilters(): boolean {
    return !!(
      this.selectedStatusFilter || 
      this.selectedLicenceFilter || 
      this.selectedFrequencyFilter || 
      this.currentSearchTerm.trim()
    );
  }
  testStatusOnlyFilter(status: string): void {
    console.log(`🎯 Testing ONLY status filter for: "${status}"`);
    
    const simpleLister: Lister = {
      Pagination: {
        CurrentPage: 1,
        PageSize: this.pageSize
      },
      SortParams: [{
        Column: 'DateDebut',
        Sort: 'desc'
      }],
      FilterParams: [{
        Column: 'Status',
        Op: '=',
        Value: status,
        AndOr: 'AND'
      }]
    };
    
    console.log('Simple lister for status filter:', JSON.stringify(simpleLister, null, 2));
    
    this.isLoading = true;
    this.subscriptionPageApiService.getPage(simpleLister).subscribe({
      next: (result) => {
        console.log(`✅ Status filter test result for "${status}":`, result);
        console.log('Number of results:', result.Content?.length || 0);
        
        if (result.Content && result.Content.length > 0) {
          const returnedStatuses = result.Content.map(sub => sub.Status);
          console.log('All statuses in result:', returnedStatuses);
          console.log('Unique statuses:', [...new Set(returnedStatuses)]);
          
          // Check if all results match the filter
          const allMatch = result.Content.every(sub => sub.Status === status);
          console.log(`All results match "${status}":`, allMatch);
          
          if (!allMatch) {
            console.warn('⚠️ Some results do not match the filter!');
            result.Content.forEach((sub, index) => {
              if (sub.Status !== status) {
                console.warn(`Result ${index}: Expected "${status}", got "${sub.Status}"`);
              }
            });
          }
        } else {
          console.log(`No results found for status "${status}"`);
        }
        
        this.isLoading = false;
      },
      error: (error) => {
        console.error(`❌ Status filter test failed for "${status}":`, error);
        this.isLoading = false;
      }
    });
  }

  // FIXED: Extract filter options from loaded data
  private extractFilterOptionsFromData(): void {
    if (this.subscriptions && this.subscriptions.length > 0) {
      // Extract unique statuses
      const statuses = this.subscriptions
        .map(sub => sub.Status)
        .filter(status => status && typeof status === 'string' && status.trim())
        .map(status => status!.trim());
      this.availableStatuses = [...new Set(statuses)].sort();

      // Extract unique payment frequencies
      const frequencies = this.subscriptions
        .map(sub => sub.PaymentFrequency)
        .filter(freq => freq && typeof freq === 'string' && freq.trim())
        .map(freq => freq!.trim());
      this.availableFrequencies = [...new Set(frequencies)].sort();

      // If we don't have licences from the initial load, extract from subscriptions
      if (this.availableLicences.length === 0) {
        const licenceNames = this.subscriptions
          .map(sub => sub.LicenceName)
          .filter(name => name && typeof name === 'string' && name.trim())
          .map(name => name!.trim());
        this.availableLicences = [...new Set(licenceNames)].sort();
      }
    }
  }

  // FIXED: Filter operations that trigger new API calls with better logging
  performSearch(): void {
    const trimmedQuery = this.searchQuery.trim();
    this.currentSearchTerm = trimmedQuery;
    this.currentPage = 0;
    console.log('🔍 Performing search for:', this.currentSearchTerm);
    this.loadSubscriptionsPage();
  }

  clearSearch(): void {
    this.searchQuery = '';
    this.currentSearchTerm = '';
    this.currentPage = 0;
    console.log('🗑️ Clearing search');
    this.loadSubscriptionsPage();
  }

  // Filter operations that update pagination correctly - Following site-management pattern
  onFilterChange(): void {
    this.currentPage = 0;
    console.log('🔧 Filters changed:', {
      status: this.selectedStatusFilter,
      licence: this.selectedLicenceFilter,
      frequency: this.selectedFrequencyFilter
    });
    
    // Additional validation for status filter
    if (this.selectedStatusFilter) {
      console.log(`📊 Filtering by status: "${this.selectedStatusFilter}"`);
      console.log('Available statuses:', this.availableStatuses);
    }
    
    // Apply server-side filtering by loading new page
    this.loadSubscriptionsPage();
  }

  clearFilters(): void {
    this.selectedStatusFilter = '';
    this.selectedLicenceFilter = '';
    this.selectedFrequencyFilter = '';
    this.searchQuery = '';
    this.currentSearchTerm = '';
    this.currentPage = 0;
    
    console.log('🧹 Clearing all filters - loading fresh data from server');

    // Load fresh data from server without filters
    this.loadSubscriptionsPage();
  }

  filterByStatus(status: string): void {
    this.selectedStatusFilter = status;
    this.currentPage = 0;
    console.log('📊 Filtering by status:', status);
    this.loadSubscriptionsPage();

    // Scroll to the appropriate container after a short delay to ensure content is loaded
    setTimeout(() => {
      this.scrollToContainer();
    }, 300);
  }

  private scrollToContainer(): void {
    let targetElement: ElementRef | null = null;

    if (this.viewMode === 'cards' && this.cardsContainer) {
      targetElement = this.cardsContainer;
    } else if (this.viewMode === 'table' && this.tableContainer) {
      targetElement = this.tableContainer;
    }

    if (targetElement && targetElement.nativeElement) {
      targetElement.nativeElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    }
  }

  // NEW: Method to test if a specific status filter is working
  testStatusFilter(status: string): void {
    console.log(`🧪 Testing filter for status: "${status}"`);
    this.selectedStatusFilter = status;
    this.currentPage = 0;
    this.loadSubscriptionsPage();

    // Scroll to the appropriate container after a short delay to ensure content is loaded
    setTimeout(() => {
      this.scrollToContainer();
    }, 300);
  }

  // FIXED: Pagination methods


  // Mat-paginator event handler - Following site-management pattern
  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadSubscriptionsPage(); // Load new page from server
  }





  // Template getter methods
  getTotalSubscriptionsCount(): number {
    return this.totalSubscriptions;
  }

  getPendingSubscriptionsCount(): number {
    return this.pendingSubscriptions;
  }

  getPaidSubscriptionsCount(): number {
    return this.paidSubscriptions;
  }

  getCancelledSubscriptionsCount(): number {
    return this.cancelledSubscriptions;
  }

  // UI methods
  toggleViewMode(mode: 'cards' | 'table'): void {
    this.viewMode = mode;
  }

  onTableAction(action: string, row: any): void {
    if (row && row.Status && row.Status.toLowerCase() === 'résilié') {
      return;
    }

    if (action === 'Modifier') {
      if (row && row.SubscriptionId) {
        this.router.navigate(['/modification-abonnement', row.SubscriptionId]);
      } else {
        console.error('No SubscriptionId found in row:', row);
      }
      return;
    }

    if (action === 'Annuler') {
      const subscription = this.subscriptions.find(sub => sub.Id === row.SubscriptionId);

      if (!subscription) {
        console.error('No subscription found for ID:', row.SubscriptionId);
        return;
      }

      if (typeof subscription.Status === 'string' && subscription.Status.trim().toLowerCase() === 'en attente') {
        this.showCannotCancelPendingPopup = true;
        return;
      }

      if (typeof subscription.Status === 'string' && subscription.Status.trim().toLowerCase() === 'résilié') {
        return;
      }

      this.cancelClientName = row.ClientName;
      this.cancelLicenceName = row.LicenceName;
      this.cancelSubscriptionRow = row;
      this.showCancelPopup = true;
      return;
    }
  }

  confirmCancelSubscription(): void {
    if (!this.cancelSubscriptionRow) return;
    const row = this.cancelSubscriptionRow;
    const subscription = this.subscriptions.find(sub => sub.Id === row.SubscriptionId);

    if (!subscription) {
      this.showCancelPopup = false;
      return;
    }

    const updatedSubscription = {
      Id: subscription.Id,
      ClientId: subscription.ClientId,
      LicenceId: subscription.LicenceId,
      DateDebut: subscription.DateDebut,
      DateFin: subscription.DateFin,
      Price: subscription.Price,
      PaymentFrequency: subscription.PaymentFrequency,
      Status: 'Résilié'
    };

    this.subscriptionApiService.update(updatedSubscription).subscribe({
      next: () => {
        this.showCancelNotification = true;
        setTimeout(() => this.showCancelNotification = false, 3000);
        this.showCancelPopup = false;     
        
        // Reload statistics and current page
        this.loadInitialData();
      },
      error: (error: any) => {
        console.error('Error cancelling subscription:', error);
        this.showCancelPopup = false;
      }
    });
  }

  // Utility methods
  getStatusBadgeClass(status: string): string {
    if (!status) return 'status-badge-default';
    
    switch (status.toLowerCase()) {
      case 'payé':
        return 'status-badge-paid';
      case 'en attente':
        return 'status-badge-pending';
      case 'résilié':
        return 'status-badge-cancelled';
      default:
        return 'status-badge-default';
    }
  }

  getStatusIcon(status: string): string {
    if (!status) return 'help_outline';
    
    switch (status.toLowerCase()) {
      case 'payé':
        return 'check_circle';
      case 'en attente':
        return 'schedule';
      case 'résilié':
        return 'cancel';
      default:
        return 'help_outline';
    }
  }

  formatDate(date: Date | string | null): string {
    if (!date) return '';
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    return `${day}/${month}/${year}`;
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount || 0);
  }

  getClientInitials(clientName: string): string {
    if (!clientName) return '?';
    return clientName.charAt(0).toUpperCase();
  }

  trackBySubscriptionId(index: number, item: any): any {
    return item.SubscriptionId;
  }

  // Popup methods
  closeCannotCancelPendingPopup() {
    this.showCannotCancelPendingPopup = false;
  }

  closeCannotModifyResiliePopup() {
    this.showCannotModifyResiliePopup = false;
  }

  goToAbonementNew() {
    this.router.navigate(['/affectation-abonnement']);
  }

  // Refresh methods
  refreshStatistics(): void {
    this.loadSubscriptionsPage();
  }

  refreshAll(): void {
    this.loadInitialData();
  }

  // Table resizing methods
  startResize(event: MouseEvent, columnIndex: number): void {
    event.preventDefault();
    this.resizingColumn = columnIndex;
    this.startX = event.clientX;
    this.startWidth = this.columnWidths[columnIndex];
    
    document.addEventListener('mousemove', this.doResize.bind(this));
    document.addEventListener('mouseup', this.stopResize.bind(this));
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }

  @HostListener('document:mousemove', ['$event'])
  doResize(event: MouseEvent): void {
    if (this.resizingColumn === null) return;
    
    const diff = event.clientX - this.startX;
    const newWidth = Math.max(60, this.startWidth + diff); 
    
    this.columnWidths[this.resizingColumn] = newWidth;
    this.applyColumnWidths();
  }

  @HostListener('document:mouseup', ['$event'])
  stopResize(event: MouseEvent): void {
    if (this.resizingColumn === null) return;
    
    this.resizingColumn = null;
    document.removeEventListener('mousemove', this.doResize.bind(this));
    document.removeEventListener('mouseup', this.stopResize.bind(this));
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }

  applyColumnWidths(): void {
    setTimeout(() => {
      if (!this.resizableTable) return;
      
      const table = this.resizableTable.nativeElement;
      const headers = table.querySelectorAll('th');
      const rows = table.querySelectorAll('tbody tr');
      
      headers.forEach((header: HTMLElement, index: number) => {
        if (index < this.columnWidths.length) {
          header.style.width = `${this.columnWidths[index]}px`;
          header.style.minWidth = `${Math.min(60, this.columnWidths[index])}px`;
        }
      });
      
      rows.forEach((row: HTMLElement) => {
        const cells = row.querySelectorAll('td');
        cells.forEach((cell: HTMLElement, index: number) => {
          if (index < this.columnWidths.length) {
            cell.style.width = `${this.columnWidths[index]}px`;
            cell.style.minWidth = `${Math.min(60, this.columnWidths[index])}px`;
          }
        });
      });
      
      const totalWidth = this.columnWidths.reduce((sum, width) => sum + width, 0);
      table.style.minWidth = `${totalWidth}px`;
    }, 0);
  }

  resetColumnWidths(): void {
    this.columnWidths = [180, 140, 100, 100, 90, 90, 120, 130];
    this.applyColumnWidths();
  }

  // Touch events for mobile
  onTouchStart(event: TouchEvent): void {
    if (event.touches.length === 1) {
      this.touchStartX = event.touches[0].clientX;
      this.touchStartY = event.touches[0].clientY;
      this.isSwipeMode = true;
    }
  }

  onTouchMove(event: TouchEvent): void {
    if (!this.isSwipeMode || event.touches.length !== 1) return;
    
    const touch = event.touches[0];
    const deltaX = touch.clientX - this.touchStartX;
    const deltaY = touch.clientY - this.touchStartY;
    
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 10) {
      event.preventDefault(); 
      
      if (deltaX > 0 && this.leftIndicator) {
        this.leftIndicator.nativeElement.style.opacity = '1';
        if (this.rightIndicator) this.rightIndicator.nativeElement.style.opacity = '0';
      } else if (deltaX < 0 && this.rightIndicator) {
        this.rightIndicator.nativeElement.style.opacity = '1';
        if (this.leftIndicator) this.leftIndicator.nativeElement.style.opacity = '0';
      }
    }
  }

  onTouchEnd(event: TouchEvent): void {
    if (!this.isSwipeMode) return;
    
    const container = this.tableContainer?.nativeElement;
    if (!container) return;
    
    const deltaX = event.changedTouches[0].clientX - this.touchStartX;
    const swipeThreshold = 50;
    
    if (Math.abs(deltaX) > swipeThreshold) {
      const scrollAmount = container.clientWidth * 0.8; 
      
      if (deltaX > 0) {
        container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
      } else {
        container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
      }
    }

    setTimeout(() => {
      if (this.leftIndicator) this.leftIndicator.nativeElement.style.opacity = '0';
      if (this.rightIndicator) this.rightIndicator.nativeElement.style.opacity = '0';
    }, 300);
    
    this.isSwipeMode = false;
  }

  // FIXED: Update subscription table rows with better data mapping
  updateSubscriptionTableRows(): void {
    this.subscriptionTableRows = this.subscriptions.map(sub => {
      return {
        ClientName: sub.ClientName || 'N/A',
        LicenceName: sub.LicenceName || 'N/A',
        DateDebut: this.formatDate(sub.DateDebut || null),
        DateFin: this.formatDate(sub.DateFin || null),
        Status: sub.Status || 'N/A',
        price: sub.Price || 0,
        PaymentFrequency: sub.PaymentFrequency || 'N/A',
        ClientId: sub.ClientId,
        LicenceId: sub.LicenceId,
        SubscriptionId: sub.Id,
        subscription: sub,
        showActions: sub.Status && sub.Status.toLowerCase() !== 'résilié',
      };
    });
  }

  resetStatistics(): void {
    this.totalSubscriptions = 0;
    this.pendingSubscriptions = 0;
    this.paidSubscriptions = 0;
    this.cancelledSubscriptions = 0;
  }
}