import { Licence } from "./licence";
import { AuditModel } from "./models-audit/audit-model";
import { Organisation } from "./organisation";
import { Site } from "./site";

export class Client extends AuditModel {
  ClientLogo?: string; // Optional field for client logo, can be a base64 string or URL
  Name!: string;
  Address!: string;
  PhoneNumber!: string;
  SIREN!: string;
  SIRET!: string;
  RC!: string;
  ICE!: string;
  IF!: string;
  Patente!: string;
  LegalForm!: string;
  CompanyCreationDate!: Date;
  ContactName?: string;
  ContactEmail?: string;
  Brand?: string;
  CompanyType?: string;
  CompanySize?: number;
  City?: string;
  Region?: string;
  Country?: string;
  Filiale?: string;
  Status?: string;
  RecusiveClientId?: string;
  ActiveEquipment?: number;
  InactiveEquipment?: number;
  IdOrganisation?: string;
  Organisation?: Organisation;
  Sites?: Site[];
  Licences?: Licence[];
  isEnabled?: boolean; // Optional field for enabling/disabling the client
  ContactAddress?: string;
  ClientStatus?: string;
  BusinessSector!: string;
  UserId?: string;
  DialCode?: string;
  CountryCode?: string;
}

export class ClientWithSiteStatus extends Client {
  SiteActif?: string;
  SiteInactif?: string;
  SiteEnMaintenance?: string;
}