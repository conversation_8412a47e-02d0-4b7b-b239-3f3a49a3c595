import { Compo<PERSON>, On<PERSON>nit, On<PERSON><PERSON>roy, ElementRef, ViewChild, Input, Output, EventEmitter } from '@angular/core';
import { SensorsBackEndDataService } from '@app/core/sensors-back-end-data.service';
import { MultiTopicListenerService } from '@app/core/services/multi-topic-listener-mqtt.service';
import * as d3 from 'd3';
import { HierarchyPointLink } from 'd3-hierarchy';
import { DynamicFormDetailsComponent } from "../dynamic-form-details/dynamic-form-details.component";
import { ControllerApiService } from '@app/core/services/administrative/controller.service';
import {buildTree} from './services/tree-builder';
import { TabLoadingService } from '@app/core/services/tab-loading.service';

@Component({
  selector: 'app-hierarchy',
  templateUrl: './hierarchy.component.html',
  styleUrls: ['./hierarchy.component.css'],
  imports: [DynamicFormDetailsComponent],
})

export class HierarchyComponent implements OnIni<PERSON>, On<PERSON><PERSON>roy {
  @ViewChild('svgContainer', { static: true }) svgRef!: ElementRef;
  @Input() clientId = "";
  showPopup = false;
  @Output() loadingChange = new EventEmitter<{ isLoading: boolean; message?: string }>();

  constructor(
    private sensorsService: SensorsBackEndDataService,
    private listener: MultiTopicListenerService,
    private controllerService: ControllerApiService,
    private tabLoadingService: TabLoadingService // inject the service
  ) {}

  ngOnInit(): void {
    this.getSensors('client', this.clientId, {
      pageSize: 100,
      pageNumber: 1,
      searchTerm: '',
    });
    
  }

  ngOnDestroy(): void {}

  openPopupForDevice() {
    this.showPopup = true;
  }

  getSensors(
    type: 'client' | 'site' | 'local' | 'controller' | 'all',
    id: string,
    payload: any
  ): void {
    // Start loading
    this.tabLoadingService.setTabLoading('hierarchy', true, 'Chargement de la hiérarchie...');
    this.loadingChange.emit({ isLoading: true, message: 'Chargement de la hiérarchie...' });

    this.sensorsService.getCapteursByType(type, id, payload).subscribe({
      next: (response) => {
        const devices = response.Data || [];
        const treeData = buildTree(devices)
        this.renderTree(treeData);
        // Stop loading
        this.tabLoadingService.setTabLoading('hierarchy', false);
        this.loadingChange.emit({ isLoading: false });
      },
      error: (err) => {
        console.error('Failed to load sensors:', err);
        // Stop loading
        this.tabLoadingService.setTabLoading('hierarchy', false, 'Erreur lors du chargement');
        this.loadingChange.emit({ isLoading: false, message: 'Erreur lors du chargement' });
      },
    });
  }
  getControllerDetails (id:string){
    this.controllerService.getById(id).subscribe({
      next(response) {
        console.log(response);
        return response;
      },
      error: (error) => {
        console.error(error);
      }
    })
  }

  renderTree(data: any) {
    const svg = d3.select(this.svgRef.nativeElement);
    svg.selectAll('*').remove();

    const width = 1000;
    const height = 600;

    const zoomG = svg.append('g');

    svg.call(
      d3.zoom<SVGSVGElement, unknown>()
        .scaleExtent([0.5, 2])
        .on('zoom', (event) => {
          zoomG.attr('transform', event.transform);
        })
    );

    const root = d3.hierarchy(data);
    const treeLayout = d3.tree().size([height, width - 200]);
    treeLayout(root);

    const nodes = root.descendants();
    const minY = d3.min(nodes, (d) => d.y) ?? 0;
    const maxY = d3.max(nodes, (d) => d.y) ?? width;
    const offsetX = (width - (maxY - minY)) / 2 - minY;

    const g = zoomG.append('g').attr('transform', `translate(${offsetX}, 0)`);

    const now = Date.now();
    const ONE_HOUR = 3600 * 1000;
    const ONE_DAY = 86400 * 1000;

    const links = root.links() as HierarchyPointLink<any>[];

    // Draw curved paths
    const pathElements = g.selectAll('path.link')
      .data(links)
      .enter()
      .append('path')
      .attr('class', 'link')
      .attr('fill', 'none')
      .attr('stroke-width', 3)
      .attr('stroke', (d) => {
        const sourceType = d.source.data.type;
        const targetType = d.target.data.type;

        if (sourceType === 'controller' && targetType === 'sensor') {
          const sensor = d.target.data.data;
          if (sensor?.State != null && sensor?.State != '') {
            const lastSeen = sensor?.LastSeen ? new Date(sensor.LastSeen).getTime() : null;
            if (lastSeen && now - lastSeen < ONE_HOUR) return '#4caf50';
            else if (lastSeen && now - lastSeen <= ONE_DAY) return '#ff9800';
            else return '#f44336';
          } else return '#9e9e9e';
        }
        return '#4caf50';
      })
      .attr('d', (d) => `
        M${d.source.y},${d.source.x}
        C${(d.source.y + d.target.y) / 2},${d.source.x}
         ${(d.source.y + d.target.y) / 2},${d.target.x}
         ${d.target.y},${d.target.x}
      `);

    // Animate circles on sensor ➝ controller links
    links.forEach((link, i) => {
      if (link.source.data.type === 'controller' && link.target.data.type === 'sensor') {
        const path = pathElements.nodes()[i];
        const circle = g.append('circle')
          .attr('r', 4)
          .attr('fill', '#00bcd4');

        this.animateAlongPath(circle, path);
      }
    });

    // Draw nodes
    const nodeGroups = g.selectAll('g.node')
      .data(nodes)
      .enter()
      .append('g')
      .attr('transform', (d) => `translate(${d.y},${d.x})`)
      .style('cursor', 'pointer')
      .on('click', (event, d) => {
        if(d.data.type == 'controller'){
          const controllerDetails = this.getControllerDetails(d.data.id);
        }
        //this.openPopupForDevice();
        //alert(`You clicked on: ${d.data.id}`);
      });

    nodeGroups
      .append('circle')
      .attr('r', 20)
      .attr('fill', 'white')
      .attr('stroke-width', 3)
      .attr('stroke', (d) => {
        switch (d.data.type) {
          case 'client': return '#3f51b5';
          case 'site': return '#009688';
          case 'local': return '#ff9800';
          case 'controller': return '#9c27b0';
          case 'sensor': return '#4caf50';
          default: return '#999';
        }
      })
      .style('filter', 'drop-shadow(0 1px 2px rgba(0,0,0,0.2))');

    nodeGroups
      .append('image')
      .attr('xlink:href', (d) => this.getIconForType(d.data.type))
      .attr('x', -12)
      .attr('y', -12)
      .attr('width', 24)
      .attr('height', 24);

    nodeGroups
      .append('text')
      .attr('dy', 30)
      .attr('text-anchor', 'middle')
      .text((d) => d.data.name)
      .style('font-size', '12px')
      .style('fill', '#333')
      .style('font-family', 'Segoe UI, sans-serif')
      .style('user-select', 'none');

    nodeGroups.append('title').text(d => d.data.name);
  }

  animateAlongPath(circle: d3.Selection<SVGCircleElement, unknown, null, undefined>, path: SVGPathElement | null) {
    if (!path) return;

    const totalLength = path.getTotalLength();

    function repeat() {
      circle
        .attr('opacity', 1)
        .transition()
        .duration(2000)
        .ease(d3.easeLinear)
        .attrTween('transform', () => {
          return function (t) {
            const point = path?.getPointAtLength(t * totalLength);
            return `translate(${point?.x},${point?.y})`;
          };
        })
        .on('end', repeat);
    }

    repeat();
  }

  getIconForType(type: string): string {
    switch (type) {
      case 'client':
        return '../../../../assets/hierarchy-images/corporate.png';
      case 'site':
        return '../../../../assets/hierarchy-images/building.png';
      case 'local':
        return '../../../../assets/hierarchy-images/meeting-room.png';
      case 'controller':
        return '../../../../assets/hierarchy-images/network-hub.png';
      case 'sensor':
        return '../../../../assets/hierarchy-images/sensor.png';
      default:
        return '../../../../assets/hierarchy-images/customer.png';
    }
  }
}
