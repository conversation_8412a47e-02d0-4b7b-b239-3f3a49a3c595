<!-- <div *ngIf="isLoading" class="loading-message">
  Vos abonnements sont en cours de chargement...
</div> -->

<ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>

<div *ngIf="!isLoading" class="table-container">
  <div class="table-header">
    <h2 class="table-title">Abonnements</h2>
  </div>

  <table *ngIf="subscriptions.length > 0" class="subscriptions-table">
    <thead>
      <tr>
        <th>Client</th>
        <th>Licence</th>
        <th>Date Début</th>
        <th>Date Fin</th>
        <th>Statut</th>
        <th>Prix</th>
        <th>Fréquence de payment</th>
        <th>Renouveller</th>
      </tr>
    </thead>
    <tbody>
      <tr
        *ngFor="let sub of subscriptions"
        (click)="selectSubscription(sub)"
        [class.selected]="selectedSubscription === sub"
        style="cursor: pointer"
      >
        <td>{{ getClientName(sub.ClientId) }}</td>
        <td>{{ getLicenceName(sub.LicenceId) }}</td>
        <td>{{ sub.DateDebut | date : "dd-MM-yyyy" }}</td>
        <td>{{ sub.DateFin | date : "dd-MM-yyyy" }}</td>
        <td>{{ sub.Status }}</td>
        <td>{{ sub.Price | currency : "EUR" }}</td>
        <td>{{ sub.PaymentFrequency }}</td>

        <td class="action-cell">
          <button
            class="action-btn renew-btn"
            [class.disabled-btn]="!canRenewSubscription(sub)"
            (click)="openRenewModal(sub); $event.stopPropagation()"
            [disabled]="isRenewing[sub.Id] || !canRenewSubscription(sub)"
            [title]="getRenewButtonTitle(sub)"
          >
            <span *ngIf="isRenewing[sub.Id]" class="loading-spinner"></span>
            <span *ngIf="!isRenewing[sub.Id]"><mat-icon>cached</mat-icon></span>
            <span *ngIf="!isRenewing[sub.Id]">Renouveller</span>
          </button>
        </td>
      </tr>
    </tbody>
  </table>

  <div *ngIf="subscriptions.length === 0" class="no-subscriptions">
    Aucun abonnement trouvé.
  </div>

  <div class="pagination-container">
    <mat-paginator
      [length]="totalSubscriptions"
      [pageSize]="pageSize"
      [pageSizeOptions]="pageSizeOptions"
      [pageIndex]="currentPage"
      (page)="onPageChange($event)"
      aria-label="Select page"
    >
    </mat-paginator>
  </div>
</div>

<div class="modal-overlay" *ngIf="showDetailModal" (click)="closeDetailModal()">
  <div class="modal-container" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>Détails de l'Abonnement</h3>
      <button class="close-button" (click)="closeDetailModal()" title="Fermer">
        ×
      </button>
    </div>
    <div class="modal-content">
      <app-detail
        [subscription]="selectedSubscription"
        [licences]="licences"
        (detailsClosed)="closeDetailModal()"
      ></app-detail>
    </div>
  </div>
</div>

<div class="modal-overlay" *ngIf="showRenewModal" (click)="closeRenewModal()">
  <div
    class="modal-container renew-modal-container"
    (click)="$event.stopPropagation()"
  >
    <div class="modal-header renew-modal-header">
      <h3>Renouvellement d'abonnement</h3>
      <button class="close-button" (click)="closeRenewModal()" title="Fermer">
        ×
      </button>
    </div>
    <div class="modal-content renew-modal-content">
      <ng-container *ngIf="selectedSubscription">
        <p class="renew-modal-text">
          Vous êtes sur le point de renouveler l'abonnement pour
          <strong class="client-name">{{
            getClientName(selectedSubscription.ClientId)
          }}</strong>
          avec la licence
          <strong class="licence-name">{{
            getLicenceName(selectedSubscription.LicenceId)
          }}</strong
          >.
        </p>
        <p class="renew-modal-text">
          La date de fin actuelle est le
          <strong>{{
            selectedSubscription.DateFin | date : "dd-MM-yyyy"
          }}</strong
          >.
        </p>

        <!-- <div class="form-group">
          <label for="renewType" class="form-label"
            >Fréquence de renouvellement:</label
          >
          <div class="radio-group">
            <input
              type="radio"
              id="renewMonthly"
              name="renewType"
              value="monthly"
              [(ngModel)]="renewType"
              class="radio-input"
            />
            <label for="renewMonthly" class="radio-label">Mensuel</label>

            <input
              type="radio"
              id="renewYearly"
              name="renewType"
              value="yearly"
              [(ngModel)]="renewType"
              class="radio-input"
            />
            <label for="renewYearly" class="radio-label">Annuel</label>
          </div>
        </div> -->

        <div class="modal-actions">
          <button class="cancel-btn" (click)="closeRenewModal()">
            Annuler
          </button>
          <button
            class="confirm-renew-btn"
            (click)="confirmAndRenewSubscription()"
            [disabled]="isRenewing[selectedSubscription.Id]"
          >
            <span
              *ngIf="isRenewing[selectedSubscription.Id]"
              class="loading-spinner"
            ></span>
            <span *ngIf="!isRenewing[selectedSubscription.Id]"
              >Confirmer le renouvellement</span
            >
          </button>
        </div>
      </ng-container>
      <ng-template #noSubscriptionSelected>
        <p>Aucun abonnement sélectionné pour le renouvellement.</p>
      </ng-template>
    </div>
  </div>
</div>
