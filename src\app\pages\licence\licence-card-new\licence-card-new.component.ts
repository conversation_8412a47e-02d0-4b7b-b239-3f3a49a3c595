import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { Client } from '@app/core/models/client';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { LicenceOptionApiService } from '@app/core/services/administrative/licenceOption.service';
import { OptionApiService } from '@app/core/services/administrative/option.service';
import { Licence } from '@app/core/models/licence';
import { LicenceOption } from '@app/core/models/licenceOption';
import { Option } from '@app/core/models/option';
import { Subscription } from '@app/core/models/subscription';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { SubscribedOptionsApiService } from '@app/core/services/administrative/subscribedOptions.service';
import { SubscribedOptions } from '@app/core/models/subscribedoptions';
import { NgxLoadingModule } from 'ngx-loading';
import { Router } from '@angular/router';
import { FactureApiService } from '@app/core/services/administrative/facture.service';
import { FilterParam, Lister, Pagination, SortPage } from '@app/core/models/util/page';
import { detailsFactureApiService } from '@app/core/services/administrative/detailsFacture.service'; // Fixed import

@Component({
  selector: 'app-licence',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgxLoadingModule,
  ],
  templateUrl: './licence-card-new.component.html',
  styleUrls: ['./licence-card-new.component.css'],
  animations: [
    trigger('slideInOut', [
      state('void', style({
        transform: 'translateY(-10px)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void <=> *', animate('200ms ease-in-out'))
    ]),
    trigger('growIn', [
      state('void', style({
        transform: 'scale(0.8)',
        opacity: 0
      })),
      state('*', style({
        transform: 'scale(1)',
        opacity: 1
      })),
      transition('void <=> *', animate('150ms ease-in-out'))
    ]),
    trigger('fadeInOut', [
      state('void', style({
        opacity: 0,
        transform: 'scale(0.9)'
      })),
      state('*', style({
        opacity: 1,
        transform: 'scale(1)'
      })),
      transition('void <=> *', animate('300ms ease-in-out'))
    ]),
    trigger('slideDown', [
      state('void', style({
        transform: 'translateY(-20px)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void <=> *', animate('400ms ease-out'))
    ])
  ]
})
export class LicenceCardNewComponent implements OnInit {

  constructor(
    private clientApiService: ClientApiService,
    private licenceApiService: LicenceApiService,
    private licenceOptionApiService: LicenceOptionApiService,
    private optionApiService: OptionApiService,
    private subscriptionApiService: SubscriptionApiService,
    private subscribedOptionsApiService: SubscribedOptionsApiService,
    private factureApiService: FactureApiService,
    private detailsFactureApiService: detailsFactureApiService, // Fixed type annotation
    private router: Router,
  ) {}

  // Component state properties
  searchQuery = '';
  showDropdown = false;
  isConfirming = false;
  isLoading = false;
  selectedPaymentFrequency: string = 'Mensuel';
  customDateFin: string = '';
  customMonths: number = 1;
  subscriptionIdFromRoute: string | null = null; 
  showConfirmationPopup = false;
  selectedLicenseForConfirmation: Licence | null = null;
  showSuccessNotification = false;
  showChooseClientError = false;
  showSameClientError = false;
  showNoOptionCheckedPopup = false;
  _pendingRestoreLicence: Licence | null = null;
  _pendingRestoreType: 'affecter' | null = null;
  
  // Data properties
  clients: Client[] = [];
  filteredClients: Client[] = [];
  licences: Licence[] = [];
  options: Option[] = [];
  licenceOptions: LicenceOption[] = []; 
  subscriptions: Subscription[] = [];
  clientSubscription: Subscription | null = null;
  checkedOptions: { [licenceId: string]: Set<string> } = {};
  initialCheckedOptions: { [licenceId: string]: Set<string> } = {};
  selectedClient: Client | null = null;

  // Properly typed request object
  request: Lister = {};

  // Payment frequency options
  paymentFrequencies: { label: string, value: string, months: number }[] = [
    { label: 'Mensuel', value: 'Mensuel', months: 1 },
    { label: 'Annuel', value: 'Annuel', months: 12 }
  ];

  /**
   * Fetch all clients from the API
   */
  fetchClients(): Promise<void> {
    return new Promise((resolve) => {
      // Check if getPage method exists on the service
      if (typeof (this.clientApiService as any).getPage === 'function') {
        // Use pagination-based fetching similar to organisation-management
        const lister: Lister = {
          Pagination: {
            CurrentPage: 1,
            PageSize: 10000, // Large number to get all records
          },
          SortParams: [{
            Column: 'Name',
            Sort: 'asc',
          }]
        };

        (this.clientApiService as any).getPage(lister).subscribe({
          next: (result: any) => {
            this.clients = (result.Content ?? []).map((client: Client) => ({
              ...client,
              Name: client.Name,
              ClientLogo: client.ClientLogo
            }));
            this.filteredClients = [...this.clients];
            this.request = result.Lister || {};
            resolve();
          },
          error: (error: any) => {
            console.error('Error fetching clients with pagination, falling back to getAll:', error);
            // Fallback to getAll() if pagination fails
            this.clientApiService.getAll().subscribe({
              next: (data: Client[]) => {
                this.clients = data.map(client => ({
                  ...client,
                  Name: client.Name,
                  ClientLogo: client.ClientLogo
                }));
                this.filteredClients = [...this.clients];
                resolve();
              },
              error: (error: any) => {
                console.error('Error fetching clients:', error);
                resolve();
              }
            });
          }
        });
      } else {
        // Fallback to getAll() if getPage doesn't exist
        this.clientApiService.getAll().subscribe({
          next: (data: Client[]) => {
            this.clients = data.map(client => ({
              ...client,
              Name: client.Name,
              ClientLogo: client.ClientLogo
            }));
            this.filteredClients = [...this.clients];
            resolve();
          },
          error: (error: any) => {
            console.error('Error fetching clients:', error);
            resolve();
          }
        });
      }
    });
  }

  /**
   * Fetch all licences from the API
   */
  fetchLicences(): Promise<void> {
    return new Promise((resolve) => {
      // Check if getPage method exists on the service
      if (typeof (this.licenceApiService as any).getPage === 'function') {
        // Use pagination-based fetching
        const lister: Lister = {
          Pagination: {
            CurrentPage: 1,
            PageSize: 10000,
          },
          SortParams: [{
            Column: 'Name',
            Sort: 'asc',
          }]
        };

        (this.licenceApiService as any).getPage(lister).subscribe({
          next: (result: any) => {
            this.licences = (result.Content ?? []).map((licence: Licence) => ({
              ...licence,
              name: licence.Name,
              description: licence.Description
            }));
            resolve();
          },
          error: (error: any) => {
            console.error('Error fetching licences with pagination, falling back to getAll:', error);
            // Fallback to getAll() if pagination fails
            this.licenceApiService.getAll().subscribe({
              next: (data: Licence[]) => {
                this.licences = data.map(licence => ({
                  ...licence,
                  name: licence.Name,
                  description: licence.Description
                }));
                resolve();
              },
              error: (error: any) => {
                console.error('Error fetching licences:', error);
                resolve();
              }
            });
          }
        });
      } else {
        // Fallback to getAll() if getPage doesn't exist
        this.licenceApiService.getAll().subscribe({
          next: (data: Licence[]) => {
            this.licences = data.map(licence => ({
              ...licence,
              name: licence.Name,
              description: licence.Description
            }));
            resolve();
          },
          error: (error: any) => {
            console.error('Error fetching licences:', error);
            resolve();
          }
        });
      }
    });
  }

  /**
   * Fetch all options from the API
   */
  fetchOptions(): Promise<void> {
    return new Promise((resolve) => {
      // Check if getPage method exists on the service
      if (typeof (this.optionApiService as any).getPage === 'function') {
        // Use pagination-based fetching
        const lister: Lister = {
          Pagination: {
            CurrentPage: 1,
            PageSize: 10000,
          },
          SortParams: [{
            Column: 'Name',
            Sort: 'asc',
          }]
        };

        (this.optionApiService as any).getPage(lister).subscribe({
          next: (result: any) => {
            this.options = (result.Content ?? []).map((option: Option) => ({
              ...option,
              name: option.Name,
              price: option.Price
            }));
            resolve();
          },
          error: (error: any) => {
            console.error('Error fetching options with pagination, falling back to getAll:', error);
            // Fallback to getAll() if pagination fails
            this.optionApiService.getAll().subscribe({
              next: (data: Option[]) => {
                this.options = data.map(option => ({
                  ...option,
                  name: option.Name,
                  price: option.Price
                }));
                resolve();
              },
              error: (error: any) => {
                console.error('Error fetching options:', error);
                resolve();
              }
            });
          }
        });
      } else {
        // Fallback to getAll() if getPage doesn't exist
        this.optionApiService.getAll().subscribe({
          next: (data: Option[]) => {
            this.options = data.map(option => ({
              ...option,
              name: option.Name,
              price: option.Price
            }));
            resolve();
          },
          error: (error: any) => {
            console.error('Error fetching options:', error);
            resolve();
          }
        });
      }
    });
  }

  /**
   * Fetch all licence options from the API
   */
  fetchLicenceOptions(): Promise<void> {
    return new Promise((resolve) => {
      // Check if getPage method exists on the service
      if (typeof (this.licenceOptionApiService as any).getPage === 'function') {
        // Use pagination-based fetching
        const lister: Lister = {
          Pagination: {
            CurrentPage: 1,
            PageSize: 10000,
          }
        };

        (this.licenceOptionApiService as any).getPage(lister).subscribe({
          next: (result: any) => {
            this.licenceOptions = result.Content ?? [];
            resolve();
          },
          error: (error: any) => {
            console.error('Error fetching licence options with pagination, falling back to getAll:', error);
            // Fallback to getAll() if pagination fails
            this.licenceOptionApiService.getAll().subscribe({
              next: (data: LicenceOption[]) => {
                this.licenceOptions = data;
                resolve();
              },
              error: (error: any) => {
                console.error('Error fetching licence options:', error);
                resolve();
              }
            });
          }
        });
      } else {
        // Fallback to getAll() if getPage doesn't exist
        this.licenceOptionApiService.getAll().subscribe({
          next: (data: LicenceOption[]) => {
            this.licenceOptions = data;
            resolve();
          },
          error: (error: any) => {
            console.error('Error fetching licence options:', error);
            resolve();
          }
        });
      }
    });
  }

  /**
   * Fetch all subscriptions from the API
   */

  /**
   * Toggle option selection for a licence
   */
  toggleOption(licence: Licence, optionId: string, event: Event): void {
    const licenceId = licence.Id;
    if (!this.checkedOptions[licenceId]) {
      this.checkedOptions[licenceId] = new Set();
    }
    if (this.checkedOptions[licenceId].has(optionId)) {
      this.checkedOptions[licenceId].delete(optionId);
    } else {
      this.checkedOptions[licenceId].add(optionId);
    }
  }

  /**
   * Select a client and initialize options
   */
  selectClient(client: Client): void {
    this.selectedClient = client;
    this.searchQuery = '';
    this.showDropdown = false;
    this.filteredClients = [];

    // Clear all previous states immediately
    this.checkedOptions = {};
    this.initialCheckedOptions = {};

    // Set ALL options as checked by default for ALL licences
    this.licences.forEach(licence => {
      const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
      this.checkedOptions[licence.Id] = new Set(allOptionIds);
      this.initialCheckedOptions[licence.Id] = new Set(allOptionIds);
    });
  }

  /**
   * Navigate back to subscription list
   */
  goBackToSubscriptionList(): void {
    this.router.navigate(['/licence']);
  }

  /**
   * Get selected options for confirmation popup
   */
  getSelectedOptionsForConfirmation(): Option[] {
    if (!this.selectedLicenseForConfirmation) return [];
    
    const licenceId = this.selectedLicenseForConfirmation.Id;
    const checkedSet = this.checkedOptions[licenceId] || new Set();
    
    // Return only options that are both linked to the licence AND checked
    return this.getOptionsForLicence(this.selectedLicenseForConfirmation)
      .filter(option => checkedSet.has(option.Id));
  }

  /**
   * Clear client selection and reset options
   */
  clearSelection(): void {
    // Reset to all options checked for all licences
    this.licences.forEach(licence => {
      const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
      this.checkedOptions[licence.Id] = new Set(allOptionIds);
      this.initialCheckedOptions[licence.Id] = new Set(allOptionIds);
    });
    
    this.selectedClient = null;
    
    // Reset payment frequency and dates when clearing selection
    this.selectedPaymentFrequency = 'Mensuel';
    this.customDateFin = '';
  }

  /**
   * Handle license selection
   */
  public selectLicense(licence: Licence): void {
    if (!this.selectedClient) {
      this.showChooseClientError = true;
      setTimeout(() => { this.showChooseClientError = false; }, 2500);
      return;
    }
    
    const checked = this.checkedOptions[licence.Id] || new Set();
    
    if (checked.size === 0) {
      this.showNoOptionCheckedPopup = true;
      this._pendingRestoreLicence = licence;
      this._pendingRestoreType = 'affecter';
      return;
    }
    
    this.selectedLicenseForConfirmation = licence;
    this.showConfirmationPopup = true;
  }

  /**
   * Close no option checked popup
   */
  closeNoOptionCheckedPopup(): void {
    this.showNoOptionCheckedPopup = false;
    if (this._pendingRestoreLicence) {
      const licence = this._pendingRestoreLicence;
      const initial = this.initialCheckedOptions[licence.Id] || new Set();
      this.checkedOptions[licence.Id] = new Set(Array.from(initial));
      this._pendingRestoreLicence = null;
      this._pendingRestoreType = null;
    }
  }

  /**
   * Close same client error popup
   */
  closeSameClientError(): void {
    this.showSameClientError = false;
  }

  /**
   * Confirm license application with comprehensive duplicate check
   */
  async confirmLicenseApplication(): Promise<void> {
    if (!this.selectedClient || !this.selectedLicenseForConfirmation) {
      console.error('Missing client or license');
      return;
    }

    const selectedOptions = this.getSelectedOptionsForConfirmation();

    if (selectedOptions.length === 0) {
      console.error('No options selected for licence');
      this.showNoOptionCheckedPopup = true;
      return;
    }

    try {
      this.isLoading = true;
      this.isConfirming = true;

      const clientId = this.selectedClient.Id;
      const licenceId = this.selectedLicenseForConfirmation.Id;
      const selectedOptionIds = selectedOptions.map(option => option.Id);
      const newStatus = 'En attente';

      // Check for existing subscription with same client, license, payment frequency, and status
      const existingSubscriptions = this.subscriptions.filter(sub => 
        sub.ClientId === clientId && 
        sub.LicenceId === licenceId &&
        sub.PaymentFrequency === this.selectedPaymentFrequency &&
        sub.Status === newStatus
      );

      // If we have potential matches, check their subscribed options
      for (const existingSubscription of existingSubscriptions) {
        try {
          // Get subscribed options for the existing subscription
          const existingSubscribedOptions = await this.subscribedOptionsApiService.getAll().toPromise();
          const existingOptionIds = (existingSubscribedOptions || [])
            .filter(so => so.SubscriptionId === existingSubscription.Id && so.checked)
            .map(so => so.OptionId);

          // Check if the selected options match exactly with existing options
          const selectedOptionsSet = new Set(selectedOptionIds);
          const existingOptionsSet = new Set(existingOptionIds);

          // Check if sets are equal (same size and same elements)
          const setsAreEqual = selectedOptionsSet.size === existingOptionsSet.size && 
            [...selectedOptionsSet].every(optionId => existingOptionsSet.has(optionId));

          if (setsAreEqual) {
            // Same client, same license, same payment frequency, same status, same options - show error
            console.log('Duplicate subscription found:', {
              ClientId: clientId,
              LicenceId: licenceId,
              PaymentFrequency: this.selectedPaymentFrequency,
              Status: newStatus,
              Options: selectedOptionIds
            });
            
            this.showSameClientError = true;
            setTimeout(() => { this.showSameClientError = false; }, 3000);
            this.isLoading = false;
            this.isConfirming = false;
            return;
          }
        } catch (error) {
          console.error('Error checking subscribed options for subscription:', existingSubscription.Id, error);
          // Continue checking other subscriptions even if one fails
        }
      }

      // Continue with subscription creation if no duplicate found
      let dateDebut: Date, dateFin: Date;
      if (this.selectedPaymentFrequency === 'custom' || this.selectedPaymentFrequency === 'Personnalisé') {
        dateDebut = new Date();
        dateFin = this.customDateFin ? new Date(this.customDateFin) : new Date();
      } else {
        dateDebut = new Date();
        dateFin = this.calculateDateFin(dateDebut, this.selectedPaymentFrequency, this.customMonths);
      }
      
      const dateDebutStr = this.formatDate(dateDebut);
      const dateFinStr = this.formatDate(dateFin);

      // Calculate base monthly price
      const baseMonthlyPrice = selectedOptions.reduce((sum, opt) => sum + (opt.Price || 0), 0);
      
      // Calculate total price based on payment frequency
      let totalPrice: number;
      if (this.selectedPaymentFrequency === 'Annuel') {
        totalPrice = baseMonthlyPrice * 12; // Annual price
      } else {
        totalPrice = baseMonthlyPrice; // Monthly price
      }

      const subscription = {
        DateDebut: new Date(dateDebutStr),
        DateFin: new Date(dateFinStr),
        ClientId: clientId,
        LicenceId: licenceId,
        Price: totalPrice,
        Status: newStatus,
        PaymentFrequency: this.selectedPaymentFrequency
      };

      console.log('Creating subscription:', subscription);
      console.log('Base monthly price:', baseMonthlyPrice);
      console.log('Payment frequency:', this.selectedPaymentFrequency);
      console.log('Final total price:', totalPrice);

      const createdSub = await this.subscriptionApiService.create(subscription).toPromise();
      console.log('Created subscription:', createdSub);
      
      if (createdSub && createdSub.Id) {
        await this.saveSubscribedOptionsForLicence(createdSub.Id, selectedOptionIds);
        await this.createFactureforAbonnement(createdSub);
        console.log('Saved subscribed options');

        this.showConfirmationPopup = false;
        this.selectedLicenseForConfirmation = null;

        this.showSuccessNotification = true;
        
        setTimeout(() => {
          this.showSuccessNotification = false;
        }, 3000);

        // Refresh data
        await Promise.all([
          this.fetchClients()
        ]);
        
        const refreshedClient = this.clients.find(c => c.Id === this.selectedClient?.Id);
        if (refreshedClient) {
          this.selectClient(refreshedClient);
        }
      } else {
        console.error('Failed to create subscription - no ID returned');
        throw new Error('Failed to create subscription');
      }
    } catch (error) {
      console.error('Error applying license:', error);
      // Show error message to user
      alert('Erreur lors de l\'affectation de la licence. Veuillez réessayer.');
    } finally {
      this.isLoading = false;
      this.isConfirming = false;
    }
  }

  private async createFactureforAbonnement(subscription: Subscription): Promise<void> {
    const dateFacture = new Date();
    const facture = {
      SubscriptionId: subscription.Id,
      Total: subscription.Price,
      Status: subscription.Status,
      IdLicence: subscription.LicenceId,
      Number: await this.factureApiService.genFactureCode().toPromise(), 
      DateFacture: new Date(),
      DateEcheance: new Date(dateFacture.getTime() + 14 * 24 * 60 * 60 * 1000)
    }
    console.log('Facture to create:', facture);
    try {
      await this.factureApiService.create(facture).toPromise();
      console.log('Facture created for subscription:', facture);
    } catch (error) {
      console.error('Error creating facture for subscription:', error);
    }
  }

  getMonthsForPaymentFrequency(paymentFrequency: string): number {
    const freq = this.paymentFrequencies.find(f => f.value === paymentFrequency);
    if (!freq) return 1;
    return freq.months;
  }

  getOptionsTotalForFrequency(licence: Licence, checkedSet: Set<string>, paymentFrequency: string): number {
    const months = this.getMonthsForPaymentFrequency(paymentFrequency);
    return this.getOptionsForLicence(licence)
      .filter(opt => checkedSet.has(opt.Id))
      .reduce((sum, opt) => sum + ((opt.Price || 0) * months), 0);
  }

  cancelLicenseApplication(): void {
    this.showConfirmationPopup = false;
    this.selectedLicenseForConfirmation = null;
  }

  closeSuccessNotification(): void {
    this.showSuccessNotification = false;
  }

  /**
   * Get licence options total price
   */
  getLicenceOptionsTotal(licence: Licence): number {
    if (!licence) return 0;
    
    const licenceId = licence.Id;
    const checkedSet = this.checkedOptions[licenceId] || new Set();
    
    const baseTotal = this.getOptionsForLicence(licence)
      .filter(opt => checkedSet.has(opt.Id))
      .reduce((sum, opt) => sum + (opt.Price || 0), 0);

    if (this.selectedPaymentFrequency === 'Annuel') {
      return baseTotal * 12; // Annual total
    } else {
      return baseTotal; // Monthly total
    }
  }

  /**
   * Scroll to top of page
   */
  scrollToTop(): void {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  /**
   * Check if in affecter card mode
   */
  isAffecterCard(): boolean {
    return true; // Since we only have add mode now
  }

  /**
   * Check if option is checked for licence
   */
  isOptionChecked(licence: Licence, optionId: string): boolean {
    return this.checkedOptions[licence.Id]?.has(optionId) ?? false;
  }

  /**
   * Get options for a specific licence
   */
  getOptionsForLicence(licence: Licence): Option[] {
    if (!licence) return [];
    const linkedOptionIds = this.licenceOptions
      .filter((lo: LicenceOption) => lo.LicenceId === licence.Id)
      .map((lo: LicenceOption) => lo.OptionId);
    return this.options.filter((opt: Option) => linkedOptionIds.includes(opt.Id));
  }

  /**
   * Check if option is linked to licence
   */
  isOptionLinkedToLicence(licenceId: string, optionId: string): boolean {
    return this.licenceOptions.some((lo: LicenceOption) => lo.LicenceId === licenceId && lo.OptionId === optionId);
  }

  /**
   * Format date for display
   */
  formatDate(date: Date | string | null): string {
    if (!date) return '';
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    return `${year}/${month}/${day}`;
  }

  /**
   * Calculate end date based on frequency
   */
  calculateDateFin(dateDebut: Date, paymentFrequency: string, customMonths: number): Date {
    let monthsToAdd = 1;
    switch (paymentFrequency) {
      case 'Mensuel':
        monthsToAdd = 1;
        break;
      case 'Annuel':
        monthsToAdd = 12;
        break;
      default:
        monthsToAdd = 1;
    }
    const result = new Date(dateDebut);
    const originalDay = result.getDate();
    let newMonth = result.getMonth() + monthsToAdd;
    let newYear = result.getFullYear();
    newYear += Math.floor(newMonth / 12);
    newMonth = newMonth % 12;
    result.setFullYear(newYear, newMonth, 1);
    const lastDay = new Date(result.getFullYear(), result.getMonth() + 1, 0).getDate();
    result.setDate(Math.min(originalDay, lastDay));
    return result;
  }

  /**
   * Save subscribed options for a licence
   */
  private async saveSubscribedOptionsForLicence(subscriptionId: string, checkedOptionIds: string[]): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.subscribedOptionsApiService.getAll().subscribe({
        next: (allSubscribedOptions: SubscribedOptions[]) => {
          const toDelete = allSubscribedOptions.filter(
            (so: SubscribedOptions) => so.SubscriptionId === subscriptionId
          );
          
          // Function to create new subscribed options
          const createNewOptions = () => {
            const createPromises = checkedOptionIds.map(optionId => {
              const subscribedOption: Partial<SubscribedOptions> = {
                SubscriptionId: subscriptionId,
                OptionId: optionId,
                checked: true
              };
              return this.subscribedOptionsApiService.create(subscribedOption).toPromise();
            });
            
            Promise.all(createPromises)
              .then(() => resolve())
              .catch((error) => {
                console.error('Error creating subscribed options:', error);
                reject(error);
              });
          };

          if (toDelete.length === 0) {
            // No existing options to delete, just create new ones
            createNewOptions();
          } else {
            // Delete existing options first
            const deletePromises = toDelete.map(so => 
              this.subscribedOptionsApiService.delete(so.Id).toPromise()
            );
            
            Promise.all(deletePromises)
              .then(() => createNewOptions())
              .catch((error) => {
                console.error('Error deleting existing options:', error);
                // Continue with creating new options even if delete fails
                createNewOptions();
              });
          }
        },
        error: (error) => {
          console.error('Error fetching subscribed options:', error);
          reject(error);
        }
      });
    });
  }

  /**
   * Filter clients based on search query
   */
  filterClients(): void {
    if (!this.searchQuery.trim()) {
      this.filteredClients = [];
      this.showDropdown = false;
      return;
    }
    const query = this.searchQuery.toLowerCase().trim();
    this.filteredClients = this.clients
      .filter(client => client.Name && client.Name.toLowerCase().includes(query))
      .slice(0, 5);
    this.showDropdown = this.filteredClients.length > 0;
  }

  /**
   * Clear client selection
   */
  onClearSelection(): void {
    this.clearSelection();
  }

  /**
   * Component initialization
   */
  ngOnInit(): void {
    this.isLoading = true;
    Promise.all([
      this.fetchClients(),
      this.fetchLicences(),
      this.fetchOptions(),
      this.fetchLicenceOptions()
    ]).then(() => {
      // Initialize all options as checked for all licences
      this.licences.forEach(licence => {
        const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
        this.checkedOptions[licence.Id] = new Set(allOptionIds);
        this.initialCheckedOptions[licence.Id] = new Set(allOptionIds);
      });
      
      this.isLoading = false;
    }).catch((error) => {
      console.error('Error loading data:', error);
      this.isLoading = false;
    });
  }
}