
export interface RuleComprehensive {
  RuleId: string;
  Enabled: boolean;
  Priority: number;
  RuleSummary: string; // This will hold the summary of the rule
  RawData: string; // This will hold the JSON for conditions/actions/name
  RuleCreatedAt: string;
  RuleLastUpdatedAt: string | null;
  TotalClients: number;
  TotalSites: number;
  TotalLocals: number;
  LastTriggered: string | null;
  Status: 'active' | 'inactive';
  TagsString: string | null;
}
