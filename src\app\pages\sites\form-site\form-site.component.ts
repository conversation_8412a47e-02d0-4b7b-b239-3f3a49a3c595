 import { Component, EventEmitter, Input, OnInit, Output, OnChanges, ViewEncapsulation } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Site } from '@app/core/models/site';
import { HotToastService } from '@ngxpert/hot-toast';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';
import { SiteApiService } from '@app/core/services/administrative/site.service';


@Component({
  selector: 'app-form-site',
  standalone: true,
  templateUrl: './form-site.component.html',
  styleUrls: ['./form-site.component.css'],
  encapsulation: ViewEncapsulation.None,
    imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatIconModule,
    NgxUiLoaderModule,
  ],
})
export class FormSiteComponent implements OnInit, OnChanges {

  @Input() selectedSite: Site | null = null;
  @Input() editSiteForm!: FormGroup;
  @Input() showOptionalFields: boolean = false;
  
  @Output() formSubmit = new EventEmitter<void>();
  @Output() closeForm = new EventEmitter<void>();
  @Output() toggleOptionalFields = new EventEmitter<Event>();
  @Output() removeExistingImage = new EventEmitter<void>();
  @Output() editImagesSelected = new EventEmitter<Event>();

  // Track if image was changed
  imageChanged: boolean = false;
  isLoadingImage: boolean = false;

  constructor(
    private readonly dialog: MatDialog,
    private readonly toastService: HotToastService,
    private readonly siteService: SiteApiService
  ) {}
ngOnInit(): void {

}

ngOnChanges(): void {
  if (this.selectedSite) {
    this.imageChanged = false;

    // If site has an ID but no image data, try to load it
    if (this.selectedSite.Id && (!this.selectedSite.Image || this.selectedSite.Image === 'null')) {
      this.loadSiteImage();
    }
  }
}

// Load site image using the download service
private loadSiteImage(): void {
  if (!this.selectedSite?.Id || this.isLoadingImage) return;

  this.isLoadingImage = true;

  this.siteService.downloadImage(this.selectedSite.Id).subscribe({
    next: (response) => {
      if (response?.image && this.selectedSite) {
        this.selectedSite.Image = response.image;
      }
      this.isLoadingImage = false;
    },
    error: (error) => {
      console.error('Error loading site image:', error);
      this.isLoadingImage = false;
    }
  });
}
  getImageUrl(imageData: string): string {
    if (imageData.startsWith('http') || imageData.startsWith('data:image')) {
      return imageData;
    }
    return `data:image/jpeg;base64,${imageData}`;
  }

  onEditImagesSelected(event: Event): void {
    this.imageChanged = true;
    this.editImagesSelected.emit(event);
  }

  onToggleOptionalFields(event: Event): void {
    this.toggleOptionalFields.emit(event);
  }

  onRemoveExistingImage(): void {
    this.imageChanged = true;
    this.removeExistingImage.emit();
  }

  // Get the display URL for existing image
  getExistingImageUrl(): string | null {
    if (this.selectedSite?.Image &&
        this.selectedSite.Image !== 'null' &&
        this.selectedSite.Image !== '' &&
        this.selectedSite.Image !== null) {

      // Check if it's already a data URL
      if (this.selectedSite.Image.startsWith('data:image')) {
        return this.selectedSite.Image;
      }

      // Otherwise, create data URL
      return `data:image/jpeg;base64,${this.selectedSite.Image}`;
    }
    return null;
  }

  // Check if site has existing image
  hasExistingImage(): boolean {
    return !!(this.selectedSite?.Image &&
             this.selectedSite.Image !== 'null' &&
             this.selectedSite.Image !== '' &&
             this.selectedSite.Image !== null);
  }

  onSubmit(): void {
    if (this.editSiteForm.valid && this.selectedSite) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        width: '400px',
        data: {
          title: 'Confirmer la modification',
          message: 'Êtes-vous sûr de vouloir modifier ce site ?',
          icon: 'edit'
        }
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.formSubmit.emit();
        }
      });
    }
  }

  onClose(): void {
    this.closeForm.emit();
  }

  onRemoveImage(): void {
    this.removeExistingImage.emit();
  }
}