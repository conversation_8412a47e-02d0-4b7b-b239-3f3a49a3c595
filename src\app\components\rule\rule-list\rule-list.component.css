/* rule-list.component.css (COMPLETE VERSION WITH EXECUTION DATA STYLING) */

.no-data-message {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary);
  font-size: 16px;
  background: var(--background);
  border-radius: 12px;
  margin-top: 20px;
  border: 2px dashed var(--card-border);
  position: relative;
}

.no-data-message::before {
  content: '🔍';
  font-size: 64px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.2;
}

.hierarchy-empty-state {
  text-align: center;
  padding: 48px 20px;
  color: var(--text-secondary);
  font-size: 16px;
  background: var(--background);
  border-radius: 12px;
  margin: 24px 0;
  border: 2px dashed var(--card-border);
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.hierarchy-empty-state::before {
  content: '🗂️';
  font-size: 48px;
  display: block;
  margin-bottom: 12px;
  opacity: 0.2;
}

/* IMPROVED RULES LIST - Much better separation */
.rules-list {
  display: flex;
  flex-direction: column;
  gap: 32px; /* Significantly increased from 16px */
  padding: 24px 8px; /* Added horizontal padding */
  margin: 0 auto;
  max-width: 100%;
}

/* IMPROVED RULE ITEM - Clear visual boundaries */
.rule-item {
  background: var(--white);
  border-radius: 12px;
  border: 1px solid var(--card-border);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.08),
    0 2px 4px rgba(0, 0, 0, 0.04); /* Enhanced shadow for better separation */
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  animation: fadeInUp 0.3s ease-out;
  margin: 8px 0; /* Additional margin for extra separation */
  isolation: isolate; /* Create new stacking context */
}

/* Add separator between rules */
.rule-item:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--card-border), transparent);
  opacity: 0.5;
}

.rule-item:hover:not(.action-pending) {
  box-shadow: 
    0 8px 24px rgba(0, 0, 0, 0.12),
    0 4px 8px rgba(0, 0, 0, 0.06); /* More prominent hover shadow */
  border-color: var(--green-main);
  transform: translateY(-4px); /* More lift on hover */
}

.rule-item.rule-expanded {
  box-shadow: 
    0 12px 32px rgba(0, 0, 0, 0.15),
    0 6px 12px rgba(0, 0, 0, 0.08);
  border-color: var(--green-main);
  margin: 16px 0; /* Extra margin when expanded */
}

.rule-item.action-pending {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.rule-item.action-pending::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  z-index: 10;
  border-radius: inherit;
}

.rule-item.simple-rule {
  border-left: 4px solid var(--green-main);
  min-height: 80px;
}

.rule-item.simple-rule .rule-content {
  padding: 24px 28px;
  padding-right: 140px;
  display: flex;
  align-items: flex-start;
  min-height: 80px;
  position: relative;
}

.rule-item.simple-rule .rule-details {
  flex: 1;
  gap: 8px;
}

.rule-item.simple-rule .rule-header {
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 0;
  flex-wrap: nowrap;
  gap: 16px;
}

.rule-item.simple-rule .rule-name {
  font-size: 16px;
  font-weight: 500;
  flex: 1;
  margin-right: 12px;
}

.rule-item.simple-rule .rule-actions {
  position: absolute;
  top: 24px;
  right: 24px;
  gap: 6px;
}

.rule-item.complex-rule {
  background: var(--white);
  position: relative;
}

.rule-item.complex-rule .rule-content {
  padding: 24px 28px;
  padding-right: 140px;
  position: relative;
}

.rule-item.complex-rule .rule-actions {
  position: absolute;
  top: 24px;
  right: 24px;
  gap: 6px;
}

.rule-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 20px;
  position: relative;
}

.rule-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 0;
}

.rule-header {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 8px;
  min-height: 32px;
}

.rule-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--card-title);
  flex: 1;
  min-width: 0;
  word-break: break-word;
}

.priority {
  font-size: 12px;
  color: var(--text-secondary);
  background: var(--beige-darl);
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  white-space: nowrap;
}

.status-active {
  background: var(--green-light);
  color: var(--green-dark);
}

.status-inactive {
  background: var(--beige-darl);
  color: var(--text-secondary);
}

.summary-preview {
  margin-top: 8px;
  padding: 12px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  border: 1px solid #bae6fd;
  transition: all 0.2s ease;
  cursor: pointer;
}

.summary-preview:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  border-color: #38bdf8;
}

.summary-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.summary-icon {
  color: #0ea5e9 !important;
  font-size: 20px !important;
  width: 20px !important;
  height: 20px !important;
  flex-shrink: 0;
  margin-top: 2px;
}

.summary-text {
  font-size: 13px;
  line-height: 1.5;
  color: #0c4a6e;
  margin: 0;
  font-style: italic;
  flex: 1;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.usage-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 12px;
  margin: 12px 0;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--background);
  padding: 10px 12px;
  border-radius: 8px;
  border: 1px solid var(--card-border);
  transition: all 0.2s ease;
}

.stat-item:hover {
  background: var(--green-light);
  border-color: var(--green-main);
}

.stat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
  color: var(--green-main);
  flex-shrink: 0;
}

.stat-value {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

.stat-label {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 500;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 8px 0;
}

.no-tags {
  color: var(--text-secondary);
  font-style: italic;
  font-size: 12px;
  padding: 8px 12px;
  background: var(--background);
  border-radius: 6px;
  border: 1px dashed var(--card-border);
}

.tag {
  font-size: 12px;
  font-weight: 500;
  background: var(--green-light);
  color: var(--green-dark);
  padding: 6px 12px;
  border-radius: 16px;
  white-space: nowrap;
  border: 1px solid var(--green-main);
  transition: all 0.2s ease;
}

.tag:hover {
  background: var(--green-main);
  color: var(--white);
}

.actions-triggered {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
  padding: 12px;
  background: var(--background);
  border-radius: 8px;
  border: 1px solid var(--card-border);
}

.actions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.action-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
  white-space: nowrap;
  margin-right: 8px;
}

.action-bubble {
  background: var(--white);
  color: var(--text-primary);
  padding: 6px 10px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;
  border: 1px solid var(--card-border);
  transition: all 0.2s ease;
}

.action-bubble:hover {
  background: var(--green-light);
  border-color: var(--green-main);
}

.no-actions {
  color: var(--text-secondary);
  font-style: italic;
  font-size: 12px;
  padding: 8px 0;
}

.last-triggered {
  font-size: 12px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px dashed var(--card-border);
}

.timestamp {
  font-weight: 500;
  color: var(--text-primary);
}

/* Rule actions positioning */
.rule-actions {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
  align-items: center;
  min-width: 96px;
  justify-content: flex-end;
  z-index: 2;
  position: absolute;
  top: 24px;
  right: 28px;
}

.rule-actions button[mat-icon-button] {
  width: 32px;
  height: 32px;
  margin: 0;
  padding: 0 !important;
  transition: all 0.2s ease;
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  color: #374151 !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;

  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important;
}

.rule-actions button[mat-icon-button] mat-icon {
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
  line-height: 1 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
}

.rule-actions button[mat-icon-button]:hover:not(:disabled) {
  background: #f3f4f6 !important;
  border-color: #10b981 !important;
  color: #10b981 !important;
}

.rule-actions button[mat-icon-button]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.rule-actions button[mat-icon-button][color="primary"] {
  color: #10b981 !important;
}

.rule-actions button[mat-icon-button][color="primary"]:hover:not(:disabled) {
  background: #d1fae5 !important;
  border-color: #10b981 !important;
  color: #047857 !important;
}

.rule-actions button[mat-icon-button][color="warn"] {
  color: #dc2626 !important;
}

.rule-actions button[mat-icon-button][color="warn"]:hover:not(:disabled) {
  background: #fee2e2 !important;
  border-color: #dc2626 !important;
  color: #b91c1c !important;
}

.rule-actions button[mat-icon-button] .mat-button-wrapper {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important;
  padding: 0 !important;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.rule-item.simple-rule .rule-actions {
  top: 24px;
  right: 28px;
}

.rule-item.complex-rule .rule-actions {
  top: 24px;
  right: 28px;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.expanded-details {
  margin-top: 0;
  background: var(--background);
  border-top: 2px solid var(--green-main);
  overflow: hidden;
}

.tab-navigation {
  display: flex;
  background: var(--white);
  border-bottom: 1px solid var(--card-border);
}

.tab-button {
  flex: 1;
  padding: 16px 20px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
  position: relative;
}

.tab-button:hover {
  background: var(--background);
  color: var(--text-primary);
}

.tab-button.active {
  color: var(--green-main);
  background: var(--green-light);
  border-bottom-color: var(--green-main);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--green-main);
}

.tab-button mat-icon {
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
}

.tab-content {
  padding: 20px;
  background: var(--white);
}

.summary-tab {
  min-height: 300px;
}

.summary-viewer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--green-main);
}

.summary-header h4 {
  color: var(--green-main);
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.summary-content-display {
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  padding: 24px;
  position: relative;
  overflow: hidden;
}

.summary-content-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #06b6d4, #10b981);
}

.summary-text-content {
  font-size: 15px;
  line-height: 1.7;
  color: #334155;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.summary-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 20px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 2px dashed #cbd5e1;
  color: var(--text-secondary);
}

.summary-empty-state .empty-icon {
  font-size: 64px !important;
  width: 64px !important;
  height: 64px !important;
  color: #94a3b8;
  margin-bottom: 16px;
  opacity: 0.6;
}

.summary-empty-state h5 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.summary-empty-state p {
  font-size: 14px;
  line-height: 1.6;
  max-width: 400px;
  margin: 0;
  color: var(--text-secondary);
}

.summary-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  background: var(--background);
  border-radius: 6px;
  border: 1px solid var(--card-border);
}

.summary-info .info-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 13px;
  color: var(--text-secondary);
}

.summary-info .info-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
  color: var(--text-secondary);
  flex-shrink: 0;
  margin-top: 1px;
}

.hierarchy h4 {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--green-main);
  color: var(--green-main);
  font-size: 16px;
  font-weight: 600;
}

.client-node, .site-node, .location-node, .controller-node {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--card-border);
  background: var(--white);
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.client-node:hover, .site-node:hover, .location-node:hover, .controller-node:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.site-node {
  margin-left: 50px;
}

.location-node {
  margin-left: 40px;
}

.controller-node {
  margin-left: 60px;
}

.node-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: var(--background);
}

.controller-header {
  cursor: pointer;
  transition: all 0.2s ease;
}

.controller-header:hover {
  background: var(--green-light);
}

.controller-header.selected {
  background: var(--green-main);
  color: var(--white);
}

.controller-header.selected .controller-icon,
.controller-header.selected span,
.controller-header.selected .arrow-icon {
   color: var(--white) !important;
}

.node-header mat-icon {
  font-size: 20px !important;
  width: 20px !important;
  height: 20px !important;
  color: var(--green-main);
  flex-shrink: 0;
}

.arrow-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
  color: var(--text-secondary);
  transition: transform 0.2s ease;
  margin-left: auto;
}

.controller-header.selected .arrow-icon {
  transform: rotate(180deg);
}

.controller-icon.status-online {
  color: var(--success) !important;
}

.controller-icon.status-offline {
  color: var(--danger) !important;
}

.controller-icon.status-warning {
  color: var(--warning) !important;
}

.controller-icon.status-unknown {
  color: var(--text-secondary) !important;
}

.controller-details-panel {
  padding: 16px;
  background: var(--white);
  border-top: 1px solid var(--card-border);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-row {
  font-size: 14px;
  color: var(--text-primary);
  padding: 8px 0;
  border-bottom: 1px dashed var(--card-border);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row strong {
  color: var(--card-title);
  margin-right: 8px;
}

.controller-details-panel h5 {
  font-size: 14px;
  font-weight: 600;
  color: var(--green-main);
  margin-top: 16px;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: var(--green-light);
  border-radius: 6px;
  border-left: 4px solid var(--green-main);
}

.performance-chart {
  background: #f8fafc;
  border-radius: 16px;
  border: 1.5px solid #e2e8f0;
  padding: 32px 24px 24px 24px;
  margin-top: 16px;
  min-width: 0;
  overflow-x: auto;
  box-shadow: 0 2px 12px rgba(16, 185, 129, 0.06);
}

.empty-message {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
  font-style: italic;
  background: var(--background);
  border-radius: 8px;
  margin-top: 20px;
  border: 2px dashed var(--card-border);
}

.applications-table {
  background: var(--white);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--card-border);
  margin-top: 16px;
}

.applications-table.empty-state {
  padding: 40px 20px;
  text-align: center;
  background: var(--background);
}

.applications-table.empty-state::before {
  content: '📱';
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.3;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 12px;
  padding: 12px 16px;
  background: var(--background);
  font-weight: 600;
  font-size: 12px;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--card-border);
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid var(--card-border);
  align-items: center;
  font-size: 13px;
  transition: background-color 0.2s ease;
}

/* Enhanced table styling for executions */
.table-row:nth-child(even) {
  background: rgba(0, 0, 0, 0.02);
}

.table-row:hover {
  background: var(--green-light) !important;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row mat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

.rawdata-tab {
  min-height: 400px;
}

.rawdata-viewer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.rawdata-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--green-main);
}

.rawdata-header h4 {
  color: var(--green-main);
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.rawdata-actions {
  display: flex;
  gap: 8px;
}

.copy-button, .download-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  font-size: 13px;
  color: var(--text-primary);
  border: 1px solid var(--card-border);
  border-radius: 6px;
  background: var(--white);
  transition: all 0.2s ease;
}

.copy-button:hover, .download-button:hover {
  border-color: var(--green-main);
  color: var(--green-main);
  background: var(--green-light);
  transform: translateY(-1px);
}

.copy-button mat-icon, .download-button mat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

.json-viewer {
  background: #1e1e1e;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--card-border);
  max-height: 500px;
  overflow-y: auto;
}

.json-content {
  margin: 0;
  padding: 20px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #d4d4d4;
  background: transparent;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.rawdata-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  background: var(--background);
  border-radius: 6px;
  border: 1px solid var(--card-border);
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 13px;
  color: var(--text-secondary);
}

.info-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
  color: var(--text-secondary);
  flex-shrink: 0;
  margin-top: 1px;
}

.search-loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px 20px;
  color: var(--text-secondary);
  font-size: 16px;
  background: var(--background);
  border-radius: 8px;
  margin-top: 20px;
}

.search-loading-message .loading-icon {
  font-size: 24px !important;
  width: 24px !important;
  height: 24px !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .rules-list {
    gap: 24px; /* Reduced gap for mobile */
    padding: 16px 4px;
  }

  .rule-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding-right: 20px !important;
  }

  .rule-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .rule-item.simple-rule .rule-content,
  .rule-item.complex-rule .rule-content {
    padding: 16px;
  }

  .usage-stats {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }

  .site-node, .location-node, .controller-node {
    margin-left: 10px;
  }

  .tab-navigation {
    flex-direction: column;
  }

  .tab-button {
    flex: none;
    padding: 12px 16px;
  }

  .tab-content {
    padding: 16px;
  }

  .summary-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .rawdata-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .rawdata-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .json-viewer {
    max-height: 300px;
  }

  .chart-grid {
    gap: 4px;
    min-height: 150px;
  }

  .chart-bar-column {
    height: 120px;
    min-width: 24px;
  }

  .bar-container {
    height: 100px;
    width: 16px;
  }

  .bar-label {
    font-size: 8px;
    width: 30px;
  }

  .table-header, .table-row {
    grid-template-columns: 1fr;
    gap: 4px;
  }

  .chart-summary {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .rule-actions {
    position: static !important;
    align-self: flex-end;
    margin-left: 0;
    margin-top: 10px;
    transform: none !important;
    top: auto !important;
    right: auto !important;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 18px;
  }

  .rule-name {
    font-size: 16px;
  }

  .chart-bar-column {
    height: 100px;
    min-width: 20px;
  }

  .bar-container {
    height: 80px;
    width: 14px;
  }

  .bar-label {
    font-size: 7px;
    width: 25px;
  }

  .summary-stat .stat-value {
    font-size: 14px;
  }

  .summary-stat .stat-label {
    font-size: 10px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .loading-icon,
  button,
  .rule-item,
  .bar-container,
  .summary-stat {
    animation: none !important;
    transition: none !important;
  }
  
  button:hover,
  .rule-item:hover,
  .bar-container:hover {
    transform: none !important;
  }
}

@media (prefers-contrast: high) {
  .rule-item {
    border: 2px solid currentColor;
  }
  
  .chart-bar-column .bar-container {
    border: 2px solid currentColor;
  }
  
  .success-bar {
    background: #008000 !important;
  }
  
  .failure-bar {
    background: #ff0000 !important;
  }
}

/* Print styles */
@media print {
  .rule-actions,
  .tab-navigation,
  .rawdata-actions {
    display: none !important;
  }
  
  .rule-item {
    box-shadow: none !important;
    border: 1px solid #000 !important;
    break-inside: avoid;
    margin-bottom: 20px;
  }
  
  .chart-grid {
    background: white !important;
  }
}

/* Dark mode support (if your app supports it) */
@media (prefers-color-scheme: dark) {
  .json-viewer {
    background: #0d1117;
  }
  
  .json-content {
    color: #c9d1d9;
  }
  
  .summary-content-display {
    background: #161b22;
    border-color: #30363d;
  }
  
  .summary-text-content {
    color: #c9d1d9;
  }
}

.executions-pagination-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
  padding: 8px 0 0 0;
  border-top: 1px solid var(--card-border);
  font-size: 13px;
  color: var(--text-secondary);
}

.performance-chart {
  background: #f8fafc;
  border-radius: 16px;
  border: 1.5px solid #e2e8f0;
  padding: 32px 24px 24px 24px;
  margin-top: 16px;
  min-width: 0;
  overflow-x: auto;
  box-shadow: 0 2px 12px rgba(16, 185, 129, 0.06);
}

.empty-message {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
  font-style: italic;
  background: var(--background);
  border-radius: 8px;
  margin-top: 20px;
  border: 2px dashed var(--card-border);
}