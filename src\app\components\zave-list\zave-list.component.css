/* Z-Wave MQTT Component Styles */
.zwave-container {
  font-family: 'Montserrat', 'Lato', sans-serif;
  max-width: 98%;
  margin-left: 50px;
  padding: 24px;
  box-sizing: border-box;
}

/* Header Styles */
.header {
  background: var(--surface);
  padding: 24px;
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  margin-bottom: 24px;
  animation: fadeIn 0.3s ease-out;
}

.connection-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
}

.status-indicator.connected {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.status-indicator.connecting {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.status-indicator.disconnected {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.status-dot {
  width: 8px !important;
  height: 8px !important;
  font-size: 8px !important;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* MQTT Connection Controls */
.mqtt-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.mqtt-controls .btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 13px;
  min-height: auto;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger);
  border-radius: 8px;
  font-size: 14px;
}

.controller-info {
  border-top: 1px solid var(--beige-darl);
  padding-top: 20px;
}

.controller-info h2 {
  margin: 0 0 12px 0;
  color: var(--text-primary);
  font-size: 24px;
  font-weight: 600;
}

.controller-info p {
  margin: 4px 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.mqtt-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--primary) !important;
  font-weight: 500;
  margin-top: 8px;
}

.info-icon {
  font-size: 16px !important;
}

/* Connection Help Section */
.connection-help {
  background: var(--surface);
  padding: 24px;
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  margin-bottom: 24px;
  animation: slideIn 0.3s ease-out;
}

.help-card {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.help-icon {
  font-size: 48px !important;
  color: var(--primary);
  margin-bottom: 16px;
}

.help-card h3 {
  color: var(--text-primary);
  margin: 0 0 16px 0;
  font-size: 20px;
}

.help-card p {
  color: var(--text-secondary);
  margin: 0 0 16px 0;
  line-height: 1.6;
}

.help-card ul {
  text-align: left;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
  padding-left: 24px;
}

.help-card li {
  margin-bottom: 8px;
}

/* Management Controls */
.management-controls {
  background: var(--surface);
  padding: 24px;
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  margin-bottom: 24px;
  animation: slideIn 0.3s ease-out;
}

.control-group h3 {
  margin: 0 0 16px 0;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
}

.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-height: 44px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.btn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-primary {
  background: var(--primary);
  color: white;
}

.btn-primary:not(:disabled):hover {
  background: var(--primary-dark);
}

.btn-secondary {
  background: var(--secondary);
  color: white;
}

.btn-warning {
  background: var(--warning);
  color: white;
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--primary);
  color: var(--primary);
}

.btn-outline:not(:disabled):hover {
  background: var(--primary);
  color: white;
}

.btn-icon {
  font-size: 16px !important;
}

.inclusion-status {
  margin-top: 20px;
  padding: 20px;
  background: var(--green-light);
  border-radius: 8px;
  border-left: 4px solid var(--primary);
}

.inclusion-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--beige-darl);
  border-top: 2px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.inclusion-help {
  margin: 0;
  color: var(--green-dark);
  font-size: 14px;
  line-height: 1.5;
}

/* Filters */
.filters {
  background: var(--surface);
  padding: 20px;
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  margin-bottom: 24px;
  animation: slideIn 0.4s ease-out;
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 40px;
  border: 2px solid var(--beige-darl);
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 18px !important;
}

.filter-select {
  padding: 12px 16px;
  border: 2px solid var(--beige-darl);
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary);
}

/* Device List */
.device-list {
  animation: fadeIn 0.5s ease-out;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: var(--surface);
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
}

.empty-icon {
  font-size: 64px !important;
  margin-bottom: 20px;
  opacity: 0.5;
  color: var(--text-secondary);
}

.empty-state h3 {
  margin: 0 0 12px 0;
  color: var(--text-primary);
  font-size: 24px;
  font-weight: 600;
}

.empty-state p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 16px;
}

.device-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
}

.device-card {
  background: var(--surface);
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  padding: 24px;
  transition: all 0.2s ease;
  border-left: 4px solid var(--primary);
  animation: cardEntrance 0.5s ease-out;
  position: relative;
}

.device-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.device-card.offline {
  border-left-color: var(--danger);
  opacity: 0.7;
}

.device-card.sleeping {
  border-left-color: var(--warning);
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 16px;
}

.device-info {
  flex: 1;
}

.device-name {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
}

.device-type {
  display: inline-block;
  padding: 4px 8px;
  background: var(--primary-lighter);
  color: var(--primary-dark);
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
}

.node-id {
  color: var(--text-secondary);
  font-size: 12px;
  margin-right: 8px;
}

/* MQTT Indicator */
.mqtt-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background: rgba(34, 139, 34, 0.1);
  color: #228B22;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mqtt-icon {
  font-size: 12px !important;
}

.device-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.alive, .status-badge.Alive {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.status-badge.asleep, .status-badge.Asleep {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.status-badge.dead, .status-badge.Dead {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.status-badge.unknown, .status-badge.Unknown {
  background: rgba(100, 116, 139, 0.1);
  color: var(--secondary);
}

.battery {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-secondary);
}

.battery-icon {
  font-size: 16px !important;
}

.battery-icon.low {
  color: var(--danger);
}

.device-location {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: var(--text-secondary);
  font-size: 14px;
}

.location-icon {
  font-size: 16px !important;
}

/* Quick Controls */
.quick-controls {
  margin: 20px 0;
  padding: 16px;
  background: var(--beige-light);
  border-radius: 8px;
}

.control-group {
  margin-bottom: 16px;
}

.control-group:last-child {
  margin-bottom: 0;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: var(--surface);
  border: 2px solid var(--beige-darl);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  width: 100%;
}

.control-btn:hover:not(:disabled) {
  border-color: var(--primary);
  transform: translateY(-1px);
}

.control-btn.active {
  background: var(--primary);
  border-color: var(--primary);
  color: white;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-icon {
  font-size: 16px !important;
}

.dimmer-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.dimmer-slider {
  width: 100%;
  margin-bottom: 8px;
  accent-color: var(--primary);
}

.dimmer-value {
  font-size: 12px;
  color: var(--text-secondary);
}

/* Sensor Values */
.sensor-values {
  margin: 20px 0;
}

.sensor-values h5 {
  margin: 0 0 12px 0;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 600;
}

.sensor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.sensor-item {
  padding: 12px;
  background: var(--beige-light);
  border-radius: 6px;
  text-align: center;
}

.sensor-label {
  display: block;
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.sensor-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.sensor-unit {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: normal;
}

/* Device Actions */
.device-actions {
  display: flex;
  gap: 8px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: var(--surface);
  border: 1px solid var(--beige-darl);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  color: var(--text-secondary);
  flex: 1;
  justify-content: center;
}

.action-btn:hover:not(:disabled) {
  border-color: var(--primary);
  color: var(--primary);
  transform: translateY(-1px);
}

.action-btn.danger {
  border-color: var(--danger);
  color: var(--danger);
}

.action-btn.danger:hover:not(:disabled) {
  background: var(--danger);
  color: white;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-icon {
  font-size: 14px !important;
}

.last-seen {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--beige-darl);
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;
}

/* MQTT Status Footer */
.mqtt-status {
  background: var(--surface);
  padding: 16px 24px;
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  margin-top: 24px;
  animation: fadeIn 0.6s ease-out;
}

.status-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: var(--primary);
  font-size: 14px;
  font-weight: 500;
}

.status-info .mqtt-icon {
  font-size: 16px !important;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes cardEntrance {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .device-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (max-width: 768px) {
  .zwave-container {
    margin-left: 0;
    padding: 16px;
  }
  
  .header,
  .management-controls,
  .filters,
  .connection-help {
    padding: 16px;
  }
  
  .device-card {
    padding: 16px;
  }
  
  .device-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    min-width: unset;
  }
  
  .button-group {
    justify-content: center;
  }
  
  .device-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .device-status {
    flex-direction: row;
    align-items: center;
    align-self: stretch;
    justify-content: space-between;
  }

  .mqtt-controls {
    flex-direction: column;
    width: 100%;
  }

  .mqtt-controls .btn {
    width: 100%;
    justify-content: center;
  }

  .connection-status {
    flex-direction: column;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .zwave-container {
    padding: 12px;
  }
  
  .btn {
    font-size: 12px;
    padding: 10px 16px;
  }
  
  .device-actions {
    justify-content: center;
  }
  
  .sensor-grid {
    grid-template-columns: 1fr 1fr;
  }

  .action-btn {
    flex: none;
    min-width: 70px;
  }
}