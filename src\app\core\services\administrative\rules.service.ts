import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ApiService } from '../api.service';
import { Rules } from '@app/core/models/rules';
import { RuleComprehensive } from '@app/shared/models/rule/RuleComprehensive';
import { RuleClientHierarchy } from '@app/shared/models/rule/RuleClientHierarchy';
import { PagedResponse } from '@app/shared/models/rule/PagedResponse';
import { Lister } from '@app/shared/models/rule/Lister';
import { WhereParams } from '@app/shared/models/rule/WhereParams';
import { Sorting } from '@app/shared/models/rule/Sorting';
import { Observable } from 'rxjs';
import { RuleTransactionDetail } from '@app/shared/models/rule/RuleTransactionDetail';
import { RuleWithAiRes } from '@app/shared/models/rule/RuleWithAiRes';
import { tap, catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';

// --- New Interfaces for API Requests ---

/**
 * Request body for publishing a rule.
 */
export interface RulePublishRequest {
  ruleId: string; // Changed from Guid to string for Angular/TypeScript
  controllerId: string; // Changed from Guid to string
  localId: string; // Changed from Guid to string
  rawData: any; // Can be any JSON object or string
}

/**
 * Request body for deleting a rule.
 */
export interface RuleDeleteRequest {
  ruleId: string; // Changed from Guid to string
  controllerId: string; // Changed from Guid to string
}

/**
 * Request body for activating/deactivating a tag.
 */
export interface TagActivationRequest {
  tagId: string; // Changed from Guid to string
  controllerIds: string[]; // Changed from Guid[] to string[]
  isActive: boolean;
}

// --- Existing Interfaces ---

export interface AdvancedRulesRequest {
  page?: number;
  pageSize?: number;
  sortParams?: Sorting[];
  filterParams?: WhereParams[];
}

export interface AdvancedSearchRequest {
  searchTerm: string;
  includeInactive?: boolean;
  page?: number;
  pageSize?: number;
  sortParams?: Sorting[];
  filterParams?: WhereParams[];
}

export interface AdvancedExecutionsRequest {
  page?: number;
  pageSize?: number;
  sortParams?: Sorting[];
  filterParams?: WhereParams[];
}

export interface RuleExecutionChartData {
  ExecutionDate: string;
  SuccessfulExecutions: number;
  FailedExecutions: number;
  TotalExecutions: number;
}

export interface RuleExecutionSimple {
  ExecutionTimestamp: string;
  IsSuccess: boolean;
  ControllerId: string;
}

@Injectable({ providedIn: 'root' })
export class RulesApiService extends ApiService<Rules> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('rule'); // Sets base endpoint to 'api/rule'
  }

  apiUrl = this.getFullUrl(); // This will be something like 'api/rule/'

  /**
   * Retrieves all rules with comprehensive statistics and pagination.
   * Uses GET method with query parameters - supports simple filtering and sorting
   */
  getRulesComprehensive(
    lister: Lister<RuleComprehensive>
  ): Observable<PagedResponse<RuleComprehensive[]>> {
    let params = new HttpParams();

    // Basic pagination
    params = params.set('page', lister.Pagination.CurrentPage.toString());
    params = params.set('pageSize', lister.Pagination.PageSize.toString());

    // Multi-column sorting support
    if (lister.SortParams && lister.SortParams.length > 0) {
      const sortColumns = lister.SortParams.map(s => s.Column).join(',');
      const sortDirections = lister.SortParams.map(s => s.Sort).join(',');
      params = params.set('sortBy', sortColumns);
      params = params.set('sortDirection', sortDirections);
    }

    // Simple filters as JSON string
    if (lister.FilterParams && lister.FilterParams.length > 0) {
      params = params.set('filters', JSON.stringify(lister.FilterParams));
    }

    console.log('🚀 Service: Sending GET params to backend:', params.toString());

    return this.http.get<PagedResponse<RuleComprehensive[]>>(
      `${this.apiUrl}rule/comprehensive`, // Corrected endpoint to be relative to base 'rule'
      { params: params }
    );
  }

  /**
   * Advanced rules retrieval with complex filtering and sorting using POST
   */
  getRulesComprehensiveAdvanced(
    request: AdvancedRulesRequest
  ): Observable<PagedResponse<RuleComprehensive[]>> {
    console.log('🚀 Service: Sending advanced rules request:', request);

    return this.http.post<PagedResponse<RuleComprehensive[]>>(
      `${this.apiUrl}rule/comprehensive/advanced`, // Corrected endpoint
      request
    );
  }

  /**
   * Searches for rules based on a search term - supports simple filtering
   */
  searchRules(
    searchTerm: string,
    lister: Lister<RuleComprehensive>
  ): Observable<PagedResponse<RuleComprehensive[]>> {
    let params = new HttpParams();

    params = params.set('searchTerm', searchTerm);
    params = params.set('page', lister.Pagination.CurrentPage.toString());
    params = params.set('pageSize', lister.Pagination.PageSize.toString());
    params = params.set('includeInactive', 'false');

    // Multi-column sorting support
    if (lister.SortParams && lister.SortParams.length > 0) {
      const sortColumns = lister.SortParams.map(s => s.Column).join(',');
      const sortDirections = lister.SortParams.map(s => s.Sort).join(',');
      params = params.set('sortBy', sortColumns);
      params = params.set('sortDirection', sortDirections);
    }

    // Simple filters as JSON string
    if (lister.FilterParams && lister.FilterParams.length > 0) {
      params = params.set('filters', JSON.stringify(lister.FilterParams));
    }

    console.log('🔍 Service: Sending search GET params:', params.toString());

    return this.http.get<PagedResponse<RuleComprehensive[]>>(
      `${this.apiUrl}rule/search`, // Corrected endpoint
      { params: params }
    );
  }

  /**
   * Advanced search with complex filtering using POST
   */
  searchRulesAdvanced(
    request: AdvancedSearchRequest
  ): Observable<PagedResponse<RuleComprehensive[]>> {
    console.log('🔍 Service: Sending advanced search request:', request);

    return this.http.post<PagedResponse<RuleComprehensive[]>>(
      `${this.apiUrl}rule/search/advanced`, // Corrected endpoint
      request
    );
  }

  /**
   * Retrieves comprehensive details for a specific rule by its ID, including statistics and tags.
   */
  getRuleComprehensiveById(
    ruleId: string
  ): Observable<RuleComprehensive | null> {
    return this.http.get<RuleComprehensive | null>(
      `${this.apiUrl}rule/${ruleId}/comprehensive` // Corrected endpoint
    );
  }

  /**
   * Retrieves the client, site, and local hierarchy associated with a specific rule.
   */
  getRuleClientHierarchy(ruleId: string): Observable<RuleClientHierarchy[]> {
    return this.http.get<RuleClientHierarchy[]>(
      `${this.apiUrl}rule/${ruleId}/hierarchy` // Corrected endpoint
    );
  }

  /**
   * Retrieves transaction details for a specific rule (includes controller information).
   */
  getRuleTransactionDetails(ruleId: string): Observable<RuleTransactionDetail[]> {
    console.log('🔄 Service: Fetching transaction details for rule:', ruleId);
    return this.http.get<RuleTransactionDetail[]>(
      `${this.apiUrl}rule/${ruleId}/transaction-details` // Corrected endpoint
    );
  }

  /**
   * Creates a rule using AI generation
   */
  createRuleWithAi(prompt: string): Observable<RuleWithAiRes> {
    const createRuleUrl = `${this.apiUrl}text-summarization/rule-with-ai`; // Adjusting for text-summarization endpoint
    
    const requestBody = {
      prompt: prompt.trim()
    };

    console.log('🤖 Service: Sending AI rule creation request:', requestBody);
    return this.http.post<RuleWithAiRes>(createRuleUrl, requestBody);
  }

  /**
   * Summarizes a rule using AI
   */
  summarizeRule(rule: any): Observable<String> {
    const summarizationUrl = `${this.apiUrl}text-summarization/summarize`; // Adjusting for text-summarization endpoint
    return this.http.post<String>(summarizationUrl, rule);
  }

  /**
   * Gets rule execution chart data grouped by date for chart visualization
   * Updated to handle improved status detection from backend JSON parsing
   */
  getRuleExecutionChart(
    ruleId: string,
    days: number = 30
  ): Observable<RuleExecutionChartData[]> {
    let params = new HttpParams();
    params = params.set('days', days.toString());

    console.log('📊 Service: Fetching execution chart data for rule:', ruleId, 'days:', days);
    
    return this.http.get<RuleExecutionChartData[]>(
      `${this.apiUrl}rule/${ruleId}/execution-chart`, // Corrected endpoint
      { params: params }
    ).pipe(
      tap(response => {
        console.log('📊 Service: Chart data response:', response);
        // Log any potential data inconsistencies for debugging
        response.forEach(item => {
          if (item.SuccessfulExecutions + item.FailedExecutions !== item.TotalExecutions) {
            console.warn('⚠️ Data inconsistency detected for date:', item.ExecutionDate, 
                          'Success + Failed ≠ Total', item);
          }
        });
      }),
      catchError(error => {
        console.error('❌ Error fetching execution chart data:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Gets hourly execution data for a specific date for detailed chart view
   */
  getRuleExecutionHourlyChart(
    ruleId: string,
    date: Date
  ): Observable<RuleExecutionChartData[]> {
    let params = new HttpParams();
    params = params.set('date', date.toISOString().split('T')[0]); // Format as YYYY-MM-DD

    console.log('⏰ Service: Fetching hourly execution data for rule:', ruleId, 'date:', date);
    
    return this.http.get<RuleExecutionChartData[]>(
      `${this.apiUrl}rule/${ruleId}/execution-chart/hourly`,
      { params: params }
    ).pipe(
      tap(response => {
        console.log('⏰ Service: Hourly chart data response:', response);
      }),
      catchError(error => {
        console.error('❌ Error fetching hourly execution chart data:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Gets recent individual executions with pagination (page size of 5)
   */
  getRecentRuleExecutions(
    ruleId: string,
    page: number = 1
  ): Observable<PagedResponse<RuleExecutionSimple[]>> {
    let params = new HttpParams();
    params = params.set('page', page.toString());
    params = params.set('pageSize', '5'); // Fixed page size of 5
    params = params.set('sortBy', 'ExecutionTimestamp');
    params = params.set('sortDirection', 'DESC'); // Most recent first

    console.log('🔍 Service: Fetching paginated recent executions for rule:', ruleId, 'page:', page);

    return this.http.get<PagedResponse<RuleExecutionSimple[]>>(
      `${this.apiUrl}rule/${ruleId}/recent-executions/paginated`, // Corrected endpoint
      { params: params }
    ).pipe(
      tap(response => console.log('🔍 Service: Response:', response))
    );
  }

  /**
   * Gets recent individual executions with custom pagination parameters
   */
  getRecentRuleExecutionsWithPagination(
    ruleId: string,
    page: number = 1,
    pageSize: number = 5,
    sortBy: string = 'ExecutionTimestamp',
    sortDirection: string = 'DESC',
    filters?: WhereParams[]
  ): Observable<PagedResponse<RuleExecutionSimple[]>> {
    let params = new HttpParams();
    params = params.set('page', page.toString());
    params = params.set('pageSize', pageSize.toString());
    params = params.set('sortBy', sortBy);
    params = params.set('sortDirection', sortDirection);

    if (filters && filters.length > 0) {
      params = params.set('filters', JSON.stringify(filters));
    }

    console.log('🔍 Service: Fetching custom paginated recent executions for rule:', ruleId, 
                'page:', page, 'pageSize:', pageSize);
    
    return this.http.get<PagedResponse<RuleExecutionSimple[]>>(
      `${this.apiUrl}rule/${ruleId}/recent-executions/paginated`, // Corrected endpoint
      { params: params }
    );
  }

  /**
   * Gets recent individual executions with advanced filtering using POST
   */
  getRecentRuleExecutionsAdvanced(
    ruleId: string,
    request: AdvancedExecutionsRequest
  ): Observable<PagedResponse<RuleExecutionSimple[]>> {
    console.log('🔍 Service: Sending advanced executions request for rule:', ruleId, request);

    return this.http.post<PagedResponse<RuleExecutionSimple[]>>(
      `${this.apiUrl}rule/${ruleId}/recent-executions/advanced`, // Corrected endpoint
      request
    );
  }

  // --- Rule Publishing, Deletion, and Tag Activation ---

  /**
   * Publishes a rule to an MQTT topic.
   * @param request The RulePublishRequest containing rule details.
   */
  publishRule(request: RulePublishRequest): Observable<any> {
    console.log('📤 Service: Publishing rule:', request);
    return this.http.post<any>(`${this.apiUrl}rule/publish`, request).pipe(
      tap(response => console.log('✅ Rule publish response:', response)),
      catchError(error => {
        console.error('❌ Error publishing rule:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Publishes a rule deletion command to an MQTT topic.
   * @param request The RuleDeleteRequest containing rule and controller IDs.
   */
  deleteRule(request: RuleDeleteRequest): Observable<any> {
    console.log('🗑️ Service: Deleting rule:', request);
    return this.http.post<any>(`${this.apiUrl}rule/delete`, request).pipe(
      tap(response => console.log('✅ Rule delete response:', response)),
      catchError(error => {
        console.error('❌ Error deleting rule:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Activates or deactivates rules associated with a specific tag on given controllers.
   * @param request The TagActivationRequest containing tag ID, controller IDs, and activation status.
   */
  activateTag(request: TagActivationRequest): Observable<any> {
    console.log('⚡ Service: Activating/Deactivating tag:', request);
    return this.http.post<any>(`${this.apiUrl}rule/tags/activate`, request).pipe(
      tap(response => console.log('✅ Tag activation response:', response)),
      catchError(error => {
        console.error('❌ Error activating/deactivating tag:', error);
        return throwError(() => error);
      })
    );
  }
}
