* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #f0f8f0 0%, #e8f5e8 100%);
  color: #2d4a2d;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #22c55e;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #16a34a;
  margin: 0;
}

.page-subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin-top: 4px;
}

.add-licence-btn {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.btn:hover::before {
  left: 100%;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s;
}

.add-licence-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(34, 197, 94, 0.4);
}

.add-licence-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.licenses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 30px;
}

.license-card {
  background: white;
  border-radius: 16px;
  padding: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.license-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.license-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

.license-header {
  padding: 20px 20px 16px;
  border-bottom: 1px solid #f1f5f9;
}

.license-info {
  margin-bottom: 16px;
}

.license-name {
  font-size: 1.375rem;
  font-weight: 700;
  color: #16a34a;
  margin: 0 0 8px 0;
}

.license-description {
  color: #64748b;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

.license-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.action-btn {
  background: none;
  border: none;
  padding: 8px 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1.1rem;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  transform: scale(1.1);
}

.action-btn.view:hover {
  background: #dbeafe;
  color: #2563eb;
}

.action-btn.edit:hover {
  background: #fef3c7;
  color: #d97706;
}

.action-btn.delete:hover {
  background: #fee2e2;
  color: #dc2626;
}

.action-btn.refresh:hover {
  background: #ecfdf5;
  color: #16a34a;
}

.license-content {
  padding: 16px 20px;
}

.license-options {
  margin-bottom: 16px;
}

.option-type-select {
  font-weight: 600;
  color: #2563eb;
}

.option-type-select.max-controllers {
  color: #d97706;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.options-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.options-count {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #16a34a;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid #86efac;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.modal-title mat-icon {
  font-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
  color: #16a34a;
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  border-radius: 10px;
  padding: 8px;
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
}

.option-tag {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
}

.option-tag:hover {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  border-color: #86efac;
}

.option-name {
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

.option-price {
  font-weight: 600;
  color: #16a34a;
  font-size: 0.9rem;
}

.no-options {
  text-align: center;
  padding: 20px;
  color: #9ca3af;
  font-style: italic;
  font-size: 0.9rem;
}

.no-options-icon {
  font-size: 1.5rem;
  margin-bottom: 8px;
  display: block;
}

.license-footer {
  padding: 16px 20px;
  background: #f8fafc;
  border-top: 1px solid #f1f5f9;
}

.license-stats {
  display: flex;
  gap: 20px;
  font-size: 0.85rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  color: #6b7280;
  font-weight: 500;
}

.stat-value {
  color: #16a34a;
  font-weight: 600;
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.empty-state-icon {
  font-size: 5rem;
  margin-bottom: 24px;
  opacity: 0.4;
}

.empty-state-title {
  font-size: 1.5rem;
  color: #6b7280;
  margin-bottom: 12px;
  font-weight: 600;
}

.empty-state-text {
  color: #9ca3af;
  font-size: 1rem;
  margin-bottom: 32px;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.empty-state-btn {
  padding: 16px 32px;
  font-size: 1.1rem;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}
/* Additional styles for modal components */

/* Modal close button */
.modal-close {
  position: absolute;
  top: 24px;
  right: 24px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  padding: 12px;
  border-radius: 12px;
  cursor: pointer;
  color: #64748b;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  z-index: 10;
}

.modal-close:hover {
  background: #fee2e2;
  color: #dc2626;
  border-color: #fecaca;
  transform: scale(1.05);
}

.modal-close mat-icon {
  font-size: 1.25rem;
  width: 1.25rem;
  height: 1.25rem;
}

/* Modal loading state */
.modal-loading {
  position: absolute;
  bottom: 16px;
  left: 32px;
  right: 80px; /* Make room for close button */
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.modal-loading .loading-spinner {
  width: 16px;
  height: 16px;
  border-width: 2px;
  border-color: #22c55e;
  border-top-color: transparent;
}

.modal-body-loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Move modal specific styles */
.move-modal .modal-header {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.move-modal .modal-header::before {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
}

.move-modal .modal-title {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.move-modal .modal-title mat-icon {
  color: #d97706;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
}

.move-description {
  font-size: 1.1rem;
  color: #374151;
  margin-bottom: 24px;
}

.move-details {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
}

.move-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.move-detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 600;
  color: #6b7280;
}

.detail-value {
  font-weight: 600;
  color: #16a34a;
}

.move-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.move-option {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.2s ease;
}

.move-option:hover {
  border-color: #22c55e;
  background: #f0fdf4;
}

.move-option h4 {
  color: #16a34a;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.move-option p {
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Loading states */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content {
  text-align: center;
}

.loading-spinner-large {
  display: inline-block;
  width: 48px;
  height: 48px;
  border: 4px solid #e5e7eb;
  border-radius: 50%;
  border-top-color: #22c55e;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
}

.loading-text {
  color: #6b7280;
  font-size: 1.1rem;
  font-weight: 500;
}

/* Card loading overlay */
.card-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
}

.card-refreshing {
  opacity: 0.7;
}

/* Drag and drop states */
.drag-over {
  border-color: #22c55e !important;
  background: #f0fdf4 !important;
}

.dragging {
  opacity: 0.5;
  transform: rotate(2deg);
}

.drop-hint {
  color: #16a34a;
  font-weight: 600;
}

/* Delete modal content loading */
.content-loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Option fields grid layout */
.option-fields {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.option-fields .form-group {
  margin-bottom: 16px;
}

/* Option disabled state */
.option-disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* Red text utility */
.text-red-600 {
  color: #dc2626;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .move-options {
    grid-template-columns: 1fr;
  }

  .option-fields {
    grid-template-columns: 1fr;
  }

  .modal-close {
    top: 12px;
    right: 12px;
  }

  .modal-loading {
    position: static;
    transform: none;
    margin-top: 12px;
  }
}

@media (max-width: 768px) {
  .modal-container {
    margin: 10px;
    border-radius: 16px;
  }

  .modal-header {
    padding: 20px 24px;
    min-height: 76px;
  }

  .modal-title {
    font-size: 1.5rem;
  }

  .modal-close {
    top: 18px;
    right: 18px;
    width: 40px;
    height: 40px;
    padding: 10px;
  }

  .modal-loading {
    bottom: 12px;
    left: 24px;
    right: 70px;
    padding: 6px 12px;
    font-size: 0.8rem;
  }

  .modal-body {
    padding: 24px;
  }

  .modal-actions {
    padding: 20px 24px;
    gap: 12px;
  }

  .btn {
    padding: 12px 20px;
    font-size: 0.9rem;
  }
}

.modal-container {
  background: white;
  border-radius: 20px;
  max-width: 700px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header {
  padding: 28px 32px;
  border-bottom: 1px solid #f1f5f9;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
  min-height: 88px; /* Fixed height to prevent layout shift */
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 50%, #059669 100%);
  border-radius: 20px 20px 0 0;
}

.modal-title {
  font-size: 1.75rem;
  font-weight: 800;
  background: linear-gradient(135deg, #16a34a 0%, #059669 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  letter-spacing: -0.025em;
}

.modal-body {
  padding: 32px;
  max-height: calc(90vh - 200px);
  overflow-y: auto;
}

.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 0.95rem;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  background: white;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #22c55e;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.readonly {
  background: #f9fafb;
  color: #6b7280;
}

.options-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.options-counter {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #16a34a;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 600;
  border: 1px solid #86efac;
}

.options-container {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.option-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.option-item:last-child {
  margin-bottom: 0;
}

.option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.option-header h4 {
  color: #16a34a;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.option-fields select.form-input {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg fill='none' stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
}

.option-fields select.form-input:focus {
  border-color: #22c55e;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.option-fields input[type="number"] {
  -moz-appearance: textfield;
}

.option-fields input[type="number"]::-webkit-inner-spin-button,
.option-fields input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.add-option-section {
  padding: 16px;
  text-align: center;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  margin-top: 16px;
}

.add-option-btn {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-option-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.icon-text {
  display: inline-flex;
  align-items: center;
  gap: 8px; /* space between icon and text */
}

.no-options-placeholder {
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
  font-style: italic;
}

.placeholder-icon {
  font-size: 2rem;
  margin-bottom: 8px;
  display: block;
  opacity: 0.5;
}

.placeholder-text {
  font-size: 0.9rem;
}

.btn {
  padding: 14px 24px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(34, 197, 94, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(34, 197, 94, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #475569;
  border: 1px solid #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-cancel {
  background: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.btn-cancel:hover {
  background: #f8fafc;
  color: #475569;
  transform: translateY(-1px);
}

.btn-remove {
  background: #fee2e2;
  color: #dc2626;
  padding: 6px 12px;
  font-size: 1.2rem;
  font-weight: bold;
  border-radius: 6px;
}

.btn-remove:hover {
  background: #fecaca;
}

.btn-danger {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(220, 38, 38, 0.3);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(220, 38, 38, 0.4);
}

.modal-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  padding: 24px 32px;
  border-top: 1px solid #f1f5f9;
  background: linear-gradient(135deg, #fafafa 0%, #f4f4f5 100%);
}

.delete-modal {
  max-width: 500px;
}

.delete-modal .modal-header {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
}

.delete-modal .modal-header::before {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
}

.delete-modal .modal-title {
  background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.delete-modal .modal-title mat-icon {
  color: #dc2626;
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
}

.delete-content {
  padding: 24px;
}

.delete-message {
  font-size: 1.1rem;
  color: #374151;
  margin-bottom: 20px;
}

.delete-details {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.delete-details p {
  margin-bottom: 8px;
  font-size: 0.95rem;
}

.delete-details p:last-child {
  margin-bottom: 0;
}

.delete-warning {
  color: #dc2626;
  font-weight: 600;
  font-size: 0.95rem;
}

.text-red-600 {
  color: #dc2626;
}

.delete-icon {
  margin-right: 8px;
}

@media (max-width: 768px) {
  .licenses-grid {
    grid-template-columns: 1fr;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .modal-container {
    margin: 10px;
  }

  .option-fields {
    grid-template-columns: 1fr;
  }

  .license-stats {
    flex-direction: column;
    gap: 8px;
  }
}
