<!-- src/app/clients/components/client-form/client-form.component.html -->
<ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
<div class="pop-up-container">
  <div class="form-header">
    <h2 class="form-title">
      <mat-icon class="title-icon">{{
        isEditMode ? "edit" : "add_circle_outline"
      }}</mat-icon>
      {{ isEditMode ? "Modifier Client" : "Créer un Client" }}
    </h2>
    <button class="close-button" (click)="cancel()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <div class="form-grid">
    <div
      class="validation-errors"
      *ngIf="showErrorMessages.length > 0"
      style="grid-column: 1 / -1"
    >
      <div
        class="alert alert-danger"
        [ngClass]="{ 'animated fadeIn': showErrorMessages }"
      >
        <div class="validation-errors-title">
          <mat-icon>error_outline</mat-icon>
          Erreurs de validation
        </div>
        <ul class="validation-errors-list">
          <li *ngFor="let error of showErrorMessages">
            <mat-icon>error</mat-icon>{{ error }}
          </li>
        </ul>
      </div>
    </div>
    <div class="form-group">
      <label for="idOrganisation"
        >Organisation<span class="required">*</span></label
      >
      <select
        id="idOrganisation"
        [(ngModel)]="clientData.IdOrganisation"
        [value]="clientData.IdOrganisation"
        class="form-select"
        required
      >
        <option value="">Sélectionnez une organisation</option>
        <option *ngFor="let org of data.organisations" [value]="org.Id">
          {{ org.Nom }}
        </option>
      </select>
    </div>

    <div class="form-group">
      <label for="raisonSociale"
        >Raison sociale<span class="required">*</span></label
      >
      <input
        id="raisonSociale"
        type="text"
        [(ngModel)]="clientData.Name"
        [value]="clientData.Name"
        required
      />
    </div>
    <div class="form-group">
      <label for="pays">Pays</label>
      <select
        id="pays"
        [(ngModel)]="clientData.Country"
        [value]="clientData.Country"
        class="form-select"
      >
        <option *ngFor="let country of countries" [value]="country">
          {{ country }}
        </option>
      </select>
    </div>
    <div class="form-group">
      <label for="region">Région</label>
      <input
        id="region"
        type="text"
        [(ngModel)]="clientData.Region"
        [value]="clientData.Region"
      />
    </div>
    <div class="form-group">
      <label for="ville">Ville</label>
      <input
        id="ville"
        type="text"
        [(ngModel)]="clientData.City"
        [value]="clientData.City"
      />
    </div>
    <div class="form-group">
      <label for="adresse">Adresse<span class="required">*</span></label>
      <input
        id="adresse"
        type="text"
        [(ngModel)]="clientData.Address"
        [value]="clientData.Address"
        required
      />
    </div>
    <div class="form-group">
      <label for="telephone">Téléphone<span class="required">*</span></label>
      <div class="phone-row" [class.focused]="isFocused">
        <ng-select
          [items]="datas"
          bindLabel="fullLabel"
          [(ngModel)]="selectedCountry"
          [clearable]="false"
          class="custom-ng-select"
        >
          <ng-template ng-option-tmp let-item="item">
            <span>{{ item.name }} ({{ item.dialCode }})</span>
          </ng-template>

          <ng-template ng-label-tmp let-item="item">
            <span>{{ item.code }}</span>
          </ng-template>
        </ng-select>
        <span class="dialCodePhone">{{ selectedCountry?.dialCode || "" }}</span>
        <input
          type="text"
          class="input-telephone"
          id="telephone"
          (focus)="isFocused = true"
          (blur)="isFocused = false"
          [placeholder]="selectedCountry?.nationalNumber"
          [(ngModel)]="clientData.PhoneNumber"
          [value]="clientData.PhoneNumber"
        />
        <!-- <select id="country" [(ngModel)]="selectedCountry" (change)=>
          <option *ngFor="let country of datas" [value]="country.code" onselect="assignPhoneNumber(country)">
            {{country.code}} {{ country.dialCode }}
          </option>
        </select>
        <input
          id="phone"
          type="tel"
          [(ngModel)]="clientData.PhoneNumber"
          (input)="onPhoneInput($event)"
          (blur)="onBlur()"
        /> -->
      </div>
      <!-- <label for="telephone">Téléphone<span class="required">*</span></label>
      <input
        id="telephone"
        type="tel"
        [(ngModel)]="clientData.PhoneNumber"
        [value]="clientData.PhoneNumber | phoneFormat"
        required
      /> -->
    </div>

    <div class="form-group">
      <label for="dateCreation"
        >Date de création d'entreprise<span class="required">*</span></label
      >
      <input id="dateCreation" type="date" [(ngModel)]="date" required />
    </div>
    <div class="form-group">
      <label for="formeJuridique"
        >Forme Juridique<span class="required">*</span></label
      >
      <select
        id="formeJuridique"
        [(ngModel)]="clientData.LegalForm"
        [value]="clientData.LegalForm"
        class="form-select"
        required
      >
        <option *ngFor="let forme of formesJuridiques" [value]="forme">
          {{ forme }}
        </option>
      </select>
    </div>

    <div class="form-group">
      <label for="secteurActivite"
        >Secteur d'activité<span class="required">*</span></label
      >
      <select
        id="secteurActivite"
        [(ngModel)]="clientData.BusinessSector"
        [value]="clientData.BusinessSector"
        required
      >
        <optgroup
          *ngFor="let secteur of secteursActivite"
          [label]="secteur.label"
        >
          <option *ngFor="let sous of secteur.sousSecteurs" [value]="sous.code">
            {{ sous.label }}
          </option>
        </optgroup>
      </select>
    </div>
    <div class="form-group">
      <label for="RC">RC<span class="required">*</span></label>
      <input
        id="RC"
        type="text"
        [(ngModel)]="clientData.RC"
        [value]="clientData.RC | numberSpacing"
        required
      />
    </div>
    <div class="form-group">
      <label for="IF">IF<span class="required">*</span></label>
      <input
        id="IF"
        type="text"
        [(ngModel)]="clientData.IF"
        [value]="clientData.IF | numberSpacing"
        required
      />
    </div>
    <div class="form-group">
      <label for="ICE">ICE<span class="required">*</span></label>
      <input
        id="ICE"
        type="text"
        [(ngModel)]="clientData.ICE"
        [value]="clientData.ICE | numberSpacing"
        required
      />
    </div>
    <div class="form-group">
      <label for="patente">Patente<span class="required">*</span></label>
      <input
        id="patente"
        type="text"
        [(ngModel)]="clientData.Patente"
        [value]="clientData.Patente | numberSpacing"
        required
      />
    </div>
    <div class="form-group">
      <label for="SIRET">SIRET<span class="required">*</span></label>
      <input
        id="SIRET"
        type="text"
        [value]="clientData.SIRET | numberSpacing : 'SIRET'"
        (input)="onSiretInput($any($event.target).value)"
        required
        pattern="^\d{14}$"
      />
    </div>
    <div class="form-group">
      <label for="SIREN">SIREN<span class="required">*</span></label>
      <input
        id="SIREN"
        type="text"
        [value]="clientData.SIREN | numberSpacing : 'SIREN'"
        (input)="onSirenInput($any($event.target).value)"
        required
        pattern="^\d{9}$"
      />
    </div>
    <div class="form-group">
      <label for="emailContact">Email du contact<span class="required">*</span></label>
      <input
        id="emailContact"
        type="text"
        [(ngModel)]="clientData.ContactEmail"
        [value]="clientData.ContactEmail"
      />
    </div>

    <div class="form1" style="grid-column: 1 / -1">
      <button
        class="show-more-button"
        (click)="showMoreFields = !showMoreFields"
      >
        <span>{{ showMoreFields ? "Afficher moins" : "Afficher plus" }}</span>
        <i class="material-icons">
          {{ showMoreFields ? "expand_less" : "expand_more" }}
        </i>
      </button>
    </div>

    <div class="form-group {{ showMoreFields ? '' : 'display-none' }}">
      <label for="marque">Marque</label>
      <input
        id="marque"
        type="text"
        [(ngModel)]="clientData.Brand"
        [value]="clientData.Brand"
      />
    </div>
    <div class="form-group {{ showMoreFields ? '' : 'display-none' }}">
      <label for="filiale">Filiale</label>
      <input
        id="filiale"
        type="text"
        [(ngModel)]="clientData.Filiale"
        [value]="clientData.Filiale"
      />
    </div>
    <div class="form-group {{ showMoreFields ? '' : 'display-none' }}">
      <label for="nomContact">Nom du contact</label>
      <input
        id="nomContact"
        type="text"
        [(ngModel)]="clientData.ContactName"
        [value]="clientData.ContactName"
      />
    </div>

    <div class="form-group {{ showMoreFields ? '' : 'display-none' }}">
      <label for="addressContact">Adresse du contact</label>
      <input
        id="addressContact"
        type="text"
        [(ngModel)]="clientData.ContactAddress"
        [value]="clientData.ContactAddress"
      />
    </div>
    <div class="form-group {{ showMoreFields ? '' : 'display-none' }}">
      <label for="typeEntreprise">Type d'entreprise</label>
      <select
        id="typeEntreprise"
        [(ngModel)]="clientData.CompanyType"
        class="form-select"
      >
        <optgroup label="Types d'entreprise (général)">
          <option value="commerciale">Entreprise commerciale</option>
          <option value="industrielle">Entreprise industrielle</option>
          <option value="artisanale">Entreprise artisanale</option>
          <option value="agricole">Entreprise agricole</option>
          <option value="services">Entreprise de services</option>
        </optgroup>

        <optgroup label="Secteurs spécifiques">
          <option value="transport">Entreprise de transport</option>
          <option value="btp">Entreprise de BTP</option>
          <option value="dev_info">
            Société de développement informatique
          </option>
          <option value="communication">Agence de communication</option>
          <option value="formation">Centre de formation</option>
          <option value="import_export">Société d'import/export</option>
          <option value="conseil">Société de conseil</option>
        </optgroup>

        <optgroup label="Modèles d'organisation">
          <option value="startup">Start-up</option>
          <option value="cooperative">Coopérative</option>
          <option value="franchise">Franchise</option>
          <option value="holding">Holding</option>
          <option value="ong">ONG (organisation non gouvernementale)</option>
          <option value="sociale">Entreprise sociale ou solidaire</option>
        </optgroup>

        <optgroup label="Secteur de la santé">
          <option value="cabinet_medical">
            Cabinet médical ou paramédical
          </option>
          <option value="clinique_privee">Clinique privée</option>
          <option value="laboratoire_analyse">
            Laboratoire d'analyses médicales
          </option>
          <option value="pharmacie">Officine / Pharmacie</option>
          <option value="e_sante">Startup e-santé (HealthTech)</option>
          <option value="centre_sante">
            Centre de santé ou maison médicale
          </option>
          <option value="sel_sante">Société d'exercice libéral (SEL)</option>
          <option value="scop_sante">SCOP dans la santé</option>
        </optgroup>

        <optgroup label="Secteur de la scolarité">
          <option value="ecole_publique">École publique</option>
          <option value="ecole_privee">École privée</option>
          <option value="etablissement_superieur">
            Établissement d’enseignement supérieur
          </option>
          <option value="centre_langue">Centre de langues</option>
          <option value="centre_soutien">Centre de soutien scolaire</option>
          <option value="institut_professionnel">
            Institut de formation professionnelle
          </option>
        </optgroup>

        <optgroup label="Statut juridique ou nature">
          <option value="publique">Entreprise publique</option>
          <option value="privee">Entreprise privée</option>
        </optgroup>
      </select>
    </div>
    <div class="form-group {{ showMoreFields ? '' : 'display-none' }}">
      <label for="tailleEntreprise">Taille d'entreprise</label>
      <input
        id="tailleEntreprise"
        type="text"
        [(ngModel)]="clientData.CompanySize"
        [value]="clientData.CompanySize"
      />
    </div>
    <div
      class="form-group {{
        (base64Image || clientData.ClientLogo) && showMoreFields
          ? ''
          : 'display-none'
      }}"
    >
      <img
        class="logo {{
          (base64Image || clientData.ClientLogo) && showMoreFields
            ? ''
            : 'display-none'
        }}"
        src="{{`data:image/jpeg;base64,${base64Image}`}}"
        alt="Logo"
      />
    </div>
    <div
      class="form-group {{ showMoreFields ? '' : 'display-none' }}"
      style="grid-column: 1/-1"
    >
      <label for="logo">Logo</label>
      <div class="file-input-container">
        <button type="button" class="file-button" (click)="fileInput.click()">
          <i class="material-icons">upload_file</i>
          {{
            (base64Image || clientData.ClientLogo) && showMoreFields
              ? "Changer le logo"
              : "Ajouter un logo"
          }}
        </button>
        <input
          hidden
          type="file"
          #fileInput
          accept="image/*"
          (change)="onFileSelected($event)"
        />
        <span *ngIf="uploadedLogo" class="file-info">
          {{ uploadedLogo.name }}
        </span>
      </div>
    </div>
  </div>

  <div class="form-actions">
    <div class="form-actions-buttons">
      <button type="button" class="cancel-button" (click)="cancel()">
        Annuler
      </button>
      <button type="submit" class="submit-button" (click)="save()">
        {{ isEditMode ? "Mettre à jour" : "Créer Client" }}
      </button>
    </div>
  </div>
</div>
