import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '@app/environments/environment';
import { Transaction } from '../models/transaction';
import { Local } from '../models/local';
import { TypeCapteur } from '@app/shared/models/typeCapteur';
import { Variables } from '../models/variables';
import { ClientLicenceControllerView } from '@app/shared/models/clientLicenceControllerView';
@Injectable({
  providedIn: 'root',
})
export class InstalationService {
  private baseUrl = environment.host + '/api'; // à adapter à ton backend

  constructor(private http: HttpClient) {}

  getClients(): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/client`);
  }

  addTransactions(transactions: Transaction[]) {
    return this.http.post(
      `${this.baseUrl}/transaction/add-range`,
      transactions
    );
  }
  getControllersByClient(clientId: number): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/controllers`);
  }

  getSites(): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/site`);
  }

  getCotroler(): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/Controller`);
  }

  getCapteur(): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/Capteur`);
  }

  getClientControllers(
    clientId: string
  ): Observable<ClientLicenceControllerView[]> {
    const url = `${this.baseUrl}/controller/client-controllers/${clientId}`;
    return this.http.get<ClientLicenceControllerView[]>(url);
  }

  getCapteurByIdControler(ControlerId: number) {
    const body = {
      pagination: {
        currentPage: 1,
        pageCount: 0,
        pageSize: 10,
        isLast: true,
        isFirst: true,
        startIndex: 0,
        totalElement: 0,
      },
      sortParams: [
        {
          column: 'nom',
          sort: 'asc',
        },
      ],
      filterParams: [
        {
          column: 'ControllerId',
          value: ControlerId,
          op: 'eq',
          andOr: 'and',
        },
      ],
    };

    return this.http.post(`${this.baseUrl}/Capteur/search`, body);
  }

  getSitesByClientId(ClientId: number) {
    const body = {
      pagination: {
        currentPage: 1,
        pageCount: 0,
        pageSize: 10,
        isLast: true,
        isFirst: true,
        startIndex: 0,
        totalElement: 0,
      },
      sortParams: [
        {
          column: 'nom',
          sort: 'asc',
        },
      ],
      filterParams: [
        {
          column: 'ClientId',
          value: ClientId,
          op: 'eq',
          andOr: 'and',
        },
      ],
    };

    return this.http.post(`${this.baseUrl}/site/search`, body);
  }

  getLocalBySitetId(IdSite: number) {
    return this.http.get<Local[]>(`${this.baseUrl}/local/by-site/${IdSite}`);
  }

  searchClients(searchTerm: string) {
    const body = {
      pagination: {
        currentPage: 1,
        pageCount: 1,
        pageSize: 10,
        isLast: false,
        isFirst: true,
        startIndex: 0,
        totalElement: 0,
      },
      filterParams: [
        {
          column: 'Name',
          value: searchTerm,
          op: 'contains', // ou "eq" ou ton opérateur selon ton backend
          andOr: 'and',
        },
      ],
      sortParams: [],
    };

    return this.http.post<any>(`${this.baseUrl}/client/search`, body);
  }

  searchSites(searchTerm: string) {
    const body = {
      pagination: {
        currentPage: 1,
        pageCount: 1,
        pageSize: 10,
        isLast: false,
        isFirst: true,
        startIndex: 0,
        totalElement: 0,
      },
      filterParams: [
        {
          column: 'Name',
          value: searchTerm,
          op: 'contains', // ou "eq" ou ton opérateur selon ton backend
          andOr: 'and',
        },
      ],
      sortParams: [],
    };

    return this.http.post<any>(`${this.baseUrl}/site/search`, body);
  }

  searchLocals(searchTerm: string) {
    const body = {
      pagination: {
        currentPage: 1,
        pageCount: 1,
        pageSize: 10,
        isLast: false,
        isFirst: true,
        startIndex: 0,
        totalElement: 0,
      },
      filterParams: [
        {
          column: 'Name',
          value: searchTerm,
          op: 'contains', // ou "eq" ou ton opérateur selon ton backend
          andOr: 'and',
        },
      ],
      sortParams: [],
    };

    return this.http.post<any>(`${this.baseUrl}/local/search`, body);
  }

  searchControlers(searchTerm: string) {
    const body = {
      pagination: {
        currentPage: 1,
        pageCount: 1,
        pageSize: 10,
        isLast: false,
        isFirst: true,
        startIndex: 0,
        totalElement: 0,
      },
      filterParams: [
        {
          column: 'Model',
          value: searchTerm,
          op: 'contains', // ou "eq" ou ton opérateur selon ton backend
          andOr: 'and',
        },
      ],
      sortParams: [],
    };

    return this.http.post<any>(`${this.baseUrl}/Controller/search`, body);
  }

  updateLocal(local: any): Observable<any> {
    console.log(local);
    const body = {
      ...local,
      architecture2DImage: JSON.stringify(local.Architecture2DImage), // 🔑
    };

    return this.http.put(`${this.baseUrl}/local`, body);
  }

  normalizeLabel(value: string): string {
    return value.toLowerCase().replace(/[\s/_-]+/g, '');
  }

  matchDeviceToTypeCapteur(
    exposes: Expose[],
    typeCapteurs: TypeCapteur[]
  ): TypeCapteur {
    const detected = this.detectDeviceType(exposes);
    const normalized = this.normalizeLabel(detected);

    return (
      typeCapteurs.find(
        (row) =>
          this.normalizeLabel(row.Nom).includes(normalized) ||
          this.normalizeLabel(row.Topic).includes(normalized) ||
          this.normalizeLabel(row.DisplayName).includes(normalized)
      ) || new TypeCapteur()
    );
  }

  detectDeviceType(exposes: Expose[]): string {
    const names: string[] = [];
  
    // Flatten names from both top-level and feature-level
    for (const e of exposes) {
      if (e.name) names.push(e.name.toLowerCase());
      if ('features' in e && Array.isArray(e.features)) {
        for (const f of e.features) {
          if (f.name) names.push(f.name.toLowerCase());
        }
      }
    }
  
    const has = (key: string) => names.includes(key);
  
    if (has('lock_state') || has('lock')) return 'Door_Lock';
    if (has('occupancy') && has('illuminance')) return 'PIR_Motion';
    if (has('garage_door') || has('garage_door_contact')) return 'Garage_Door_Sensor';
    if (has('fan_mode') || has('fan_state')) return 'Smart_Fan';
    if (has('leak') || has('water_leak')) return 'Water_Leak_Sensor';
    if (has('contact')) return 'Door_Window_Sensor';
    if (has('occupancy')) return 'Occupancy_Sensor';
    if (has('motion')) return 'Motion_Sensor';
    if (has('temperature') && has('humidity') && has('pressure')) return 'Weather_Station';
    if (has('brightness') || has('brightness_move_up')) return 'Dimmer_Switch';
    if (has('valve') || has('valve_state')) return 'Valve_Controller';
    if (has('local_temperature') && has('system_mode')) return 'Thermostat';
    if (has('state') && has('brightness')) return 'Smart_Light';
    if (has('position') && has('valve_position')) return 'Radiator_Valve';
    if (has('contact')) return 'Contact_Sensor';
    if (has('stream') || has('camera')) return 'Camera_Sensor';
    if (has('state') && (has('power') || has('switch'))) return 'Smart_Plug';
    if (has('fan_speed') || has('ceiling_fan')) return 'Ceiling_Fan';
    if (has('illuminance')) return 'Luminance_Sensor';
    if (has('temperature') && has('humidity')) return 'Temperature_Humidity_Sensor';
    if (has('air_quality') || has('pm2.5') || has('voc')) return 'Air_Quality_Sensor';
    if (has('motor') || (has('position') && has('state'))) return 'Blinds_Curtain_Motor';
    if (has('lock') && has('contact')) return 'Door_Lock_Sensor';
    if (has('color') && has('brightness') && has('state')) return 'Smart_Bulb';
    if (has('alarm') || has('siren')) return 'Siren';
    if (has('glass_break')) return 'Glass_Break';
    if (has('color') && has('effect')) return 'LED_Strip';
    if (has('presence')) return 'Presence_Sensor';
    if (has('ac_mode') || has('ac_state')) return 'AC_Controller';
    if (has('vibration')) return 'Vibration_Sensor';
    if (has('action') && has('battery')) return 'Smart_Button';
    if (has('smoke') || has('smoke_detected')) return 'Smoke_Detector';
    if (has('garage_door') || has('garage_open')) return 'Garage_Door_Opener';
  
    // Move this specific condition BEFORE the generic Zigbee_Switch condition
    if (has('state') && has('power_on_behavior') && has('linkquality')) return 'Switch';
  

  
    if (has('switch')) return 'Light_Switch';
  
    return 'unknown_device';
  }

  inferDeviceType(variables: Variables[]): string {
    const keys = variables.map((v) => v.Key);
    const types = variables.map((v) => v.Type);

    const has = (key: string) => keys.includes(key);
    const isType = (t: string) => types.includes(t);

    // PRIORITY RULES

    // === SAFETY ===
    if (has('smoke')) return 'smoke_sensor';
    if (has('co')) return 'co_sensor';
    if (has('water_leak')) return 'leak_sensor';
    if (has('glass_break')) return 'glass_break_sensor';

    // === MOTION / PRESENCE ===
    if (has('occupancy') || has('presence') || has('motion'))
      return 'motion_sensor';

    // === CONTACT ===
    if (has('contact')) return 'contact_sensor';
    if (has('state') && keys.includes('open')) return 'door_window_sensor';

    // === VIBRATION ===
    if (has('vibration')) return 'vibration_sensor';

    // === BUTTONS ===
    if (has('action') && keys.includes('single')) return 'button';
    if (keys.includes('action') && keys.includes('scene'))
      return 'scene_controller';

    // === LIGHTS ===
    if (has('brightness') || has('color') || has('color_temp')) return 'light';

    // === CLIMATE ===
    if (has('temperature') && has('humidity') && has('pressure'))
      return 'weather_station';
    if (has('temperature') && has('humidity')) return 'climate_sensor';
    if (has('temperature')) return 'temperature_sensor';
    if (has('humidity')) return 'humidity_sensor';
    if (has('pressure')) return 'pressure_sensor';

    // === AIR QUALITY ===
    if (has('voc') || has('pm10') || has('pm25') || has('co2'))
      return 'air_quality_sensor';

    // === ENERGY ===
    if (has('energy') || has('power') || has('voltage') || has('current'))
      return 'power_monitor';

    // === FAN ===
    if (has('speed') || has('direction')) return 'fan';

    // === THERMOSTAT ===
    if (
      has('local_temperature') ||
      has('occupied_cooling_setpoint') ||
      has('occupied_heating_setpoint') ||
      has('system_mode')
    )
      return 'thermostat';

    // === WINDOW COVERINGS ===
    if (has('position') && has('tilt')) return 'blinds';
    if (
      keys.includes('state') &&
      ['open', 'close', 'stop'].some((v) => keys.includes(v))
    )
      return 'cover';

    // === SIREN ===
    if (keys.includes('siren') || keys.includes('alarm')) return 'siren';

    // === PLUG ===
    if (has('state') && has('power')) return 'smart_plug';

    // === DIMMER ===
    if (has('brightness') && !has('color')) return 'dimmer_switch';

    // === CAMERA ===
    if (keys.includes('camera')) return 'camera';

    // === ACTUATORS GENERIC ===
    if (has('valve_position') || has('valve_state')) return 'valve_controller';
    if (has('relay') || has('switch')) return 'switch';

    // === FALLBACK ===
    return 'unknown';
  }

  inferTypeCapteurIdFromExposes(
    exposes: { name: string; type: string }[],
    allVariables: Variables[]
  ): string | null {
    const normalizeKey = (key: string) =>
      key.toLowerCase().replace(/[^a-z0-9]/g, '');

    const exposeKeys = exposes.map((e) => normalizeKey(e.name));
    console.log('Expose keys (normalized):', exposeKeys);

    const matchScore: Record<string, number> = {};

    for (const variable of allVariables) {
      const keyLower = normalizeKey(variable.Key);
      if (exposeKeys.includes(keyLower)) {
        const capteurId = variable.IdTypeCapteur;
        if (capteurId) {
          matchScore[capteurId] = (matchScore[capteurId] || 0) + 1;
        }
      }
    }

    console.log('Match score:', matchScore);

    if (Object.keys(matchScore).length === 0) {
      return null;
    }

    const [topId] = Object.entries(matchScore).sort((a, b) => b[1] - a[1])[0];
    console.log('Top matched IdTypeCapteur:', topId);
    return topId;
  }

  inferTypeCapteurFromExposes(
    exposes: { name: string; type: string }[],
    allVariables: Variables[],
    allTypeCapteurs: TypeCapteur[]
  ): TypeCapteur {
    const id = this.inferTypeCapteurIdFromExposes(exposes, allVariables);
    return allTypeCapteurs.find((c) => c.Id === id) || new TypeCapteur();
  }
}

export interface Expose {
  type: string;
  name?: string;
  property?: string;
  features?: Expose[];
}
