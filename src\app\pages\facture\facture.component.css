/* General Layout */
.invoice-container {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  max-width: 1400px; /* Keep a max width for large screens */
  margin: 0 auto; /* Center the container */
  padding: 24px;
  color: #333;
  position: relative;
  /* Removed conflicting max-width and margin-left to allow proper centering and responsiveness */
  box-sizing: border-box;
}

.header {
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h1 {
  font-family: "Montserrat", sans-serif;
  font-size: 28px;
  margin: 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* Action Buttons (Download, Preview) */
.action-btn {
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
  background: white;
  color: #4a5568;
}

.action-btn:hover {
  background: #f8fafc;
  border-color: #cbd5e0;
}

.action-btn.primary {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.action-btn.primary:hover {
  background: #0d9f6e;
  border-color: #0d9f6e;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Summary Cards */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(
    auto-fit,
    minmax(220px, 1fr)
  ); /* Responsive grid */
  gap: 24px;
  margin-bottom: 30px;
}

.card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #e2e8f0;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
  border-color: #10b981;
}

.card-icon {
  font-size: 32px;
  margin-bottom: 12px;
  color: #10b981;
}

.card h3 {
  margin: 0 0 12px;
  font-size: 16px;
  color: #4a5568;
  font-weight: 600;
}

.card .amount {
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 8px;
  color: #2d3748;
}

.card .subtext {
  font-size: 13px;
  color: #718096;
  margin: 0;
}

.card::after {
  content: "";
  position: absolute;
  right: -20px;
  top: -20px;
  width: 60px;
  height: 60px;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 50%;
}

/* Controls (Search & Filter) */
.controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap; /* Allow wrapping on smaller screens */
}

.search {
  position: relative;
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-grow: 1; /* Allow it to grow */
}

.search input {
  width: 100%;
  padding: 12px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px 0 0 12px;
  font-size: 14px;
  background-color: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  outline: none; /* Remove default outline */
}

.search input:focus {
  border-color: #10b981;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.search-btn {
  padding: 0 16px;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 0 12px 12px 0;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn:hover {
  background-color: #0d9f6e;
}

.search-btn .material-icons {
  font-size: 20px;
}

.search::before {
  /* This is likely for a search icon inside the input, if so, it should be a background image or separate element */
  /* If you want an icon inside, it's better to use a span with material-icons and position it */
  /* For now, commenting out as it might conflict with actual input text */
  /* font-family: "Material Icons";
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-size: 20px; */
}

.filter select {
  padding: 12px 15px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  outline: none; /* Remove default outline */
}

.filter select:focus {
  border-color: #10b981;
}

/* Table Container and Table Styling */
.table-strip {
  width: 100%;
  height: 5px;
  min-height: 5px;
  max-height: 12px;
  background: #10b981;
  border-radius: 8px 8px 0 0;
  margin-bottom: 0;
}

.invoice-table-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  width: 100%;
  margin-bottom: 20px;
  padding: 8px 0; /* Adjusted padding */
  overflow-x: auto; /* Enables horizontal scrolling */
  -webkit-overflow-scrolling: touch; /* Improves scrolling on iOS devices */
}

.invoice-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed; /* Keep fixed layout for consistent columns */
  font-size: 13px;
  /* Calculate total min-width for all 11 columns:
     40 (checkbox) + 100 (Numéro) + 180 (Client) + 120 (Abonnement) + 90 (Prix) + 100 (Statut) +
     100 (Date création) + 100 (Date échéance) + 100 (Date paiement) + 120 (Fréquence) + 100 (Actions) = 1150px
  */
  min-width: 1150px; /* Ensure table takes at least this width to trigger scroll */
}

.invoice-table th,
.invoice-table td {
  padding: 8px 8px;
  text-align: left;
  border-bottom: 1px solid #eee;
  white-space: nowrap; /* Prevent text wrapping */
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis; /* Truncate text with ellipsis if it overflows */
}

/* Updated column widths for all 11 columns */
.invoice-table th:nth-child(1),
.invoice-table td:nth-child(1) {
  width: 40px;
} /* Checkbox */
.invoice-table th:nth-child(2),
.invoice-table td:nth-child(2) {
  width: 100px;
} /* Numéro de Facture */
.invoice-table th:nth-child(3),
.invoice-table td:nth-child(3) {
  width: 180px;
} /* Nom de Client */
.invoice-table th:nth-child(4),
.invoice-table td:nth-child(4) {
  width: 120px;
} /* Abonnement */
.invoice-table th:nth-child(5),
.invoice-table td:nth-child(5) {
  width: 90px;
} /* Prix */
.invoice-table th:nth-child(6),
.invoice-table td:nth-child(6) {
  width: 100px;
} /* Statut */
.invoice-table th:nth-child(7),
.invoice-table td:nth-child(7) {
  width: 100px;
} /* Date de création */
.invoice-table th:nth-child(8),
.invoice-table td:nth-child(8) {
  width: 100px;
} /* Date d'écheance */
.invoice-table th:nth-child(9),
.invoice-table td:nth-child(9) {
  width: 100px;
} /* Date de paiement */
.invoice-table th:nth-child(10),
.invoice-table td:nth-child(10) {
  width: 120px;
} /* Fréquence de Paiement */
.invoice-table th:nth-child(11),
.invoice-table td:nth-child(11) {
  width: 100px;
} /* Actions */

.invoice-table th {
  font-weight: 600;
  color: #718096;
  font-size: 13px;
  background: #f8fafc;
}

.invoice-table tbody tr {
  cursor: pointer;
  transition: background 0.2s ease;
  position: relative;
}

.invoice-table tbody tr:hover {
  background-color: #f3fdf7;
}

.invoice-table tbody tr.selected {
  background-color: #e6f9f0;
}

.checkbox-cell {
  width: 32px;
  padding-right: 0;
}

.checkbox-cell input {
  width: 14px;
  height: 14px;
  cursor: pointer;
}

/* Status Badge Styles */
.status-badge {
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  display: inline-block;
  width: auto;
  text-align: center; /* Ensure text is centered within the badge */
}

/* Paid Status - Green */
.status-badge.paid {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: #28a745;
  border: 1px solid #10b981; /* Added border for definition */
}

/* En attente Status - Yellow/Orange */
.status-badge.unpaid, /* Combined with 'En attente' for consistency */
.status-badge.en.attente {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  border: 1px solid #f59e0b; /* Added border for definition */
}

/* Résilié Status - Red */
.status-badge.cancelled, /* Combined with 'Résilié' for consistency */
.status-badge.résilié {
  background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
  color: #991b1b;
  border: 1px solid #ef4444; /* Added border for definition */
}

/* New status badge styles (if they map to specific values not covered above) */
.status-active {
  color: #28a745;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: rgba(40, 167, 69, 0.1);
  display: inline-block;
  min-width: 60px;
}

.status-inactive {
  color: #dc3545;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: rgba(220, 53, 69, 0.1);
  display: inline-block;
  min-width: 60px;
}

.status-retire {
  color: #ff9900;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: #ffe1c4;
  display: inline-block;
  min-width: 60px;
}

.actions-cell {
  width: 100px; /* Adjusted to match new column width */
  min-width: 100px;
}

.action-dropdown {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  width: 100%;
}

/* Dropdown buttons within table cells */
.action-dropdown .dropdown-item,
.action-dropdown .dropdown-btn {
  background: #f8fafc;
  cursor: pointer;
  color: #64748b;
  font-size: 10px;
  padding: 4px 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  border: 1px solid #e2e8f0; /* Added border for better definition */
}

.action-dropdown .dropdown-item::before,
.action-dropdown .dropdown-btn::before {
  content: ""; /* Ensure content is present for pseudo-elements */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(16, 185, 129, 0.1),
    transparent
  );
  transition: transform 0.3s ease;
  transform: translateX(-100%);
}

.action-dropdown .dropdown-item:hover::before,
.action-dropdown .dropdown-btn:hover::before {
  transform: translateX(0%);
}

.action-dropdown .dropdown-item:hover,
.action-dropdown .dropdown-btn:hover {
  color: black;
  background: #f0fdf4; /* Light green on hover */
}

/* Responsive adjustments for dropdown buttons */
@media (max-width: 900px) {
  .action-dropdown .dropdown-item,
  .action-dropdown .dropdown-btn {
    padding: 3px 5px;
    font-size: 9px;
    min-width: 20px;
    height: 20px;
  }

  .action-dropdown .dropdown-item .material-icons,
  .action-dropdown .dropdown-btn .material-icons {
    font-size: 12px;
  }
}

@media (max-width: 600px) {
  .action-dropdown .dropdown-item,
  .action-dropdown .dropdown-btn {
    padding: 2px 4px;
    font-size: 8px;
    min-width: 18px;
    height: 18px;
    gap: 2px;
  }

  .action-dropdown .dropdown-item .material-icons,
  .action-dropdown .dropdown-btn .material-icons {
    font-size: 11px;
  }
}

/* Specific dropdown button styles (for the "more_horiz" button if it exists) */
.dropdown-btn {
  background: transparent;
  border: none;
  cursor: pointer;
  color: #6b7280;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 2px;
  min-width: 24px;
  flex: none;
  justify-content: center;
  font-weight: 400;
  position: relative;
  z-index: 1;
}

.dropdown-btn:hover {
  background: transparent;
  color: #374151;
  transform: none;
}

.dropdown-btn:active {
  background: transparent;
  color: #1f2937;
}

.dropdown-btn .material-icons {
  font-size: 12px;
  transition: transform 0.2s ease;
}

.dropdown-btn:hover .material-icons {
  transform: translate(-2px);
}

.dropdown-content {
  position: absolute;
  right: 0;
  background-color: white;
  min-width: 120px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  z-index: 2001;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  display: none;
}

.dropdown-content.show {
  display: block;
}

.dropdown-item {
  padding: 8px 12px;
  text-decoration: none;
  display: block;
  color: #4a5568;
  font-size: 13px;
  transition: background 0.2s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dropdown-item:hover {
  background-color: #f8fafc;
  color: #10b981;
}

/* No results row */
.no-results {
  text-align: center;
  padding: 40px 20px;
  color: #718096;
  font-style: italic;
}

/* Responsive table adjustments */
@media (max-width: 1200px) {
  /* These might still be too aggressive if the overall container is small */
  /* .invoice-table th:nth-child(3),
  .invoice-table td:nth-child(3) {
    width: 150px;
  }
  .invoice-table th:nth-child(8),
  .invoice-table td:nth-child(8) {
    width: 100px;
  } */
}

@media (max-width: 900px) {
  .invoice-table th,
  .invoice-table td {
    font-size: 12px;
    padding: 6px 4px;
  }
  .invoice-table-container {
    padding: 0;
  }
  .dropdown-btn {
    padding: 3px 6px;
    font-size: 11px;
  }
  .dropdown-btn .material-icons {
    font-size: 12px;
  }

  /* Status badge responsive */
  .status-badge {
    padding: 4px 8px;
    font-size: 10px;
  }
}

@media (max-width: 600px) {
  .invoice-table th,
  .invoice-table td {
    font-size: 11px;
    padding: 4px 2px;
  }
  .invoice-table-container {
    padding: 0;
  }
  .dropdown-btn {
    padding: 2px 4px;
    font-size: 10px;
  }
  .dropdown-btn .material-icons {
    font-size: 11px;
  }

  /* Status badge responsive */
  .status-badge {
    padding: 3px 6px;
    font-size: 9px;
  }
}

.table-strip {
  width: 100%;
  height: 5px;
  min-height: 5px;
  max-height: 12px;
  background: #10b981;
  border-radius: 8px 8px 0 0;
  margin-bottom: 0;
}

.card-pagination-container,
.pagination-container {
  margin-top: 24px;
  margin-bottom: 24px;
}

.pagination-container {
  padding: 24px;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.card-pagination-container {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 16px 24px;
  border: 1px solid #f1f5f9;
}

/* Force override Material paginator background - Multiple selectors for specificity */
.pagination-container mat-paginator,
.card-pagination-container mat-paginator,
mat-paginator.mat-mdc-paginator,
.mat-mdc-paginator {
  background: transparent !important;
  background-color: transparent !important;
}

/* Target the specific Material classes with higher specificity */
.pagination-container .mat-mdc-paginator,
.card-pagination-container .mat-mdc-paginator {
  background: none !important;
  background-color: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Style the paginator wrapper div */
.pagination-container .mat-mdc-paginator > div,
.card-pagination-container .mat-mdc-paginator > div {
  background: transparent !important;
}

/* Navigation buttons styling */
.pagination-container .mat-mdc-icon-button,
.card-pagination-container .mat-mdc-icon-button {
  background: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 6px !important;
  margin: 0 4px !important;
  width: 36px !important;
  height: 36px !important;
  transition: all 0.2s ease !important;
}

.pagination-container .mat-mdc-icon-button:hover,
.card-pagination-container .mat-mdc-icon-button:hover {
  background: #e2e8f0 !important;
  border-color: #cbd5e1 !important;
  transform: translateY(-1px);
}

.pagination-container .mat-mdc-icon-button:disabled,
.card-pagination-container .mat-mdc-icon-button:disabled {
  background: #f1f5f9 !important;
  border-color: #e5e7eb !important;
  opacity: 0.5;
  transform: none;
}

/* Page size dropdown */
.pagination-container .mat-mdc-select,
.card-pagination-container .mat-mdc-select {
  background: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 6px !important;
  margin: 0 8px !important;
}

.pagination-container .mat-mdc-select-trigger,
.card-pagination-container .mat-mdc-select-trigger {
  background: transparent !important;
}

/* Range label text */
.pagination-container .mat-mdc-paginator-range-label,
.card-pagination-container .mat-mdc-paginator-range-label {
  color: #475569 !important;
  font-weight: 500 !important;
  margin: 0 16px !important;
}

/* Page size label */
.pagination-container .mat-mdc-paginator-page-size-label,
.card-pagination-container .mat-mdc-paginator-page-size-label {
  color: #64748b !important;
  font-weight: 400 !important;
}

/* Hide form field underlines */
.pagination-container .mat-mdc-form-field-subscript-wrapper,
.card-pagination-container .mat-mdc-form-field-subscript-wrapper {
  display: none !important;
}

/* Alternative: If the above doesn't work, use this global override */
mat-paginator {
  background: transparent !important;
}

mat-paginator .mat-mdc-paginator {
  background: transparent !important;
  background-color: transparent !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pagination-container {
    padding: 16px;
  }

  .card-pagination-container {
    padding: 12px 16px;
  }

  .pagination-container .mat-mdc-paginator,
  .card-pagination-container .mat-mdc-paginator {
    flex-wrap: wrap;
    gap: 8px;
  }
}

/* Invoice Detail Sidebar */
.invoice-detail {
  position: fixed;
  top: 0;
  right: 0;
  width: 420px;
  max-width: 100vw;
  height: 100%;
  background: linear-gradient(120deg, #f8fafc 60%, #e6f9f0 100%);
  box-shadow: -10px 0 32px rgba(16, 185, 129, 0.13),
    -2px 0 8px rgba(0, 0, 0, 0.04);
  padding: 28px 28px 90px 28px;
  overflow-y: auto;
  z-index: 9999;
  transition: box-shadow 0.3s, background 0.3s;
  border-left: 2px solid #10b981;
  border-top-left-radius: 18px;
  border-bottom-left-radius: 18px;
  animation: slideInInvoiceDetail 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

@keyframes slideInInvoiceDetail {
  0% {
    right: -440px;
    opacity: 0;
  }
  100% {
    right: 0;
    opacity: 1;
  }
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 22px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e2e8f0;
}

.detail-header h3 {
  margin: 0;
  color: #10b981;
  font-size: 1.35rem;
  font-family: "Montserrat", sans-serif;
  letter-spacing: 0.5px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 26px;
  cursor: pointer;
  color: #b0b0b0;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s, color 0.2s, transform 0.2s;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #ef4444;
  transform: scale(1.08);
}

.from-to {
  display: flex;
  gap: 20px;
  margin-bottom: 18px;
}

.from,
.to {
  flex: 1;
}

.from-to h4 {
  margin: 0 0 5px;
  font-size: 14px;
  color: #7f8c8d;
  font-weight: 600;
  letter-spacing: 0.2px;
}

.from-to p {
  margin: 0;
  font-size: 15px;
  color: #374151;
  font-weight: 500;
}

.invoice-meta {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px 18px;
  margin-bottom: 18px;
}

.invoice-meta h4 {
  margin: 0 0 4px;
  font-size: 13px;
  color: #7f8c8d;
  font-weight: 600;
}

.invoice-meta p {
  margin: 0;
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.items-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 18px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(16, 185, 129, 0.04);
}

.items-table th,
.items-table td {
  padding: 10px 8px;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
  font-size: 13px;
}

.items-table th {
  color: #10b981;
  font-weight: 700;
  background: #f8fafc;
  font-size: 13.5px;
}

.items-table tr:last-child td {
  border-bottom: none;
}

.summary {
  margin-top: 12px;
  background: #f8fafc;
  border-radius: 8px;
  padding: 14px 18px 10px 18px;
  box-shadow: 0 1px 4px rgba(16, 185, 129, 0.04);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #374151;
}

.summary-row.total {
  font-weight: bold;
  padding-top: 8px;
  border-top: 1px solid #e2e8f0;
  color: #10b981;
  font-size: 15px;
}

.summary-row.amount-due {
  font-size: 16px;
  font-weight: bold;
  color: #ef4444;
  margin-top: 8px;
}

.pay-now-btn {
  width: 100%;
  padding: 13px 0;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  margin-top: 18px;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.08);
  letter-spacing: 0.5px;
}

.pay-now-btn:hover {
  background: linear-gradient(90deg, #059669 0%, #10b981 100%);
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.13);
}

@media (max-width: 600px) {
  .invoice-detail {
    width: 100vw;
    min-width: 0;
    padding: 18px 8px 70px 8px;
    border-radius: 0;
  }
  .detail-header h3 {
    font-size: 1.1rem;
  }
  .items-table th,
  .items-table td {
    font-size: 12px;
    padding: 7px 4px;
  }
  .summary {
    padding: 10px 8px 8px 8px;
  }
}

/* --- Existing Popup and Form Styles (kept as is) --- */
/* Popup styles */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.confirmation-popup {
  background: white;
  border-radius: 24px;
  padding: 2.5rem;
  max-width: 900px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  position: relative;
  border: 1px solid #e2e8f0;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid #f1f5f9;
}

.popup-header h3 {
  font-family: "Montserrat", sans-serif;
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.close-popup-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
}

.currency-spaced .amount::first-letter {
  margin-right: 4px;
}

/* Option 2: If using separate spans */
.currency-symbol {
  margin-right: 4px;
}

/* Option 3: Universal solution for all currency displays */
.amount {
  letter-spacing: 1px;
}

.card .amount::after {
  content: "";
  display: inline-block;
  width: 4px;
}

.close-popup-btn:hover {
  background: #10b981;
  color: white;
  transform: scale(1.1);
}

.popup-content {
  margin-bottom: 2.5rem;
}

.popup-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1.5rem;
  border-top: 1px solid #f1f5f9;
}

.cancel-btn,
.confirm-btn {
  padding: 0.875rem 2rem;
  border-radius: 16px;
  font-family: "Montserrat", sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
  justify-content: center;
}

.cancel-btn {
  background: #f8fafc;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.cancel-btn:hover {
  background: #e2e8f0;
  color: #475569;
  transform: translateY(-2px);
}

.confirm-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: 2px solid #10b981;
}

.confirm-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.confirm-btn:disabled {
  background: #e5e7eb !important;
  color: #a1a1aa !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  opacity: 0.7;
  transform: none !important;
  border-color: #e5e7eb !important;
}

/* Enhanced Form styles */
.add-option-inputs-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 0.5rem;
}

.form-row.two-cols > .improved-form-group {
  flex: 1 1 0;
  min-width: 0;
}

.form-row.three-cols > .improved-form-group {
  flex: 1 1 0;
  min-width: 0;
}

.improved-form-group {
  margin-bottom: 0;
  position: relative;
}

.label {
  font-family: "Montserrat", sans-serif;
  font-weight: 600;
  color: #374151;
  font-size: 0.95rem;
  margin-bottom: 0.75rem;
  display: block;
  position: relative;
}

.label::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 20px;
  height: 2px;
  background: #10b981;
  border-radius: 2px;
}

.improved-input {
  width: 100%;
  padding: 1rem 1.25rem;
  border-radius: 16px;
  border: 2px solid #e2e8f0;
  font-family: "Lato", sans-serif;
  font-size: 1rem;
  color: #2d3748;
  transition: all 0.3s ease;
  outline: none;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.improved-input:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
  transform: translateY(-1px);
}

.improved-input:disabled {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  color: #64748b;
  cursor: not-allowed;
  border-color: #e2e8f0;
}

.improved-input:disabled::placeholder {
  color: #94a3b8;
}

/* Search input container */
.search-input-container {
  position: relative;
  margin-bottom: 0;
}

.search-input {
  width: 100%;
  padding: 1rem 1.25rem 1rem 3.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  font-family: "Lato", sans-serif;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  outline: none;
}

.search-input:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
  transform: translateY(-1px);
}

.search-input::placeholder {
  color: #94a3b8;
  font-style: italic;
}

.search-icon {
  position: absolute;
  left: 1.25rem;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-size: 1.3rem;
  transition: all 0.3s ease;
}

.search-input:focus + .search-icon {
  color: #059669;
  transform: translateY(-50%) scale(1.1);
}

/* Dropdown styles */
.dropdown {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  width: 100%;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  margin-top: 0.25rem;
  max-height: 280px;
  overflow-y: auto;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid #f1f5f9;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
  transform: translateX(4px);
}

.client-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.client-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e2e8f0;
}

.client-text {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.client-name {
  font-family: "Montserrat", sans-serif;
  font-weight: 600;
  font-size: 1rem;
  color: #1e293b;
  margin-bottom: 2px;
}

.client-email {
  font-family: "Lato", sans-serif;
  font-size: 0.875rem;
  color: #64748b;
}

/* Input focus animations */
.improved-input:focus,
.search-input:focus {
  animation: inputFocus 0.3s ease-out;
}

@keyframes inputFocus {
  0% {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }
  50% {
    transform: translateY(-2px);
  }
  100% {
    transform: translateY(-1px);
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
  }
}

/* Success notification */
.success-notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: white;
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #10b981;
  z-index: 3000;
  max-width: 400px;
  width: 90%;
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.success-icon {
  color: #10b981;
  font-size: 1.5rem;
}

.notification-text h4 {
  font-family: "Montserrat", sans-serif;
  font-size: 1.1rem;
  color: #065f46;
  margin: 0 0 0.25rem 0;
  font-weight: 700;
}

.notification-text p {
  font-family: "Lato", sans-serif;
  font-size: 0.9rem;
  color: #374151;
  margin: 0;
  line-height: 1.5;
}

.close-notification-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  background: #f0fdf4;
  color: #10b981;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: auto;
}

.close-notification-btn:hover {
  background: #dcfce7;
  color: #059669;
}

/* Facture creation popup grid style (like client creation) */
.facture-popup .popup-header {
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 24px;
  padding-bottom: 16px;
}

.facture-popup .popup-header h3 {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 22px;
  font-weight: 700;
  color: #2d3748;
}

.facture-form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 18px 28px;
  width: 100%;
  margin-bottom: 24px;
}

.facture-form-grid .form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.facture-form-grid label {
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
}

.facture-form-grid input,
.facture-form-grid select {
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  color: #4a5568;
  background-color: white;
  transition: all 0.2s ease;
  box-sizing: border-box;
  width: 100%;
}

.facture-form-grid input:focus,
.facture-form-grid select:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.required {
  color: #ef4444;
  margin-left: 4px;
}

@media (max-width: 900px) {
  .facture-form-grid {
    grid-template-columns: 1fr 1fr;
    gap: 14px 18px;
  }
}
@media (max-width: 600px) {
  .facture-form-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }
}

/* Responsive design for forms/popups */
@media (max-width: 900px) {
  .form-row.three-cols {
    flex-direction: column;
    gap: 1rem;
  }
  .form-row.two-cols {
    flex-direction: column;
    gap: 1rem;
  }

  .confirmation-popup {
    padding: 2rem;
    max-width: 95%;
  }

  .popup-header h3 {
    font-size: 1.5rem;
  }
}

@media (max-width: 600px) {
  .confirmation-popup {
    padding: 1.5rem;
  }

  .popup-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .cancel-btn,
  .confirm-btn {
    width: 100%;
  }
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  border: 5px solid #f3f3f3;
  border-top: 5px solid #10b981;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
 