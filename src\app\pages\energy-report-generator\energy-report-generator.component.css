/* Energy Report Generator Component Styles */

.report-generator-container {
  min-height: 100vh;
  font-family: 'Montserrat', 'Lato', sans-serif;

  max-width: 98%;
  margin-left: 50px; /* Add this line to push content right of the sidebar */
  padding: 24px; /* Optional: add some padding for top/right/bottom */
  box-sizing: border-box;
}

/* Header Section */
.header-section {
  text-align: center;
  margin-bottom: 3rem;
  color: var(--text-primary, #1e293b);
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  color: var(--text-primary, #1e293b);
  animation: slideDown 0.8s ease-out;
}

.page-title mat-icon {
  font-size: 3rem;
  width: 3rem;
  height: 3rem;
  color: var(--primary, #2F7D33);
  animation: pulse 2s infinite;
}

.page-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary, #64748b);
  opacity: 0.9;
  margin: 0;
  animation: fadeIn 1s ease-out 0.3s both;
}

/* Form Container */
.form-container {
  animation: slideUp 0.8s ease-out;
}

.form-card {
  background: var(--surface, #ffffff);
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
  border: 1px solid var(--card-border, #eeeeee);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.form-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow, 0 4px 12px rgba(0, 0, 0, 0.05));
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary, #1e293b);
  margin: 0 0 2rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--primary-light, #67c987);
}

.section-title mat-icon {
  color: var(--primary, #2F7D33);
  font-size: 1.75rem;
  width: 1.75rem;
  height: 1.75rem;
}

/* Form Sections */
.form-section {
  margin-bottom: 2.5rem;
}

.form-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary, #1e293b);
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

/* Search Container */
.search-container {
  position: relative;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 1rem;
  color: var(--text-secondary, #64748b);
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid var(--card-border, #eeeeee);
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--surface, #ffffff);
  color: var(--text-primary, #1e293b);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary, #2F7D33);
  box-shadow: 0 0 0 3px var(--highlight-color, rgba(46, 125, 50, 0.1));
  transform: translateY(-2px);
}

/* Dropdown */
.dropdown-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--surface, #ffffff);
  border: 1px solid var(--card-border, #eeeeee);
  border-radius: 12px;
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
  z-index: 1000;
  max-height: 350px;
  overflow-y: auto;
  animation: dropDown 0.3s ease-out;
  margin-top: 4px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--beige-light, #F6F6F6);
  border: none;
  background: transparent;
  width: 100%;
  text-align: left;
}

.dropdown-item:hover {
  background: var(--hover-bg-color, rgba(103, 201, 135, 0.1));
  border-left: 4px solid var(--primary, #2F7D33);
  padding-left: calc(1.25rem - 4px);
}

.dropdown-item:last-child {
  border-bottom: none;
  border-radius: 0 0 12px 12px;
}

.dropdown-item:first-child {
  border-radius: 12px 12px 0 0;
}

.org-info {
  flex: 1;
  min-width: 0;
}

.org-info .org-name {
  font-weight: 600;
  color: var(--text-primary, #1e293b);
  margin-bottom: 0.25rem;
  font-size: 1rem;
  line-height: 1.4;
}

.org-info .org-details {
  font-size: 0.875rem;
  color: var(--text-secondary, #64748b);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.org-info .org-details::before {
  content: "🏢";
  font-size: 0.75rem;
}

.select-icon {
  color: var(--primary, #2F7D33);
  flex-shrink: 0;
  margin-left: 1rem;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.dropdown-item:hover .select-icon {
  opacity: 1;
}

.no-results {
  padding: 2rem 1.5rem;
  text-align: center;
  color: var(--text-secondary, #64748b);
  font-style: italic;
  background: var(--beige-light, #F6F6F6);
  border-radius: 12px;
  margin: 0.5rem;
}

/* Selected Organization */
.selected-org {
  margin-top: 1rem;
  animation: fadeIn 0.5s ease-out;
}

.org-card-mini {
  background: linear-gradient(135deg, var(--primary, #2F7D33), var(--primary-dark, #488c5e));
  border-radius: 16px;
  padding: 1.5rem;
  color: white;
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
  border: 1px solid var(--primary-light, #67c987);
}

.org-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.org-icon {
  font-size: 2rem;
  width: 2rem;
  height: 2rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.org-header .org-info h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
}

.org-header .org-info p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.clear-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: auto;
}

.clear-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.clear-btn mat-icon {
  color: white;
  font-size: 1.25rem;
}

.org-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.15);
  padding: 1rem;
  border-radius: 12px;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 1.5rem;
  opacity: 0.9;
  color: white;
}

.stat-item span {
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Date Range */
.date-range-container {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 1rem;
  align-items: center;
}

.date-field {
  width: 100%;
}

.date-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary, #2F7D33);
  font-weight: bold;
}

.date-separator mat-icon {
  font-size: 1.5rem;
}

/* Format Selection */
.format-selection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.format-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border: 2px solid var(--card-border, #eeeeee);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--surface, #ffffff);
  position: relative;
  overflow: hidden;
}

.format-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--highlight-color, rgba(46, 125, 50, 0.1)), transparent);
  transition: left 0.5s ease;
}

.format-option:hover::before {
  left: 100%;
}

.format-option:hover {
  border-color: var(--primary, #2F7D33);
  transform: translateY(-3px);
  box-shadow: var(--shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1));
}

.format-option.selected {
  border-color: var(--primary, #2F7D33);
  background: linear-gradient(135deg, var(--primary, #2F7D33), var(--primary-dark, #488c5e));
  color: white;
  transform: scale(1.02);
}

.format-icon {
  font-size: 2rem;
  width: 2rem;
  height: 2rem;
  color: var(--primary, #2F7D33);
}

.format-option.selected .format-icon {
  color: white;
}

.format-info h4 {
  margin: 0 0 0.25rem 0;
  font-weight: 600;
  color: var(--text-primary, #1e293b);
}

.format-option.selected .format-info h4 {
  color: white;
}

.format-info p {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.8;
  color: var(--text-secondary, #64748b);
}

.format-option.selected .format-info p {
  color: rgba(255, 255, 255, 0.9);
}

.check-icon {
  margin-left: auto;
  color: var(--success, #10b981);
  font-size: 1.5rem;
}

.format-option.selected .check-icon {
  color: white;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e2e8f0;
}

/* Buttons */
.btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.3s ease, height 0.3s ease;
  transform: translate(-50%, -50%);
}

.btn:hover::before {
  width: 300px;
  height: 300px;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary, #2F7D33), var(--primary-dark, #488c5e));
  color: white;
  box-shadow: var(--shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1));
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
  background: linear-gradient(135deg, var(--primary-dark, #488c5e), var(--primary, #2F7D33));
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: var(--surface, #ffffff);
  color: var(--text-secondary, #64748b);
  border: 2px solid var(--card-border, #eeeeee);
}

.btn-secondary:hover {
  background: var(--beige-light, #F6F6F6);
  border-color: var(--primary-light, #67c987);
  transform: translateY(-2px);
  color: var(--primary, #2F7D33);
}

/* Preview Container */
.preview-container {
  animation: slideUp 0.8s ease-out;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--surface, #ffffff);
  padding: 1.5rem 2rem;
  border-radius: 16px;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1));
  border: 1px solid var(--card-border, #eeeeee);
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary, #1e293b);
  margin: 0;
}

.preview-title mat-icon {
  color: var(--primary, #2F7D33);
}

.action-buttons {
  display: flex;
  gap: 1rem;
}

/* Report Preview */
.report-preview {
  background: var(--surface, #ffffff);
  border-radius: 16px;
  padding: 3rem;
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
  animation: fadeIn 0.8s ease-out;
  border: 1px solid var(--card-border, #eeeeee);
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 3px solid var(--primary, #2F7D33);
}

.report-title h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary, #1e293b);
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, var(--primary, #2F7D33), var(--primary-light, #67c987));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.report-period {
  font-size: 1.1rem;
  color: var(--text-secondary, #64748b);
  font-weight: 500;
}

.report-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-icon {
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
  color: white;
  background: linear-gradient(135deg, var(--primary, #2F7D33), var(--primary-light, #67c987));
  border-radius: 50%;
  padding: 1rem;
  box-shadow: var(--shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Report Sections */
.report-section {
  margin-bottom: 3rem;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary, #1e293b);
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--primary-light, #67c987);
}

.section-header mat-icon {
  color: var(--primary, #2F7D33);
  font-size: 1.75rem;
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 1rem;
  background: var(--green-light, #D5E4CF);
  border-radius: 12px;
  transition: all 0.2s ease;
  border: 1px solid var(--primary-light, #67c987);
}

.info-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1));
  background: var(--hover-bg-color, rgba(103, 201, 135, 0.1));
}

.info-item label {
  font-weight: 600;
  color: var(--text-secondary, #64748b);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item span {
  font-size: 1rem;
  color: var(--text-primary, #1e293b);
  font-weight: 600;
}

.sites-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.site-address {
  font-size: 0.875rem;
  color: #718096;
  margin-left: 1rem;
}

/* Summary Grid */
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.summary-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: linear-gradient(135deg, #f7fafc, #edf2f7);
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, var(--primary, #2F7D33), var(--primary-light, #67c987));
}

.summary-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
}

.summary-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.summary-icon.positive {
  background: linear-gradient(135deg, var(--primary, #2F7D33), var(--primary-light, #67c987));
  color: white;
}

.summary-icon.warning {
  background: linear-gradient(135deg, var(--warning, #f59e0b), #fbbf24);
  color: white;
}

.summary-icon.negative {
  background: linear-gradient(135deg, var(--secondary, #64748b), var(--text-secondary, #64748b));
  color: white;
}

.summary-icon mat-icon {
  font-size: 1.5rem;
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary, #1e293b);
  margin-bottom: 0.25rem;
}

.summary-label {
  font-size: 0.875rem;
  color: var(--text-secondary, #64748b);
  font-weight: 500;
}

/* Data Table */
.data-table {
  overflow-x: auto;
  border-radius: 12px;
  box-shadow: var(--shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1));
  border: 1px solid var(--card-border, #eeeeee);
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
  background: var(--surface, #ffffff);
}

.data-table thead {
  background: linear-gradient(135deg, var(--primary, #2F7D33), var(--primary-dark, #488c5e));
  color: white;
}

.data-table th,
.data-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--card-border, #eeeeee);
}

.data-table th {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.875rem;
  color: white;
}

.data-table td {
  color: var(--text-primary, #1e293b);
}

.data-table tbody tr {
  transition: background-color 0.2s ease;
}

.data-table tbody tr:hover {
  background: var(--hover-bg-color, rgba(103, 201, 135, 0.1));
}

.data-table tbody tr:last-child td {
  border-bottom: none;
}

/* Savings Section */
.savings-highlight {
  text-align: center;
  padding: 3rem;
  background: linear-gradient(135deg, var(--primary, #2F7D33), var(--primary-light, #67c987));
  border-radius: 16px;
  color: white;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--primary-light, #67c987);
}

.savings-highlight::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

.savings-amount {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 1;
  color: white;
}

.savings-description {
  font-size: 1.2rem;
  opacity: 0.95;
  position: relative;
  z-index: 1;
  color: rgba(255, 255, 255, 0.9);
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes dropDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .report-generator-container {
    padding: 1rem;
  }
  
  .form-card,
  .report-preview {
    padding: 1.5rem;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .date-range-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .date-separator {
    transform: rotate(90deg);
  }
  
  .format-selection {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .preview-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .action-buttons {
    width: 100%;
    justify-content: center;
  }
  
  .report-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .savings-amount {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .report-generator-container {
    padding: 0.5rem;
  }
  
  .form-card,
  .report-preview {
    padding: 1rem;
  }
  
  .page-title {
    font-size: 1.5rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .org-stats {
    grid-template-columns: 1fr;
  }
  
  .data-table {
    font-size: 0.875rem;
  }
  
  .data-table th,
  .data-table td {
    padding: 0.5rem;
  }
}