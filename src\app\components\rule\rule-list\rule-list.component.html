<div class="rules-list" *ngIf="rules.length > 0">
    <div *ngFor="let rule of rules" class="rule-item" [class.rule-expanded]="rule.id === expandedRuleId"
        [class.action-pending]="isRuleActionPending(rule.id)" [class.simple-rule]="isSimpleRule(rule)"
        [class.complex-rule]="!isSimpleRule(rule)">

        <div class="rule-content">
            <div class="rule-details">
                <div class="rule-header">
                    <h3 class="rule-name">{{ rule.name }}</h3>
                    <span class="priority">Priorité: {{ rule.priority }}</span>
                    <span class="status" [class.status-active]="rule.status === 'active'"
                        [class.status-inactive]="rule.status === 'inactive'">
                        {{ rule.status === 'active' ? 'Active' : 'Inactive' }}
                    </span>
                </div>

                <!-- Summary Preview for Simple Rules -->
                <div *ngIf="isSimpleRule(rule) && hasRuleSummary(rule.id)" class="summary-preview"
                    (click)="handleToggleExpandRule(rule.id)">
                    <div class="summary-content">
                        <mat-icon class="summary-icon">summarize</mat-icon>
                        <p class="summary-text">
                            {{ getRuleSummary(rule.id) | slice:0:120 }}{{ getRuleSummary(rule.id).length > 120 ? '...' :
                            '' }}
                        </p>
                    </div>
                </div>

                <!-- Detailed Stats for Complex Rules or Expanded View -->
                <div *ngIf="!isSimpleRule(rule) || expandedRuleId === rule.id" class="usage-stats">
                    <div class="stat-item">
                        <mat-icon class="stat-icon">location_city</mat-icon>
                        <div>
                            <div class="stat-value">{{ rule.totalSites }}</div>
                            <div class="stat-label">Sites</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <mat-icon class="stat-icon">people</mat-icon>
                        <div>
                            <div class="stat-value">{{ getTotalClientsForRule(rule) }}</div>
                            <div class="stat-label">Clients</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <mat-icon class="stat-icon">devices</mat-icon>
                        <div>
                            <div class="stat-value">{{ getTotalControllersForRule(rule) }}</div>
                            <div class="stat-label">Contrôleurs</div>
                        </div>
                    </div>
                </div>

                <!-- Tags Section -->
                <div class="tags" *ngIf="!isSimpleRule(rule) || expandedRuleId === rule.id">
                    <span *ngFor="let tag of rule.tags" class="tag">#{{ tag }}</span>
                    <div *ngIf="rule.tags.length === 0" class="no-tags">Aucun tag disponible</div>
                </div>

                <!-- Actions Section -->
                <div class="actions-triggered" *ngIf="!isSimpleRule(rule) || expandedRuleId === rule.id">
                    <div class="actions-list">
                        <span class="action-label">Actions:</span>
                        <span *ngIf="!rule.actions || rule.actions.length === 0" class="no-actions">
                            Aucune action définie
                        </span>
                        <span *ngFor="let action of rule.actions" class="action-bubble">
                            {{ action.type }}
                            <span *ngIf="action.action"> ({{ action.action }}</span>
                            <span *ngIf="action.value">: {{ action.value }})</span>
                            <span *ngIf="!action.action && !action.value">)</span>
                            <span *ngIf="action.target"> sur {{ action.target }}</span>
                        </span>
                    </div>
                    <div class="last-triggered">
                        <mat-icon style="font-size: 14px;">schedule</mat-icon>
                        Dernier déclenchement:
                        <span class="timestamp">{{ rule.lastTriggered || "Jamais" }}</span>
                    </div>
                </div>
            </div>

            <!-- Rule Actions (Buttons) -->
            <div class="rule-actions">
                <button mat-icon-button (click)="handleToggleExpandRule(rule.id)"
                    [matTooltip]="expandedRuleId === rule.id ? 'Masquer les détails' : 'Voir les détails'"
                    [disabled]="isRuleActionPending(rule.id)">
                    <mat-icon>{{ expandedRuleId === rule.id ? "expand_less" : "expand_more" }}</mat-icon>
                </button>
                <button mat-icon-button color="primary" (click)="handleEditRule(rule.id)" matTooltip="Modifier la règle"
                    [disabled]="isRuleActionPending(rule.id)">
                    <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button color="warn" (click)="handleDeleteRule(rule.id)" matTooltip="Supprimer la règle"
                    [disabled]="isRuleActionPending(rule.id)">
                    <mat-icon *ngIf="!isRuleActionPending(rule.id)">delete</mat-icon>
                    <mat-icon class="loading-icon" *ngIf="isRuleActionPending(rule.id)">hourglass_empty</mat-icon>
                </button>
            </div>
        </div>

        <!-- Expanded Details Section -->
        <div *ngIf="expandedRuleId === rule.id" class="expanded-details">
            <!-- Tab Navigation -->
            <div class="tab-navigation">
                <button class="tab-button" [ngClass]="{ 'active': activeTab === 'summary' }"
                    (click)="handleSetActiveTab('summary')">
                    <mat-icon>summarize</mat-icon>
                    Résumé IA
                </button>
                <button class="tab-button" [ngClass]="{ 'active': activeTab === 'hierarchy' }"
                    (click)="handleSetActiveTab('hierarchy')">
                    <mat-icon>account_tree</mat-icon>
                    Hiérarchie & Performances
                </button>
                <button class="tab-button" [ngClass]="{ 'active': activeTab === 'rawdata' }"
                    (click)="handleSetActiveTab('rawdata')">
                    <mat-icon>code</mat-icon>
                    Données Brutes (JSON)
                </button>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Summary Tab -->
                <div *ngIf="activeTab === 'summary'" class="summary-tab">
                    <div class="summary-viewer">
                        <div class="summary-header">
                            <h4>
                                <mat-icon style="vertical-align: middle; margin-right: 8px;">summarize</mat-icon>
                                Résumé Intelligent de la Règle
                            </h4>
                        </div>

                        <div class="summary-content-display" *ngIf="hasRuleSummary(rule.id)">
                            <div class="summary-text-content">{{ getRuleSummary(rule.id) }}</div>
                        </div>

                        <div class="summary-empty-state" *ngIf="!hasRuleSummary(rule.id)">
                            <mat-icon class="empty-icon">smart_toy</mat-icon>
                            <h5>Résumé non disponible</h5>
                            <p>Cette règle n'a pas encore de résumé généré par l'IA. Le résumé sera automatiquement créé
                                lors de
                                la prochaine sauvegarde de la règle.</p>
                        </div>

                        <div class="summary-info">
                            <div class="info-item">
                                <mat-icon class="info-icon">info</mat-icon>
                                <span>Ce résumé est généré automatiquement par l'IA pour vous aider à comprendre
                                    rapidement le but
                                    et le fonctionnement de la règle.</span>
                            </div>
                            <div class="info-item" *ngIf="hasRuleSummary(rule.id)">
                                <mat-icon class="info-icon">schedule</mat-icon>
                                <span>Dernière mise à jour: {{ rule.lastTriggered || 'Date de création de la règle'
                                    }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hierarchy Tab -->
                <div *ngIf="activeTab === 'hierarchy'" class="hierarchy-tab">
                    <div class="hierarchy">
                        <h4>
                            <mat-icon style="vertical-align: middle; margin-right: 8px;">account_tree</mat-icon>
                            Hiérarchie des Clients & Contrôleurs
                        </h4>

                        <div *ngIf="rule.clients.length === 0" class="hierarchy-empty-state">
                            Aucun client, site, localisation ou contrôleur associé à cette règle.
                        </div>

                        <div *ngFor="let client of rule.clients" class="client-node">
                            <div class="node-header">
                                <mat-icon>business</mat-icon>
                                <span><strong>Client:</strong> {{ client.name }}</span>
                            </div>
                            <div class="node-children">
                                <div *ngFor="let site of client.sites" class="site-node">
                                    <div class="node-header">
                                        <mat-icon>location_city</mat-icon>
                                        <span><strong>Site:</strong> {{ site.name }} <em>({{ site.address
                                                }})</em></span>
                                    </div>
                                    <div class="node-children">
                                        <div *ngFor="let location of site.locations" class="location-node">
                                            <div class="node-header">
                                                <mat-icon>room</mat-icon>
                                                <span><strong>Localisation:</strong> {{ location.name }}</span>
                                            </div>
                                            <div class="node-children">
                                                <div *ngFor="let controller of location.controllers"
                                                    class="controller-node">
                                                    <div class="node-header controller-header"
                                                        [ngClass]="{ 'selected': selectedControllerId === controller.id }"
                                                        (click)="handleSelectController(controller.id)">
                                                        <span><strong>Contrôleur:</strong> {{ controller.name }}</span>
                                                        <span class="controller-status-green">{{ controller.status
                                                            }}</span>
                                                        <mat-icon class="arrow-icon">
                                                            {{ selectedControllerId === controller.id ?
                                                            'keyboard_arrow_up' :
                                                            'keyboard_arrow_down' }}
                                                        </mat-icon>
                                                    </div>

                                                    <!-- Controller Details Panel -->
                                                    <div *ngIf="selectedControllerId === controller.id"
                                                        class="controller-details-panel">
                                                        <div class="detail-row">
                                                            <strong>Modèle:</strong> {{ controller.model || 'Non
                                                            spécifié' }}
                                                        </div>
                                                        <div class="detail-row">
                                                            <strong>Dernière vue:</strong> {{
                                                            formatTimestamp(controller.lastSeen) || 'Jamais' }}
                                                        </div>
                                                        <div class="detail-row">
                                                            <strong>Statut:</strong>
                                                            <span class="controller-status-green">
                                                                {{ controller.status }}
                                                            </span>
                                                        </div>

                                                        <h5>
                                                            <mat-icon
                                                                style="vertical-align: middle; margin-right: 4px;">history</mat-icon>
                                                            Historique des Exécutions (Récentes)
                                                        </h5>

                                                        <!-- Updated Applications/Executions Table -->
                                                        <div class="applications-table"
                                                            [ngClass]="{ 'empty-state': !controller.executions || controller.executions.length === 0 }">
                                                            <div
                                                                *ngIf="controller.executions && controller.executions.length > 0">
                                                                <div class="table-header">
                                                                    <span>Date/Heure</span>
                                                                    <span>Statut</span>
                                                                    <span>Contrôleur ID</span>
                                                                </div>
                                                                <div *ngFor="let execution of controller.executions | slice:0:pagedExecutionsPageSize"
                                                                    class="table-row">
                                                                    <span>{{
                                                                        formatTimestamp(execution.ExecutionTimestamp)
                                                                        }}</span>
                                                                    <span>
                                                                        <mat-icon
                                                                            [color]="execution.IsSuccess ? 'primary' : 'warn'">
                                                                            {{ execution.IsSuccess ? 'check_circle' :
                                                                            'cancel' }}
                                                                        </mat-icon>
                                                                        {{ execution.IsSuccess ? 'Succès' : 'Échec' }}
                                                                    </span>
                                                                    <span>{{ getControllerName(rule,
                                                                        execution.ControllerId) }}</span>
                                                                </div>

                                                                <!-- Pagination Footer for Controller Executions -->
                                                                <div class="executions-pagination-footer"
                                                                    *ngIf="getControllerExecutionsPageCount(rule.id, controller.id) > 1">
                                                                    <button mat-icon-button
                                                                        (click)="onControllerExecutionsPageChange(rule.id, controller.id, getControllerExecutionsCurrentPage(rule.id, controller.id) - 1)"
                                                                        [disabled]="getControllerExecutionsCurrentPage(rule.id, controller.id) === 1">
                                                                        <mat-icon>chevron_left</mat-icon>
                                                                    </button>
                                                                    <span>Page {{
                                                                        getControllerExecutionsCurrentPage(rule.id,
                                                                        controller.id) }} /
                                                                        {{ getControllerExecutionsPageCount(rule.id,
                                                                        controller.id) }}</span>
                                                                    <button mat-icon-button
                                                                        (click)="onControllerExecutionsPageChange(rule.id, controller.id, getControllerExecutionsCurrentPage(rule.id, controller.id) + 1)"
                                                                        [disabled]="getControllerExecutionsCurrentPage(rule.id, controller.id) === getControllerExecutionsPageCount(rule.id, controller.id)">
                                                                        <mat-icon>chevron_right</mat-icon>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            <div *ngIf="!controller.executions || controller.executions.length === 0"
                                                                class="empty-message">
                                                                Aucune exécution récente enregistrée
                                                            </div>
                                                        </div>

                                                        <h5>
                                                            <mat-icon
                                                                style="vertical-align: middle; margin-right: 4px;">analytics</mat-icon>
                                                            Performance Quotidienne (30 derniers jours)
                                                        </h5>

                                                        <!-- Updated Performance Chart using Chart.js -->
                                                        <div class="performance-chart">
                                                            <ng-container
                                                                *ngIf="controllerCharts[controller.id] && controllerCharts[controller.id].labels?.length">
                                                                <canvas baseChart
                                                                    [data]="controllerCharts[controller.id]"
                                                                    [options]="barChartOptions" [type]="barChartType">
                                                                </canvas>
                                                            </ng-container>
                                                            <div *ngIf="!controllerCharts[controller.id] || !controllerCharts[controller.id].labels?.length"
                                                                class="chart-grid empty-state">
                                                                <div class="empty-message">Aucune donnée de performance
                                                                    disponible</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Raw Data Tab -->
                <div *ngIf="activeTab === 'rawdata'" class="rawdata-tab">
                    <div class="rawdata-viewer">
                        <div class="rawdata-header">
                            <h4>
                                <mat-icon style="vertical-align: middle; margin-right: 8px;">code</mat-icon>
                                Données Brutes de la Règle
                            </h4>
                            <div class="rawdata-actions">
                                <button mat-button (click)="handleCopyRawData(rule.id)" class="copy-button">
                                    <mat-icon>content_copy</mat-icon>
                                    Copier
                                </button>
                                <button mat-button (click)="handleDownloadRawData(rule)" class="download-button">
                                    <mat-icon>download</mat-icon>
                                    Télécharger
                                </button>
                            </div>
                        </div>

                        <div class="json-viewer">
                            <pre class="json-content">{{ formatRawData(rule.id) }}</pre>
                        </div>

                        <div class="rawdata-info">
                            <div class="info-item">
                                <mat-icon class="info-icon">info</mat-icon>
                                <span>Cette section affiche la structure JSON brute telle qu'elle est stockée dans la
                                    base de
                                    données.</span>
                            </div>
                            <div class="info-item">
                                <mat-icon class="info-icon">warning</mat-icon>
                                <span>Modification directe non recommandée. Utilisez l'éditeur de règles pour les
                                    modifications.</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div *ngIf="isLoading === true" class="search-loading-message">
    <mat-icon class="loading-icon">hourglass_empty</mat-icon>
    <span>Chargement des règles...</span>
</div>