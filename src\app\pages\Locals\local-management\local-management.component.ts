import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import {
  FormsModule,
  ReactiveFormsModule,
  FormGroup,
  FormControl,
  Validators,
} from '@angular/forms';
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';
import { Local } from '@app/core/models/local';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { CardLocalComponent } from '@app/components/card-local/card-local.component';
import {
  LocalApiService,
  TypeLocalApiService,
} from '@app/core/services/administrative/local.service';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatIconModule } from '@angular/material/icon';
import { SiteApiService } from '@app/core/services/administrative/site.service';
import { Site } from '@app/core/models/site';
import { FilterParam, Lister, Page } from '@app/core/models/util/page';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS,
} from 'ng-angular-popup';
import { NavigationService } from '@app/core/services/Navigation.Service';

interface IndexableLocal extends Local {
  [key: string]: any;
}

@Component({
  selector: 'app-local-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GenericTableComponent,
    CardLocalComponent,
    MatPaginatorModule,
    MatIconModule,
    MatDialogModule,
    NgxUiLoaderModule,
    NgToastComponent,
    // NgxSpinnerModule
  ],
  templateUrl: './local-management.component.html',
  styleUrls: ['./local-management.component.css'],
  animations: [
    trigger('tableRowAnimation', [
      state('void', style({ opacity: 0, transform: 'translateY(20px)' })),
      transition('void => *', animate('300ms ease-in')),
    ]),
    trigger('fadeIn', [
      state('void', style({ opacity: 0 })),
      transition('void => *', animate('400ms 300ms ease-in')),
    ]),
  ],
})
export class LocalManagementComponent implements OnInit {
  TOAST_POSITIONS = TOAST_POSITIONS;

  constructor(
    private readonly localService: LocalApiService,
    private readonly typeLocalService: TypeLocalApiService,
    private readonly siteService: SiteApiService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly dialog: MatDialog,
    private readonly ngxUiLoaderService: NgxUiLoaderService,
    private readonly toast: NgToastService,
    private readonly navigationService: NavigationService // private readonly spinner: NgxSpinnerService
  ) {}

  downloadedImageBase64: string | null = null;
  locals: Local[] = [];
  filteredLocals: IndexableLocal[] = [];
  sites: any[] = [];
  searchTerm: string = '';
  isLoading: boolean = true;
  showCreateForm: boolean = false;
  showEditForm: boolean = false;
  uploadedImages: File[] = [];
  uploadedImages2D: File[] = [];
  viewMode: 'cards' | 'table' = 'cards';
  currentPage: number = 0;
  pageSize: number = 5;
  totalCount: number = 0;
  paginatedLocals: Local[] = [];
  isSubmitting: boolean = false;
  siteId: string | null = null;
  selectedLocal: Local | null = null;
  currentSite: Site | null = null;
  showDashboard: boolean = false;
  formSubmitted: boolean = false;
  imageUrl?: string | null;

  typeLocals: any[] = [];

  headers: string[] = ['Nom', 'Étage', 'Capteur', 'Capacité', 'Type'];
  keys: string[] = [
    'Name',
    'Floor',
    'SensorsCount',
    'Capacity',
    'TypeLocal.Nom',
  ];

  createLocalForm = new FormGroup({
    Name: new FormControl('', [Validators.required]),
    Description: new FormControl(''),
    Floor: new FormControl<number>(0),
    SensorsCount: new FormControl<number>(0),
    Capacity: new FormControl<number>(0),
    Architecture2DImage: new FormControl(''),
    ImageLocal: new FormControl(''),
    Latitude: new FormControl<number>(0),
    Longtitude: new FormControl<number>(0),
    TypeLocalId: new FormControl('', [Validators.required]),
    IdSite: new FormControl('', [Validators.required]),
  });

  editLocalForm = new FormGroup({
    Name: new FormControl('', [Validators.required]),
    Description: new FormControl(''),
    Floor: new FormControl<number>(0),
    SensorsCount: new FormControl<number>(0),
    Capacity: new FormControl<number>(0),
    Architecture2DImage: new FormControl(''),
    ImageLocal: new FormControl(''),
    Latitude: new FormControl<number>(0),
    Longtitude: new FormControl<number>(0),
    TypeLocalId: new FormControl('', [Validators.required]),
    IdSite: new FormControl('', [Validators.required]),
  });

  ngOnInit(): void {
    this.loadLocals();
    this.loadSiteDetails();
    this.loadSites();
    this.loadTypeLocals();

    this.route.queryParams.subscribe((params) => {
      if (params['action'] === 'create') {
        this.showAddLocalForm();
      }
    });
  }

  onImageError(event: any): void {
    this.downloadedImageBase64 = null;
  }

  loadSiteDetails(): void {
    const siteId = this.route.snapshot.paramMap.get('siteId');
    if (siteId) {
      this.siteService.getById(siteId).subscribe({
        next: (site: Site) => {
          this.loadSiteImage(siteId);
          this.currentSite = site;
          console.log(this.currentSite, 'oualid zamel');
        },
        error: (error) => {
          console.error('Error loading site details:', error);
        },
      });
    }
  }

  loadTypeLocals(): void {
    this.typeLocalService.getAll().subscribe({
      next: (types) => {
        this.typeLocals = types;
      },
      error: (error) => {
        console.error('Error loading type locals:', error);
        this.typeLocals = [];
      },
    });
  }

  loadSiteImage(id: string): void {
    this.siteService.downloadImage(id).subscribe({
      next: (response) => {
        if (response) {
          if (response.image != null) {
            this.imageUrl = `data:image/jpeg;base64,${response.image}`;
          } else {
            this.imageUrl = null;
          }
        }
      },
      error: (error) => {
        console.error('Error loading site image:', error);
        this.imageUrl = null;
      },
    });
  }

  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'cards' ? 'table' : 'cards';
    this.updatePaginatedLocals();
  }

  updatePaginatedLocals(): void {
    const startIndex = this.currentPage * this.pageSize;
    this.paginatedLocals = this.filteredLocals.slice(
      startIndex,
      startIndex + this.pageSize
    );
  }

  loadSites(): void {
    this.isLoading = true;
    this.siteService.getAll().subscribe({
      next: (sites) => {
        this.sites = sites;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading sites:', error);
        this.isLoading = false;
      },
    });
  }

  getSiteNameById(siteId: string | null | undefined): string {
    if (!siteId) return '';
    const site = this.sites.find((s) => s.Id === siteId || s.id === siteId);
    if (!site) return '';
    return (
      (site.Name || site.name) +
      (site.Adress || site.adress
        ? ' (' + (site.Adress || site.adress) + ')'
        : '')
    );
  }

  loadLocals(): void {
    const siteId = this.route.snapshot.paramMap.get('siteId');
    this.isLoading = true;
    this.ngxUiLoaderService.start();
    // this.spinner.show();

    const request: Lister = {
      Pagination: {
        CurrentPage: this.currentPage + 1,
        PageSize: this.pageSize,
        totalElement: 0,
      },
      FilterParams: [
        {
          Column: 'IdSite',
          Value: siteId ?? '',
          Op: 'eq',
        },
      ],
    };

    this.localService.gatePage(request).subscribe({
      next: (response: Page<Local>) => {
        // Process images before assigning to locals
        this.locals = (response.Content ?? []).map((local) => ({
          ...local,
          // Convert string images to proper base64 format if they exist
          Architecture2DImage: local.Architecture2DImage
            ? this.ensureBase64Format(local.Architecture2DImage)
            : '',
          ImageLocal: local.ImageLocal
            ? this.ensureBase64Format(local.ImageLocal)
            : '',
        }));
        this.filteredLocals = this.locals;
        // if (this.locals.length > 0 && this.currentSite) {
        //   this.currentSite.LocalsCount = this.locals.length;
        //   this.siteService.update(this.currentSite).subscribe({
        //     next: (site) => {
        //       this.currentSite = site;
        //     },
        //     error: (error) => {
        //       console.error('Error updating site:', error);
        //     }
        //   });
        // }
        this.totalCount = response.Lister?.Pagination?.TotalElement ?? 0;
        this.updatePaginatedLocals();
        this.isLoading = false;
        this.ngxUiLoaderService.stop(); // Stop the loader
      },
      error: (error) => {
        console.error('Error loading locals:', error);
        // this.showError('Erreur lors du chargement des locaux', 'Erreur');
        this.filteredLocals = [];
        this.totalCount = 0;
        this.isLoading = false;
        this.ngxUiLoaderService.stop(); // Stop the loader
      },
    });
  }

  // Add helper method to ensure proper base64 format
  private ensureBase64Format(imageData: string): string {
    // Remove any existing data:image prefix if present
    const base64Data = imageData.includes('base64,')
      ? imageData.split('base64,')[1]
      : imageData;

    // Clean up any whitespace or invalid characters
    return base64Data.trim();
  }

  // Add helper methods for toast notifications
  // private showSuccess(message: string, title: string) {
  //   this.toast.success(message, title, 3000, false);
  // }

  // private showError(message: string, title: string) {
  //   this.toast.warning(message, title, 3000, false);
  // }

  private refreshData(): void {
    // Reset pagination to show the latest changes
    this.currentPage = 0;

    // Clear search to see all results
    const wasSearching = this.searchTerm.trim() !== '';

    // If there was a search, maintain it, otherwise load all
    if (wasSearching) {
      this.filterLocals();
    } else {
      this.loadLocals();
    }
  }

  submitEditForm(): void {
    if (this.editLocalForm.valid && this.selectedLocal) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Confirmer la modification',
          message: 'Voulez-vous vraiment modifier ce local ?',
          icon: 'edit',
        },
      });

      dialogRef.afterClosed().subscribe((result) => {
        if (result && this.selectedLocal) {
          this.isSubmitting = true;
          const formValues = this.editLocalForm.value;

          const updatedLocal: Local = {
            Id: this.selectedLocal.Id,
            CreatedAt: this.selectedLocal.CreatedAt,
            CreatedBy: this.selectedLocal.CreatedBy,
            LastUpdatedAt: new Date(),
            LastUpdatedBy: 'system',
            Name: formValues.Name || this.selectedLocal.Name,
            Floor: Number(formValues.Floor) || this.selectedLocal.Floor,
            SensorsCount:
              Number(formValues.SensorsCount) ||
              this.selectedLocal.SensorsCount,
            Capacity:
              Number(formValues.Capacity) || this.selectedLocal.Capacity,
            Architecture2DImage: this.ensureBase64Format(
              formValues.Architecture2DImage ||
                this.selectedLocal.Architecture2DImage ||
                ''
            ),
            ImageLocal: this.ensureBase64Format(
              formValues.ImageLocal || this.selectedLocal.ImageLocal || ''
            ),
            Description:
              formValues.Description || this.selectedLocal.Description,
            IdSite: formValues.IdSite || this.selectedLocal.IdSite,
            TypeLocalId:
              formValues.TypeLocalId || this.selectedLocal.TypeLocalId,
            Latitude:
              Number(formValues.Latitude) || this.selectedLocal.Latitude,
            Longtitude:
              Number(formValues.Longtitude) || this.selectedLocal.Longtitude,
          };

          this.localService.update(updatedLocal).subscribe({
            next: () => {
              if (updatedLocal.ImageLocal) {
                this.localService
                  .uploadImage(updatedLocal.Id!, updatedLocal.ImageLocal)
                  .subscribe({
                    next: () => {
                      this.refreshData(); // Change this line
                      this.showSuccessToast('Local modifié avec succès.');
                    },
                    error: () => {
                      this.refreshData(); // Add this line
                      // this.showErrorToast('Local modifié mais erreur upload image.');
                    },
                  });
              } else {
                this.refreshData(); // Change this line
                this.showSuccessToast('Local modifié avec succès.');
              }
              this.hideEditLocalForm();
              this.isSubmitting = false;
            },
            error: (error) => {
              this.showErrorToast('Erreur lors de la modification du local.');
              this.isSubmitting = false;
            },
          });
        }
      });
    }
  }

  onPageChange(event: any): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadLocals();
  }

  filterLocals(): void {
    const siteId = this.route.snapshot.paramMap.get('siteId');
    const searchTerm = this.searchTerm.trim();

    const filters: FilterParam[] = [];

    if (siteId) {
      filters.push({
        Column: 'IdSite',
        Value: siteId,
        Op: 'eq',
      });
    }

    if (searchTerm) {
      const searchableFields = ['Name'];
      searchableFields.forEach((field, idx) => {
        filters.push({
          Column: field,
          Value: searchTerm,
          Op: 'contains',
          AndOr: idx === 0 ? 'AND' : 'OR',
        });
      });
    }

    const request: Lister = {
      Pagination: {
        CurrentPage: this.currentPage + 1,
        PageSize: this.pageSize,
        totalElement: 0,
      },
      FilterParams: filters,
    };

    this.isLoading = true;
    this.ngxUiLoaderService.start();

    this.localService.gatePage(request).subscribe({
      next: (response: Page<Local>) => {
        this.filteredLocals = response.Content ?? [];
        this.totalCount = response.Lister?.Pagination?.TotalElement ?? 0;
        this.updatePaginatedLocals();
        this.isLoading = false;
        this.ngxUiLoaderService.stop();
        // this.spinner.hide();
      },

      error: (error) => {
        console.error('Error during backend filtering:', error);
        // this.showError('Erreur serveur — affichage complet sans filtre.', 'Erreur');
        this.loadLocals();
      },
    });
  }

  generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      }
    );
  }

  submitCreateForm(): void {
    this.formSubmitted = true;

    if (this.createLocalForm.valid) {
      this.isSubmitting = true;
      const formValues = this.createLocalForm.value;
      const local: Local = {
        Id: this.generateUUID(),
        Name: formValues.Name ?? '',
        Description: formValues.Description ?? '',
        Floor: formValues.Floor ?? 0,
        SensorsCount: formValues.SensorsCount ?? 0,
        Capacity: formValues.Capacity ?? 0,
        Architecture2DImage: formValues.Architecture2DImage ?? '',
        ImageLocal: formValues.ImageLocal ?? '',
        IdSite: formValues.IdSite ?? '',
        Latitude: 0,
        Longtitude: 0,
        TypeLocalId: formValues.TypeLocalId ?? '',
      };

      this.localService.create(local).subscribe({
        next: () => {
          if (local.ImageLocal) {
            this.localService
              .uploadImage(local.Id, local.ImageLocal)
              .subscribe({
                next: () => {
                  this.refreshData(); // Change this line
                  this.showSuccessToast('Local créé avec succès.');
                },
                error: (err) => {
                  console.error('Erreur upload image :', err);
                  this.refreshData(); // Add this line
                  this.showErrorToast(
                    "Création réussie mais erreur d'upload image."
                  );
                },
              });
          } else {
            this.refreshData(); // Change this line
            this.showSuccessToast('Local créé avec succès.');
          }
          this.hideAddLocalForm();
          this.isSubmitting = false;
          this.formSubmitted = false;
        },
        error: (error) => {
          this.isSubmitting = false;
          this.showErrorToast('Erreur lors de la création du local.');
          console.error('Erreur création local :', error);
        },
      });
    } else {
      const firstInvalidControl = document.querySelector('.ng-invalid');
      if (firstInvalidControl) {
        firstInvalidControl.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }
    }
  }

  deleteLocal(id?: string): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Confirmer la suppression',
        message: 'Voulez-vous vraiment supprimer ce local ?',
        icon: 'delete',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.localService.delete(id).subscribe({
          next: () => {
            this.refreshData(); // Change this line
            this.showSuccessToast('Local supprimé avec succès.');
          },
          error: (error) => {
            this.showErrorToast('Erreur lors de la suppression du local.');
          },
        });
      }
    });
  }

  viewDetails(id?: string): void {
    this.router.navigate(['/local-details/', id]);
  }

  showAddLocalForm(): void {
    this.showCreateForm = true;
    this.createLocalForm.reset();
    const siteIdFromRoute = this.route.snapshot.paramMap.get('siteId');
    let siteIdToSet = '';
    if (siteIdFromRoute) {
      siteIdToSet = siteIdFromRoute;
    } else if (this.sites && this.sites.length > 0) {
      siteIdToSet = this.sites[0].Id || this.sites[0].id;
    }
    if (siteIdToSet) {
      this.createLocalForm.patchValue({ IdSite: siteIdToSet });
    }
  }

  hideAddLocalForm(): void {
    this.showCreateForm = false;
    this.formSubmitted = false;
  }

  showEditLocalForm(local: Local): void {
    this.selectedLocal = local;

    // télécharger l'image réelle depuis l'API
    this.localService.downloadImage(local.Id!).subscribe({
      next: (res) => {
        if (res?.image) {
          const imageBase64 = res.image;
          this.selectedLocal!.ImageLocal = imageBase64;
          this.editLocalForm.patchValue({ ImageLocal: imageBase64 });
        }
      },
      error: (err) => {
        console.error(
          "Erreur lors du téléchargement de l'image du local :",
          err
        );
      },
    });

    this.editLocalForm.patchValue({
      Name: local.Name,
      Description: local.Description,
      Floor: local.Floor,
      SensorsCount: local.SensorsCount,
      Capacity: local.Capacity,
      Architecture2DImage: local.Architecture2DImage,
      // ImageLocal est mis à jour ci-dessus après download
      Latitude: local.Latitude,
      Longtitude: local.Longtitude,
      TypeLocalId: local.TypeLocalId,
      IdSite: local.IdSite,
    });

    this.showEditForm = true;
    this.updatePaginatedLocals();
  }

  hideEditLocalForm(): void {
    this.showEditForm = false;
    this.selectedLocal = null;
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.filterLocals();
  }

  editLocal(local: Local): void {
    this.showEditLocalForm(local);
  }

  private showSuccessToast(message: string, title: string = 'Succès') {
    this.toast.success(message, title, 3000, false);
  }

  private showErrorToast(message: string, title: string = 'Erreur') {
    this.toast.warning(message, title, 3000, false);
  }

  onImagesSelected(event: any, fieldName: string): void {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        const base64Data = result.includes('base64,')
          ? result.split('base64,')[1]
          : result;

        if (this.showCreateForm) {
          this.createLocalForm.patchValue({
            [fieldName]: base64Data,
          });
        } else if (this.showEditForm && this.selectedLocal) {
          this.editLocalForm.patchValue({
            [fieldName]: base64Data,
          });
        }
      };
      reader.readAsDataURL(file);
    }
  }

  handleAction(event: { action: string; row: any }): void {
    const { action, row } = event;
    if (action === 'edit') {
      this.editLocal(row);
    } else if (action === 'delete') {
      this.deleteLocal(row.Id);
    } else if (action === 'view') {
      this.viewDetails(row.Id);
    }
  }

  goBackToOrganisation(): void {
    const previous = this.navigationService.getPrevious();
    if (previous) {
      this.router.navigateByUrl(previous.url).then(() => {
        // Delay scroll restoration to ensure DOM is rendered
        setTimeout(() => {
          window.scrollTo({ top: previous.position, behavior: 'smooth' });
        }, 100);
      });
    } else {
      this.router.navigate(['/']);
    }
  }

  toggleDashboard(): void {
    this.showDashboard = !this.showDashboard;
  }
}
