import { Component, HostListener } from '@angular/core';

import { LucideAngularModule } from 'lucide-angular';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InstalationService } from '@app/core/services/instalation.service';
import {
  DragDropModule,
  CdkDragDrop,
  CdkDragStart,
  CdkDragEnd,
} from '@angular/cdk/drag-drop';

import { MatSnackBar } from '@angular/material/snack-bar';
import * as fabric from 'fabric';
import { MatIconModule } from '@angular/material/icon';
import { CustomMqttService } from '../../core/services/custom-mqtt.service';
import { Subscription } from 'rxjs';
import {
  Zigbee2MqttService,
  ZigbeeDevice,
} from '../../core/services/mqtt.service';
import { Capteur } from '@app/core/models/capteur';
import { CapteurApiService } from '@app/core/services/administrative/capteur.service';
import { ControllerApiService } from '@app/core/services/administrative/controller.service';
import { TypeCapteurApiService } from '@app/core/services/administrative/typecapteur.service';
import { Transaction } from '@app/core/models/transaction';
import { TransactionApiService } from '@app/core/services/administrative/transaction.service';
import { TypeCapteur } from '@app/shared/models/typeCapteur';
import { Client } from '@app/core/models/client';
import { Site } from '@app/core/models/site';
import { Local } from '@app/core/models/local';
import { Controller } from '@app/core/models/controller';
import { VariablesApiService } from '@app/core/services/administrative/variables.service';
import { Variables } from '@app/core/models/variables';
import { ClientLicenceControllerView } from '@app/shared/models/clientLicenceControllerView';

// Interface pour une lecture de capteur sécurisée
interface SafeSensorReading {
  Value: number | string;
  Unit?: string;
  Timestamp: Date;
}
// Interface pour le résultat d'installation
interface InstallationResult {
  success: boolean;
  totalCapteurs: number;
  successfulInstalls: number;
  failedInstalls: number;
  transactions: Transaction[];
  errors: any[];
  timestamp: Date;
}
@Component({
  standalone: true,
  selector: 'app-install-caplteur',
  imports: [
    CommonModule,
    FormsModule,
    DragDropModule,
    LucideAngularModule,
    MatIconModule,
  ],
  templateUrl: './install-caplteur.component.html',
  styleUrl: './install-caplteur.component.css',
})
export class InstallCaplteurComponent {
  canvas!: fabric.Canvas;
  canvasInitialized = false;
  selectedTool = '';
  planType = 'local';
  isGridVisible = true;

  // Palette de couleurs
  selectedColor = '#3B82F6';
  selectedStrokeColor = '#374151';
  selectedStrokeWidth = 2;
  capteur!: Capteur;

  peredCapteurs: Capteur[] = [];
  peredCapteurs2: Capteur[] = [];
  instaledCapteurs: Capteur[] = [];
  typeCapteurs: TypeCapteur[] = [];

  // Nouvelles propriétés pour les améliorations
  selectedCapteurForDetails: Capteur | null = null;
  showCapteurDetails = false;
  isDraggingCapteur = false;
  dragPreview: any = null;
  capteurDetailsPosition = { x: 0, y: 0 };

  // Animation d'appairage
  pairingAnimation = {
    isActive: false,
    progress: 0,
    currentStep: '',
    foundDevices: 0,
  };

  // Track source of installed capteurs
  capteurSourceMap = new Map<
    string,
    'peredCapteurs' | 'peredCapteurs2' | 'Capteurs'
  >();

  // Historique des capteurs placés
  placedCapteurs: Array<{
    capteur: Capteur;
    position: { x: number; y: number };
    canvasObject: fabric.Object;
    timestamp: Date;
  }> = [];

  // Propriété pour compatibilité avec le drop list
  get canvasDropData(): any[] {
    return this.placedCapteurs.map((pc) => pc.capteur);
  }

  constructor(
    private installService: InstalationService,
    readonly mqttService: CustomMqttService,
    private zigbeeService: Zigbee2MqttService,
    private controlerService: ControllerApiService,
    private typeCapteurService: TypeCapteurApiService,
    private transactionService: TransactionApiService,
    private capteursServices: CapteurApiService,
    private snackBar: MatSnackBar,
    private variabaleService: VariablesApiService
  ) {}
  generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      }
    );
  }

  variables: Variables[] = [];
  // Propriétés pour l'animation
  particles: Array<{ x: number; delay: number }> = [];
  ngOnInit() {
    this.loadInitialData();
    // this.setupMqttSubscription();
    this.initializePairingAnimation();
    this.initializePairingAnimation();
    this.generateParticles();

    this.variabaleService.getAll().subscribe((res) => {
      this.variables = res;
      console.log(this.variables);
    });
  }
  // Méthode pour générer les particules
  generateParticles() {
    this.particles = [];
    for (let i = 0; i < 12; i++) {
      this.particles.push({
        x: Math.random() * 300,
        delay: Math.random() * 4,
      });
    }
  }

  /**  addTransaction() {

     const trasactions: Transaction[] = []

     this.instaledCapteurs.forEach(element => {
       const transaction: Transaction = new Transaction();
       transaction.IdCapteur = element.Id;
       transaction.IdController = this.selectedControler.Id;
       transaction.IdLocal = this.selectedLocal.Id;
       trasactions.push(transaction);
     });
     console.log("Transaction :", trasactions)

     this.installService.addTransactions(trasactions).subscribe({
       next: (response) => {
         console.log('Transactions créées:', response);
       },
       error: (error) => {
         console.error('Erreur lors de la création:', error);
       }
     });

   }*/

  loadInitialData() {
    this.installService.getClients().subscribe((data) => {
      this.clients = data;
      this.filteredClients = this.clients;
      console.log(this.clients);
    });

    this.typeCapteurService.getAll().subscribe((data) => {
      this.typeCapteurs = data;
    });
  }

  setupMqttSubscription() {
    if (this.selectedControler.ControllerBaseTopic) {
      this.zigbeeService.subscribeToBridgeLogs(
        this.selectedControler.ControllerBaseTopic
      );
    } else {
      console.warn('ControllerBaseTopic is null');
    }
    this.logsSubscription = this.zigbeeService
      .getBridgeLogs()
      .subscribe((log: string) => {
        this.processNewDeviceLog(log);
      });
  }

  processNewDeviceLog(log: string) {
    console.log('Nouveau log reçu:', log);
    this.logs.unshift(log);

    try {
      const parsedLog = JSON.parse(log);
      const regex = /payload '(.*)'/;
      const match = parsedLog.message.match(regex);

      if (match && match[1]) {
        const payloadStr = match[1];
        const payload = JSON.parse(payloadStr);

        if (
          payload.type === 'device_interview' &&
          payload.data &&
          payload.data.definition
        ) {
          this.handleNewDeviceFound(payload);
        }
      }
    } catch (err) {
      console.error('Erreur lors du parsing JSON MQTT :', err);
    }
  }
  typeCapteur!: TypeCapteur;
  capteurCree: Capteur[] = [];
  //capteurBd=new Capteur();
  handleNewDeviceFound(payload: any) {
    const definition = payload.data.definition;
    this.typeCapteur = this.installService.matchDeviceToTypeCapteur(
      definition.exposes,
      this.typeCapteurs
    );
    console.log('Type Capteur Detecter', this.typeCapteur);
    const message = payload?.message;

    // Animation de découverte de nouveau capteur
    this.animateNewDeviceFound();

    let typeCapteur2 = new TypeCapteur();

    typeCapteur2 = {
      Id: this.typeCapteur.Id,
      DeviceType: this.typeCapteur.DeviceType,
      DisplayName: this.typeCapteur.DisplayName,
      Nom: this.typeCapteur.Nom,
      Topic: this.typeCapteur.Topic,
    } as TypeCapteur;

    console.log(typeCapteur2, 'qsdmqldkqmsdkmqsdkmqsdkmsq');

    this.capteur = {
      Id: this.generateUUID(),
      Protocol: 'Zigbee',
      Manufacturer: definition.vendor || '',
      Model: definition.model || '',
      ModelIdentifier: definition.model || '',
      FriendlyName: payload.data.friendly_name || '',
      LastSeen: new Date(),
      IeeeAddress: payload.data.ieee_address || '',
      NetworkAddress: 0,
      Endpoint: 1,
      NodeId: 0,
      HomeId: 0,
      SecurityClasses: '',
      SensorReadings: [],
      Transactions: [],
      RowData: JSON.stringify(payload.data),
      Topic:
        this.selectedControler.ControllerBaseTopic +
        '/' +
        payload.data.friendly_name,
      State: '',
      IdTypeCapteur: this.typeCapteur?.Id || '',
      TypeCapteur: typeCapteur2,
      Brand: definition.vendor || '',
    };
    const capteurBd: Partial<Capteur> = {
      Protocol: 'Zigbee',
      Manufacturer: definition.vendor || '',
      Model: definition.model || '',
      ModelIdentifier: definition.model || '',
      FriendlyName: payload.data.friendly_name || '',
      LastSeen: new Date(),
      IeeeAddress: payload.data.ieee_address || '',
      NetworkAddress: 0,
      Endpoint: 1,
      NodeId: 0,
      HomeId: 0,
      SecurityClasses: '',
      SensorReadings: [],
      Transactions: [],
      RowData: JSON.stringify(payload.data),
      Topic:
        this.selectedControler.ControllerBaseTopic +
        '/' +
        payload.data.friendly_name,
      State: '',
      IdTypeCapteur: this.typeCapteur?.Id || '',
      TypeCapteur: undefined,
      Brand: definition.vendor || '',
    };

    // Vérifie si le capteur existe déjà
    const existeDeja = this.peredCapteurs.some(
      (c) => c.FriendlyName === this.capteur.FriendlyName
    );
    const existeDejaBd = this.Capteurs.some(
      (c) => c.IeeeAddress === this.capteur.IeeeAddress
    );

    this.peredCapteurs.push(this.capteur);
    console.log('Capteur  ajouter au per ', this.capteur);

    if (!existeDeja && !existeDejaBd) {
      //this.capteur.TypeCapteur=typeCapteur|| undefined;
      //   this.peredCapteurs.push(this.capteur);
      console.log('Capteur  ', this.capteur);
      this.snackBar.open('Capteur ajouté avec succès.', 'Fermer', {
        duration: 3000,
      });
    } else if (existeDeja) {
      this.snackBar.open('Ce capteur existe déjà dans la liste.', 'Fermer', {
        duration: 3000,
      });
    } else if (existeDejaBd) {
      this.snackBar.open(
        'Ce capteur existe déjà en base de données.',
        'Fermer',
        { duration: 3000 }
      );
    }

    console.log('Capteeeeur BD ', capteurBd);
    if (this.selectedControler.ControllerId !== null) {
      capteurBd.ControllerId = this.selectedControler?.ControllerId;
    }
    if (this.selectedControler && !existeDeja && !existeDejaBd) {
      capteurBd.TypeCapteur = undefined;
      console.log('Capteeeeur BD a cree', capteurBd);
      this.capteursServices.create(capteurBd).subscribe((res) => {
        console.log('Creation Capteur', res);

        // Only add to peredCapteurs2 if not already installed on canvas
        const isAlreadyInstalled = this.instaledCapteurs.find(
          (installed) =>
            installed.FriendlyName === res.FriendlyName ||
            installed.IeeeAddress === res.IeeeAddress
        );

        if (!isAlreadyInstalled) {
          this.peredCapteurs2.push(res);
        } else {
          console.log(
            'Capteur already installed on canvas, not adding to peredCapteurs2'
          );
        }

        //this.capteurCree.push(res);
        console.log('Capteur cree dans la base :', this.capteurCree);
        this.showSuccessNotification('Nouveau capteur détecté et créé !');
      });
    }
  }

  creeCapteur() {}

  initializePairingAnimation() {
    // Configuration de l'animation d'appairage
    this.pairingAnimation = {
      isActive: false,
      progress: 0,
      currentStep: 'En attente...',
      foundDevices: 0,
    };
  }

  // getCapteurTypeIdById(displayName?: string): TypeCapteur | null {
  //   const capteur = this.typeCapteurs.find(
  //     c => c.Id === displayName
  //   );
  //   return capteur ? capteur : null;
  // }

  // getCapteurTypeByDisplayName(displayName: string): TypeCapteur | null {
  //   const capteur = this.typeCapteurs.find(
  //     c => c.DisplayName.toLowerCase().trim() === displayName.toLowerCase().trim()
  //   );
  //   console.log("Type Capteur by Display name :", capteur)
  //   return capteur ? capteur : null;
  // }

  getIconForCapteur(nom: string): string {
    const map: { [key: string]: string } = {
      'Water Leak Sensor': 'water_drop',
      'Door/Window Sensor': 'sensor_door',
      'Motion Sensor': 'radar',
      'Thermostat': 'device_thermostat',
      'Smart Plug': 'power',
      'Luminance Sensor': 'wb_sunny',
      'Air/Quality Sensor': 'air',
      'Blinds/Curtain Motor': 'tune',
      'Presence Sensor': 'radio_button_checked',
      'Vibration Sensor': 'equalizer',
      'Smart Button': 'circle',
      'Smoke Detector': 'cloud',
      'Light Switch': 'toggle_on',
      'bridge': 'hub',
      'Temperature/Humidity Sensor': 'thermostat',
      'Roller shade driver E1': 'horizontal_rule',
      'Weather Station': 'device_thermostat'
    };
    return map[nom] || 'sensors';
  }

  getIconColorForCapteur(displayName: string): string {
    const map: { [key: string]: string } = {
      'Water Leak Sensor': '#2196f3',
      'Door/Window Sensor': '#4caf50',
      'Motion Sensor': '#ff9800',
      'Thermostat': '#f44336',
      'Smart Plug': '#9c27b0',
      'Luminance Sensor': '#ffc107',
      'Temperature/Humidity Sensor': '#00bcd4',
      'Air Quality Sensor': '#8bc34a',
      'Blinds/Curtain Motor': '#795548',
      'Presence Sensor': '#607d8b',
      'Vibration Sensor': '#e91e63',
      'Smart Button': '#3f51b5',
      'Smoke Detector': '#f44336',
      'Light Switch': '#ffeb3b',
      'bridge': '#9e9e9e',
    };
    return map[displayName] || '#000000';
  }

  ngAfterViewInit(): void {
    this.initializeCanvas();
  }

  initializeCanvas(): void {
    this.canvas = new fabric.Canvas('canvas', {
      backgroundColor: '#FAFAFA',
      width: 900,
      height: 500,
      selection: true,
    });

    this.canvasInitialized = true;
    this.canvas.on('selection:created', () => this.updateToolbar());
    this.canvas.on('selection:cleared', () => this.updateToolbar());
    this.canvas.on('mouse:down', (e) => this.onCanvasMouseDown(e));
  }

  // Nouvelle méthode pour gérer les clics sur le canvas
  onCanvasMouseDown(event: any) {
    const pointer = this.canvas.getPointer(event.e);
    const objects = this.canvas.getObjects();

    // Vérifier si on clique sur un capteur
    const clickedObject = this.canvas.findTarget(event.e);
    if (clickedObject && clickedObject.get('type') === 'capteur') {
      const capteurData = clickedObject.get('capteurData');
      if (capteurData) {
        this.showCapteurDetailsAtPosition(capteurData, pointer.x, pointer.y);
      }
    } else {
      this.hideCapteurDetails();
    }
  }

  // Amélioration du système de drag & drop
  onCapteurDragStart(event: CdkDragStart, capteur: Capteur) {
    this.isDraggingCapteur = true;
    this.selectedCapteur = capteur;

    // Créer un aperçu visuel pendant le drag
    this.createDragPreview(capteur);

    // Animation de début de drag
    const element = event.source.element.nativeElement;
    element.style.transform = 'scale(1.1) rotate(5deg)';
    element.style.zIndex = '1000';

    console.log('Début du drag pour:', capteur.FriendlyName);
  }

  onCapteurDragEnd(event: CdkDragEnd, capteur: Capteur) {
    this.isDraggingCapteur = false;

    // Réinitialiser le style de l'élément
    const element = event.source.element.nativeElement;
    element.style.transform = '';
    element.style.zIndex = '';

    // Vérifier si le drop est sur le canvas
    const canvasElement = document.getElementById('canvas');
    if (canvasElement) {
      const canvasRect = canvasElement.getBoundingClientRect();
      const dropX = event.dropPoint.x;
      const dropY = event.dropPoint.y;

      if (
        dropX >= canvasRect.left &&
        dropX <= canvasRect.right &&
        dropY >= canvasRect.top &&
        dropY <= canvasRect.bottom
      ) {
        // Convertir les coordonnées de l'écran vers le canvas
        const canvasX = dropX - canvasRect.left;
        const canvasY = dropY - canvasRect.top;

        this.dropCapteurOnCanvas(capteur, canvasX, canvasY);
      }
    }

    this.removeDragPreview();
    console.log('Fin du drag pour:', capteur.FriendlyName);
  }

  createDragPreview(capteur: Capteur) {
    // Logique pour créer un aperçu visuel pendant le drag
    console.log("Création de l'aperçu de drag pour:", capteur.FriendlyName);
  }

  removeDragPreview() {
    if (this.dragPreview) {
      this.dragPreview = null;
    }
  }

  // dropCapteurOnCanvas(capteur: Capteur, x: number, y: number) {
  //   // Convertir les coordonnées canvas
  //   const pointer = { x, y };

  //   // Ajouter le capteur au canvas avec animation
  //   this.addCapteurToCanvasWithAnimation(pointer.x, pointer.y, capteur);

  //   // Ajouter aux capteurs installés
  //   if (!this.instaledCapteurs.find((c) => c.Id === capteur.Id)) {
  //     this.instaledCapteurs.push(capteur);

  //     // Remove from all possible source arrays and track the source
  //     const index = this.peredCapteurs.findIndex(
  //       (c) => c.FriendlyName === capteur.FriendlyName
  //     );
  //     const index2 = this.Capteurs.findIndex(
  //       (c) => c.FriendlyName === capteur.FriendlyName
  //     );
  //     const index3 = this.peredCapteurs2.findIndex(
  //       (c) => c.FriendlyName === capteur.FriendlyName
  //     );

  //     if (index !== -1) {
  //       // Remove from peredCapteurs (temporary pairing list)
  //       this.peredCapteurs.splice(index, 1);
  //       this.capteurSourceMap.set(capteur.Id, 'peredCapteurs');
  //     } else if (index2 !== -1) {
  //       // Remove from Capteurs (existing capteurs list)
  //       this.Capteurs.splice(index, 1);
  //       this.capteurSourceMap.set(capteur.Id, 'Capteurs');
  //     } else if (index3 !== -1) {
  //       // Remove from peredCapteurs2 (newly paired capteurs)
  //       this.peredCapteurs2.splice(index, 1);
  //       this.capteurSourceMap.set(capteur.Id, 'peredCapteurs2');
  //     }
  //   }

  //   this.showSuccessNotification(`${capteur.FriendlyName} placé sur le plan`);
  // }
  // Update the dropCapteurOnCanvas method to ensure proper tracking
  dropCapteurOnCanvas(capteur: Capteur, x: number, y: number) {
    // Convert canvas coordinates
    const pointer = { x, y };

    // Add the capteur to canvas with animation
    this.addCapteurToCanvasWithAnimation(pointer.x, pointer.y, capteur);

    // Add to installed capteurs if not already there
    if (!this.instaledCapteurs.find((c) => c.Id === capteur.Id)) {
      this.instaledCapteurs.push(capteur);

      // Remove from all possible source arrays and track the source
      const indexPered = this.peredCapteurs.findIndex(
        (c) =>
          c.FriendlyName === capteur.FriendlyName ||
          c.IeeeAddress === capteur.IeeeAddress
      );
      const indexCapteurs = this.Capteurs.findIndex(
        (c) =>
          c.FriendlyName === capteur.FriendlyName ||
          c.IeeeAddress === capteur.IeeeAddress
      );
      const indexPered2 = this.peredCapteurs2.findIndex(
        (c) =>
          c.FriendlyName === capteur.FriendlyName ||
          c.IeeeAddress === capteur.IeeeAddress
      );

      if (indexPered !== -1) {
        this.peredCapteurs.splice(indexPered, 1);
        this.capteurSourceMap.set(capteur.Id, 'peredCapteurs');
      } else if (indexCapteurs !== -1) {
        this.Capteurs.splice(indexCapteurs, 1);
        this.capteurSourceMap.set(capteur.Id, 'Capteurs');
      } else if (indexPered2 !== -1) {
        this.peredCapteurs2.splice(indexPered2, 1);
        this.capteurSourceMap.set(capteur.Id, 'peredCapteurs2');
      }
    }

    this.showSuccessNotification(`${capteur.FriendlyName} placé sur le plan`);
  }

  addCapteurToCanvasWithAnimation(x: number, y: number, capteur: Capteur) {
    const iconName = this.getIconForCapteur(
      capteur.TypeCapteur?.DisplayName || ''
    );
    const iconColor = this.getIconColorForCapteur(
      capteur.TypeCapteur?.DisplayName || ''
    );

    const capteurGroup = this.createCapteurIcon(
      x,
      y,
      capteur,
      iconName,
      iconColor
    );

    if (capteurGroup) {
      capteurGroup.set({
        capteurData: capteur,
        type: 'capteur',
        selectable: true,
        hasControls: true,
        hasBorders: true,
        opacity: 0,
        scaleX: 0.1,
        scaleY: 0.1,
      });

      this.canvas.add(capteurGroup);

      // Animation d'apparition
      capteurGroup.animate(
        {
          opacity: 1,
          scaleX: 1,
          scaleY: 1,
        },
        {
          duration: 500,
          easing: fabric.util.ease.easeOutBounce,
          onChange: () => this.canvas.renderAll(),
          onComplete: () => {
            // Ajouter un effet de pulsation
            this.pulseCapteur(capteurGroup);
          },
        }
      );

      // Enregistrer dans l'historique
      this.placedCapteurs.push({
        capteur,
        position: { x, y },
        canvasObject: capteurGroup,
        timestamp: new Date(),
      });

      console.log(
        'Capteur ajouté au canvas avec animation:',
        capteur.FriendlyName
      );
    }
  }

  pulseCapteur(capteurGroup: fabric.Object) {
    // Animation de pulsation pour attirer l'attention
    capteurGroup.animate(
      { scaleX: 1.2, scaleY: 1.2 },
      {
        duration: 300,
        easing: fabric.util.ease.easeOutQuad,
        onChange: () => this.canvas.renderAll(),
        onComplete: () => {
          capteurGroup.animate(
            { scaleX: 1, scaleY: 1 },
            {
              duration: 300,
              easing: fabric.util.ease.easeOutQuad,
              onChange: () => this.canvas.renderAll(),
            }
          );
        },
      }
    );
  }

  createCapteurIcon(x: number, y: number, capteur: Capteur, iconName: string, iconColor: string): fabric.Group | null {
    try {
      // Create a background circle for the icon, using the sensor's color
      const backgroundCircle = new fabric.Circle({
        radius: 20, // Adjust size as needed
        fill: iconColor,
        originX: 'center',
        originY: 'center'
      });

      // Create the Material Icon text object
      const iconText = new fabric.Text(this.getIconCharacter(iconName), {
        fontSize: 24,
        fill: '#ffffff', // White color for the Material Icon glyph
        fontFamily: 'Material Icons', // Crucial: specifies the font to use
        textAlign: 'center',
        originX: 'center',
        originY: 'center'
      });

      // Create the name text with improved style
      const nameText = new fabric.Text(capteur.FriendlyName, {
        fontSize: 10,
        fill: '#333333',
        fontFamily: 'Arial, sans-serif',
        textAlign: 'center',
        originX: 'center',
        originY: 'center',
        top: 35, // Position below the icon
        backgroundColor: 'rgba(255,255,255,0.95)',
        padding: 6
      });

      // Status badge (remains the same)
      const statusBadge = new fabric.Circle({
        radius: 6,
        fill: capteur.State === 'online' ? '#4ade80' : '#f87171',
        stroke: '#ffffff',
        strokeWidth: 2,
        left: 15, // Position relative to the group's center
        top: -15, // Position relative to the group's center
        shadow: new fabric.Shadow({
          color: 'rgba(0,0,0,0.2)',
          blur: 3,
          offsetX: 1,
          offsetY: 1
        })
      });

      // Group all elements. Order matters for layering (background first).
      const group = new fabric.Group([backgroundCircle, iconText, nameText, statusBadge], {
        left: x, // x, y are the center of the group
        top: y,
        selectable: true,
        hasControls: true,
        hasBorders: true
      });

      return group;

    } catch (error) {
      console.error('Erreur lors de la création de l\'icône capteur:', error);
      return null;
    }
  }

  getIconCharacter(iconName: string): string {
    // This map contains the Unicode codepoints for various Material Icons.
    // These codepoints are specific to the 'Material Icons' font.
    const materialIconMap: { [key: string]: string } = {
      'water_drop': '\ue798',           // water_drop
      'sensor_door': '\uf1aa',          // sensor_door
      'radar': '\ue328',                // radar
      'device_thermostat': '\ue1ff',    // device_thermostat
      'power': '\ue63c',                // power
      'wb_sunny': '\ue430',             // wb_sunny
      'air': '\uefd1',                  // air_quality
      'tune': '\ue429',                 // tune
      'radio_button_checked': '\ue837', // radio_button_checked (often used for presence)
      'equalizer': '\ue01d',            // equalizer (often used for vibration)
      'circle': '\uef4a',               // circle (often used for smart button)
      'cloud': '\ue2bd',                // cloud (often used for smoke detector)
      'toggle_on': '\ue9f6',            // toggle_on (often used for light switch)
      'hub': '\ue9f4',                  // hub
      'thermostat': '\uef02',           // thermostat
      'horizontal_rule': '\uef05',      // horizontal_rule (for roller shade)
      'sensors': '\ue328',
      'Weather Station': '\ue1ff',
      "inconnu": '\ue328'
    };
    return materialIconMap[iconName] || materialIconMap['sensors'];
  }

  // Nouveau système d'affichage des détails
  showCapteurDetailsAtPosition(capteur: Capteur, x: number, y: number) {
    this.selectedCapteurForDetails = capteur;
    this.showCapteurDetails = true;
    this.capteurDetailsPosition = { x: x + 20, y: y + 20 };

    // Assurer que le popup reste dans les limites de l'écran
    const popup = document.querySelector(
      '.capteur-details-popup'
    ) as HTMLElement;
    if (popup) {
      setTimeout(() => {
        const rect = popup.getBoundingClientRect();
        if (rect.right > window.innerWidth) {
          this.capteurDetailsPosition.x = x - rect.width - 20;
        }
        if (rect.bottom > window.innerHeight) {
          this.capteurDetailsPosition.y = y - rect.height - 20;
        }
      }, 10);
    }
  }

  hideCapteurDetails() {
    this.showCapteurDetails = false;
    this.selectedCapteurForDetails = null;
  }

  // Amélioration de l'appairage avec animations
  togglePermitJoin(): void {
    this.pairingMode = !this.pairingMode;

    if (this.pairingMode) {
      this.startPairingWithAnimation();
    } else {
      this.stopPairingWithAnimation();
    }

    const message = this.pairingMode
      ? '{"value": true, "time": 254}'
      : '{"value": false}';

    console.log(
      'Toggling Permit Join - New Pairing Mode:',
      this.pairingMode,
      'Message:',
      message
    );

    if (!this.pairingMode) {
      this.stopPairingCountdown();
      const deactivationMessage = '{"value": false, "time": 0}';
      setTimeout(() => {
        this.mqttService.publish(
          `${this.selectedControler.ControllerBaseTopic}/bridge/request/permit_join`,
          deactivationMessage
        );
        console.log(
          'Deactivation message sent with delay:',
          deactivationMessage
        );
      }, 1000);
    } else {
      this.mqttService.publish(
        `${this.selectedControler.ControllerBaseTopic}/bridge/request/permit_join`,
        message
      );
      console.log('Activation message sent');
      this.startPairingCountdown(240);
    }
  }

  // Méthode améliorée pour démarrer l'animation
  startPairingWithAnimation() {
    this.pairingAnimation.isActive = true;
    this.pairingAnimation.progress = 0;
    this.pairingAnimation.currentStep = 'Activation du mode appairage...';
    this.pairingAnimation.foundDevices = 0;

    // Régénérer les particules
    this.generateParticles();

    // Animation progressive des étapes
    const steps = [
      'Activation du mode appairage...',
      'Recherche de nouveaux appareils...',
      'Écoute des signaux Zigbee...',
      'Prêt à détecter des capteurs...',
    ];

    let currentStepIndex = 0;
    const stepInterval = setInterval(() => {
      if (currentStepIndex < steps.length && this.pairingMode) {
        this.pairingAnimation.currentStep = steps[currentStepIndex];
        this.pairingAnimation.progress =
          ((currentStepIndex + 1) / steps.length) * 100;
        currentStepIndex++;
      } else {
        clearInterval(stepInterval);
        if (this.pairingMode) {
          this.pairingAnimation.currentStep =
            'En attente de nouveaux capteurs...';
          this.pairingAnimation.progress = 100;
        }
      }
    }, 1500);
  }

  // Méthode pour animer la découverte d'un nouveau capteur
  animateNewDeviceFound() {
    this.pairingAnimation.foundDevices++;

    // Effet de flash sur l'interface
    const container = document.querySelector('.pairing-animation-container');
    if (container) {
      container.classList.add('device-found-flash');
      setTimeout(() => {
        container.classList.remove('device-found-flash');
      }, 500);
    }

    // Mettre à jour le message
    this.pairingAnimation.currentStep = `Nouveau capteur détecté ! (${this.pairingAnimation.foundDevices})`;

    // Son de notification (optionnel)
    // this.playNotificationSound();
  }

  // Méthode pour arrêter l'animation
  stopPairingWithAnimation() {
    this.pairingAnimation.isActive = false;
    this.pairingAnimation.currentStep = "Arrêt de l'appairage...";

    // Filter out capteurs that are already installed on the canvas
    this.peredCapteurs2 = this.peredCapteurs2.filter(
      (capteur) =>
        !this.instaledCapteurs.find((installed) => installed.Id === capteur.Id)
    );

    // Also clear the temporary pairing list
    this.peredCapteurs = this.peredCapteurs.filter(
      (capteur) =>
        !this.instaledCapteurs.find((installed) => installed.Id === capteur.Id)
    );

    console.log(
      'Pairing stopped. Filtered peredCapteurs2:',
      this.peredCapteurs2.length,
      'items'
    );
    console.log('Installed capteurs:', this.instaledCapteurs.length, 'items');

    setTimeout(() => {
      this.pairingAnimation.progress = 0;
      this.pairingAnimation.currentStep = '';
      this.pairingAnimation.foundDevices = 0;
    }, 2000);
  }

  // Méthodes utilitaires
  // Méthodes utilitaires pour la sécurité des données
  hasSensorReadings(capteur: Capteur): boolean {
    return (
      capteur &&
      capteur.SensorReadings &&
      Array.isArray(capteur.SensorReadings) &&
      capteur.SensorReadings.length > 0
    );
  }

  getSafeSensorReadings(capteur: Capteur): SafeSensorReading[] {
    if (!this.hasSensorReadings(capteur)) {
      return [];
    }

    return capteur.SensorReadings!.slice(0, 3).map((reading) => ({
      Value: reading.Value || 'N/A',
      Unit: reading.Id || '',
      Timestamp: reading.Timestamp ? new Date(reading.Timestamp) : new Date(),
    }));
  }

  darkenColor(color: string): string {
    const hex = color.replace('#', '');
    const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - 50);
    const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - 50);
    const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - 50);
    return `#${r.toString(16).padStart(2, '0')}${g
      .toString(16)
      .padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  showSuccessNotification(message: string) {
    // Créer une notification de succès
    const notification = document.createElement('div');
    notification.className = 'success-notification';
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
      notification.classList.add('show');
    }, 100);

    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, 3000);
  }

  // Méthodes de gestion des capteurs installés
  // removeCapteurFromCanvas(capteur: Capteur) {
  //   // Add back to the appropriate source list based on where it came from
  //   const source = this.capteurSourceMap.get(capteur.Id);

  //   if (source === 'peredCapteurs2') {
  //     // Add back to newly paired capteurs
  //     if (!this.peredCapteurs2.find((c) => c.Id === capteur.Id)) {
  //       this.peredCapteurs2.push(capteur);
  //     }
  //   } else if (source === 'peredCapteurs') {
  //     // Add back to temporary pairing list
  //     if (!this.peredCapteurs.find((c) => c.Id === capteur.Id)) {
  //       this.peredCapteurs.push(capteur);
  //     }
  //   } else {
  //     // Default: add back to existing capteurs list
  //     if (!this.Capteurs.find((c) => c.Id === capteur.Id)) {
  //       this.Capteurs.push(capteur);
  //     }
  //   }

  //   // Clean up the tracking
  //   this.capteurSourceMap.delete(capteur.Id);

  //   const placedCapteur = this.placedCapteurs.find(
  //     (pc) => pc.capteur.Id === capteur.Id
  //   );
  //   if (placedCapteur) {
  //     // Animation de suppression
  //     placedCapteur.canvasObject.animate(
  //       {
  //         opacity: 0,
  //         scaleX: 0.1,
  //         scaleY: 0.1,
  //       },
  //       {
  //         duration: 300,
  //         easing: fabric.util.ease.easeInQuad,
  //         onChange: () => this.canvas.renderAll(),
  //         onComplete: () => {
  //           this.canvas.remove(placedCapteur.canvasObject);
  //           this.placedCapteurs = this.placedCapteurs.filter(
  //             (pc) => pc.capteur.Id !== capteur.Id
  //           );
  //           this.instaledCapteurs = this.instaledCapteurs.filter(
  //             (c) => c.Id !== capteur.Id
  //           );
  //         },
  //       }
  //     );
  //   }
  // }

  get availableCapteurs(): Capteur[] {
    return this.Capteurs.filter(
      (capteur) =>
        !this.instaledCapteurs.some(
          (installed) =>
            installed.Id === capteur.Id ||
            installed.IeeeAddress === capteur.IeeeAddress ||
            installed.FriendlyName === capteur.FriendlyName
        )
    );
  }

  // Also add a getter for available peredCapteurs2 (newly paired sensors)
  get availablePeredCapteurs2(): Capteur[] {
    return this.peredCapteurs2.filter(
      (capteur) =>
        !this.instaledCapteurs.some(
          (installed) =>
            installed.Id === capteur.Id ||
            installed.IeeeAddress === capteur.IeeeAddress ||
            installed.FriendlyName === capteur.FriendlyName
        )
    );
  }

  // Update the removeCapteurFromCanvas method to ensure proper filtering
  removeCapteurFromCanvas(capteur: Capteur) {
    // Get the source of this capteur
    const source = this.capteurSourceMap.get(capteur.Id);

    // Remove from installed capteurs first
    this.instaledCapteurs = this.instaledCapteurs.filter(
      (c) => c.Id !== capteur.Id
    );

    // Add back to the appropriate source list based on where it came from
    if (source === 'peredCapteurs2') {
      // Add back to newly paired capteurs if not already there
      if (!this.peredCapteurs2.find((c) => c.Id === capteur.Id)) {
        this.peredCapteurs2.push(capteur);
      }
    } else if (source === 'peredCapteurs') {
      // Add back to temporary pairing list if not already there
      if (!this.peredCapteurs.find((c) => c.Id === capteur.Id)) {
        this.peredCapteurs.push(capteur);
      }
    } else {
      // Default: add back to existing capteurs list if not already there
      if (!this.Capteurs.find((c) => c.Id === capteur.Id)) {
        this.Capteurs.push(capteur);
      }
    }

    // Clean up the tracking
    this.capteurSourceMap.delete(capteur.Id);

    // Remove from canvas with animation
    const placedCapteur = this.placedCapteurs.find(
      (pc) => pc.capteur.Id === capteur.Id
    );
    if (placedCapteur) {
      placedCapteur.canvasObject.animate(
        {
          opacity: 0,
          scaleX: 0.1,
          scaleY: 0.1,
        },
        {
          duration: 300,
          easing: fabric.util.ease.easeInQuad,
          onChange: () => this.canvas.renderAll(),
          onComplete: () => {
            this.canvas.remove(placedCapteur.canvasObject);
            this.placedCapteurs = this.placedCapteurs.filter(
              (pc) => pc.capteur.Id !== capteur.Id
            );
          },
        }
      );
    }

    console.log(
      `Capteur ${capteur.FriendlyName} removed from canvas and returned to source list`
    );
  }

  getCapteurStatus(capteur: Capteur): string {
    // Calculer le statut basé sur la dernière activité
    const lastSeen = new Date(capteur.LastSeen);
    const now = new Date();
    const diffMinutes = (now.getTime() - lastSeen.getTime()) / (1000 * 60);

    if (diffMinutes < 5) return 'online';
    if (diffMinutes < 60) return 'warning';
    return 'offline';
  }

  // Propriétés existantes (gardées pour compatibilité)
  pairingMode: boolean = false;
  pairingTimeLeft: number = 0;
  private pairingInterval: any;
  private subscriptions: Subscription[] = [];
  bridgeState: string = 'offline';
  devices: ZigbeeDevice[] = [];
  private logSubscription: Subscription | undefined;
  logs: string[] = [];
  private logsSubscription!: Subscription;

  // Variables pour les dropdowns (gardées pour compatibilité)
  clients: any[] = [];
  sites: any[] = [];
  locals: any[] = [];
  Controlers: any = [];
  Capteurs: Capteur[] = [];

  selectedClient: any = null;
  selectedSite: any = null;
  selectedLocal: any = null;
  // selectedControler: ClientLicenceControllerView =
  //   new ClientLicenceControllerView();
  selectedControler: any = null;

  clientSearchTerm = '';
  siteSearchTerm = '';
  localSearchTerm = '';
  ControlerSearchTerm = '';

  isClientDropdownOpen = false;
  isSiteDropdownOpen = false;
  isLocalDropdownOpen = false;
  isControlerDropdownOpen = false;

  selectedCapteur: Capteur | null = null;
  isAddingCapteur = false;

  filteredClients: Client[] = [];
  filteredSites: Site[] = [];
  filteredLocals: Local[] = [];
  filteredControler: any[] = [];
  clientId: string = '';

  // Méthodes existantes (simplifiées)
  private startPairingCountdown(seconds: number): void {
    this.pairingTimeLeft = seconds;
    this.pairingInterval = setInterval(() => {
      this.pairingTimeLeft--;
      if (this.pairingTimeLeft <= 0) {
        this.pairingMode = false;
        this.stopPairingCountdown();
        const deactivationMessage = '{"value": false, "time": 0}';
        this.mqttService.publish(
          `${this.selectedControler.ControllerBaseTopic}/bridge/request/permit_join`,
          deactivationMessage
        );
        console.log('Pairing automatically disabled after timeout');
      }
    }, 1000);
  }

  private stopPairingCountdown(): void {
    if (this.pairingInterval) {
      clearInterval(this.pairingInterval);
      this.pairingInterval = null;
    }
    this.pairingTimeLeft = 0;
  }

  getFormattedTimeLeft(): string {
    const minutes = Math.floor(this.pairingTimeLeft / 60);
    const seconds = this.pairingTimeLeft % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  ngOnDestroy(): void {
    this.stopPairingCountdown();
    if (this.logsSubscription) {
      this.logsSubscription.unsubscribe();
    }
  }

  // Autres méthodes (simplifiées pour garder la fonctionnalité de base)
  selectTool(elementType: string): void {
    this.selectedTool = elementType;
  }
  updateToolbar(): void {}
  deleteSelected(): void {}
  bringAllToFront(): void {}
  sendAllToBack(): void {}
  setPlanType(type: 'local' | 'site'): void {
    this.planType = type;
  }
  selectColor(color: string): void {
    this.selectedColor = color;
  }
  selectStrokeColor(color: string): void {
    this.selectedStrokeColor = color;
  }
  setStrokeWidth(width: number): void {
    this.selectedStrokeWidth = width;
  }
  onColorChange(event: Event): void {}
  onStrokeWidthChange(event: Event): void {}
  applyColorToSelected(): void {}
  pickColorFromSelected(): void {}
  async importFile(event: Event): Promise<void> {}
  exportToJson(): void {
    const dataURL = this.canvas.toDataURL({
      format: 'png',
      quality: 1,
      multiplier: 2,
    });

    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute('href', dataURL);
    downloadAnchorNode.setAttribute(
      'download',
      `plan-${this.planType}-${new Date().toISOString().split('T')[0]}.png`
    );
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  }

  exportToPNG(): void {}
  hexToRgba(hex: string, alpha: number): string {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  }

  lightenColor(color: string): string {
    const hex = color.replace('#', '');
    const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + 50);
    const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + 50);
    const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + 50);
    return `#${r.toString(16).padStart(2, '0')}${g
      .toString(16)
      .padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  // Méthodes pour les dropdowns (versions simplifiées)

  onSearchChangeClient() {
    if (this.clientSearchTerm.length > 1) {
      // optionnel : déclencher après 2 caractères
      this.installService
        .searchClients(this.clientSearchTerm)
        .subscribe((result) => {
          this.filteredClients = result['Content'];
          //console.log("client rechercher ", this.filteredClients);// adapte selon structure de ton retour
        });
    } else {
      this.filteredClients = this.clients;
    }
  }

  onSearchChangeSites() {
    if (this.siteSearchTerm.length > 1) {
      // optionnel : déclencher après 2 caractères
      this.installService
        .searchSites(this.siteSearchTerm)
        .subscribe((result) => {
          this.filteredSites = result['Content'];
          //console.log("client rechercher ", this.filteredSites);// adapte selon structure de ton retour
        });
    } else {
      this.filteredSites = this.sites;
    }
  }

  onSearchChangeLocals() {
    if (this.localSearchTerm.length > 1) {
      // optionnel : déclencher après 2 caractères
      this.installService
        .searchLocals(this.localSearchTerm)
        .subscribe((result) => {
          this.filteredLocals = result['Content'];
          // console.log("locals rechercher  ", this.filteredLocals);// adapte selon structure de ton retour
        });
    } else {
      this.filteredLocals = this.locals;
    }
  }

  onSearchChangeController() {
    if (this.ControlerSearchTerm.length > 1) {
      // optionnel : déclencher après 2 caractères
      this.installService
        .searchControlers(this.ControlerSearchTerm)
        .subscribe((result) => {
          this.filteredControler = result['Content'];
          //console.log("client rechercher ", this.filteredControler);// adapte selon structure de ton retour
        });
    } else {
      this.filteredControler = this.Controlers;
    }
  }

  selectClient(client: any): void {
    this.canvas.clear();
    this.Capteurs = [];
    this.peredCapteurs = [];
    this.peredCapteurs2 = [];
    this.instaledCapteurs = [];
    this.capteurSourceMap.clear();
    this.selectedClient = client;
    this.selectedSite = null;
    this.selectedLocal = null;
    this.isClientDropdownOpen = false;
    this.clientSearchTerm = '';
    this.siteSearchTerm = '';
    this.localSearchTerm = '';
    this.clientId = client.Id;
    console.log('Client sélectionné:', client);
    this.installService.getSitesByClientId(client.Id).subscribe(
      (res: any) => {
        this.sites = res['Content'];
        this.filteredSites = this.sites;
        //console.log(this.sites);
      },
      (err) => {
        console.error(err);
      }
    );
  }

  selectSite(site: any): void {
    this.selectedSite = site;
    this.selectedLocal = null;
    this.isSiteDropdownOpen = false;
    this.siteSearchTerm = '';
    this.localSearchTerm = '';
    console.log('Site sélectionné:', site);
    this.installService.getLocalBySitetId(site.Id).subscribe(
      (res: any) => {
        this.locals = res;
        this.filteredLocals = this.locals;
        // console.log("LOCAL", this.locals);
      },
      (err) => {
        console.error(err);
      }
    );
  }

  selectLocal(local: any): void {
    this.selectedLocal = local;
    this.isLocalDropdownOpen = false;
    this.localSearchTerm = '';
    this.installService.getClientControllers(this.clientId).subscribe(
      (res: any) => {
        this.Controlers = res;
        this.filteredControler = this.Controlers;
        console.log('Controler', this.Controlers);
      },
      (err) => {
        console.error(err);
      }
    );
    console.log('Local sélectionné:', local);
    if (local.Architecture2DImage) {
      const jsonData = JSON.parse(local.Architecture2DImage);
      this.canvas.loadFromJSON(jsonData, () => {
        // Attendre que tous les objets soient chargés
        setTimeout(() => {
          this.fitCanvasContent();
          this.lockPlanAsBackground();
          this.canvas.renderAll();
          //console.log('✅ Plan JSON importé, centré et redimensionné');
        }, 100);
      });
    }
  }

  // Méthode pour verrouiller le plan en arrière-plan
  lockPlanAsBackground() {
    const objects = this.canvas.getObjects();
    console.log(objects, "qqqqqqqqqqqqqqqqqqqqqqqqq");
  // h  objects.forEach((obj, index) => {


    // h  obj.textLines
      // Marquer comme objet du plan de base
     // h obj.set({
      // h  selectable: false,        // Ne peut pas être sélectionné
      // h  evented: false,          // N'écoute pas les événements souris
      // h  lockMovementX: true,     // Verrouiller le mouvement X
       // h lockMovementY: true,     // Verrouiller le mouvement Y
// h        lockRotation: true,      // Verrouiller la rotation
      // h  lockScalingX: true,      // Verrouiller le redimensionnement X
       // h lockScalingY: true,      // Verrouiller le redimensionnement Y
        // hlockUniScaling: true,    // Verrouiller le redimensionnement uniforme
        // hasControls: false,      // Cacher les contrôles de redimensionnement
        // hasBorders: false,       // Cacher les bordures de sélection
        //hoverCursor: 'default',  // Curseur normal au survol
        //  moveCursor: 'default',   // Curseur normal au déplacement
        // Optionnel : rendre légèrement transparent
     // h });

   // h   console.log(obj, "object plan");

      // Envoyer à l'arrière-plan (ordre des couches)
    // h  this.canvas.sendObjectToBack(obj);
   // h });

   this.canvas.getObjects().forEach((obj) => {
    obj.selectable = false;
    obj.hoverCursor = 'default';

    console.log("OBJ       ", obj);
  });

    this.canvas.renderAll();

  }
  // Méthode améliorée pour adapter le contenu au canvas
  fitCanvasContent() {
    const objects = this.canvas.getObjects();

    if (objects.length === 0) {
      return;
    }

    // Calculer les limites de tous les objets
    let minX = Infinity;
    let minY = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;

    objects.forEach((obj) => {
      const bounds = obj.getBoundingRect();
      minX = Math.min(minX, bounds.left);
      minY = Math.min(minY, bounds.top);
      maxX = Math.max(maxX, bounds.left + bounds.width);
      maxY = Math.max(maxY, bounds.top + bounds.height);
    });

    // Dimensions du contenu total
    const contentWidth = maxX - minX;
    const contentHeight = maxY - minY;
    const contentCenterX = minX + contentWidth / 2;
    const contentCenterY = minY + contentHeight / 2;

    console.log('Dimensions du contenu:', {
      width: contentWidth,
      height: contentHeight,
      minX,
      minY,
      maxX,
      maxY,
    });

    // Dimensions du canvas
    const canvasWidth = this.canvas.getWidth();
    const canvasHeight = this.canvas.getHeight();
    const canvasCenterX = canvasWidth / 2;
    const canvasCenterY = canvasHeight / 2;

    // Calculer l'échelle pour s'adapter au canvas avec une marge
    const margin = 50;
    const scaleX = (canvasWidth - margin * 2) / contentWidth;
    const scaleY = (canvasHeight - margin * 2) / contentHeight;
    const scale = Math.min(scaleX, scaleY, 1); // Ne pas agrandir

    // Calculer le décalage pour centrer
    const deltaX = canvasCenterX - contentCenterX;
    const deltaY = canvasCenterY - contentCenterY;

    // Appliquer la transformation à tous les objets
    objects.forEach((obj) => {
      // Sauvegarder les propriétés actuelles
      const currentLeft = obj.left || 0;
      const currentTop = obj.top || 0;
      const currentScaleX = obj.scaleX || 1;
      const currentScaleY = obj.scaleY || 1;

      // Calculer les nouvelles positions
      const newLeft = (currentLeft - contentCenterX) * scale + canvasCenterX;
      const newTop = (currentTop - contentCenterY) * scale + canvasCenterY;

      // Appliquer les transformations
      obj.set({
        left: newLeft,
        top: newTop,
        scaleX: currentScaleX * scale,
        scaleY: currentScaleY * scale,
      });

      // Mettre à jour les coordonnées
      obj.setCoords();
    });

  }

  selectControler(Controler: any): void {
    this.selectedControler = Controler;
    this.isControlerDropdownOpen = false;
    this.ControlerSearchTerm = '';
    console.log('Controleur sélectionné:', Controler);

    this.capteursServices.getUnusedCapteurs(Controler.ControllerId).subscribe(
      (res: any) => {
        this.Capteurs = Array.isArray(res) ? res : res['Content'] || [];
        console.log('Capteurs disponibles:', this.Capteurs);
      },
      (err) => {
        console.error(err);
      }
    );

    this.zigbeeService.subscribeToBridgeLogs(Controler.ControllerBaseTopic);
    this.logsSubscription = this.zigbeeService
      .getBridgeLogs()
      .subscribe((log: string) => {
        this.processNewDeviceLog(log);
      });
  }

  toggleClientDropdown(): void {
    this.isClientDropdownOpen = !this.isClientDropdownOpen;
    this.isSiteDropdownOpen = false;
    this.isLocalDropdownOpen = false;
  }

  toggleSiteDropdown(): void {
    if (!this.selectedClient) return;
    this.isSiteDropdownOpen = !this.isSiteDropdownOpen;
    this.isClientDropdownOpen = false;
    this.isLocalDropdownOpen = false;
  }

  toggleLocalDropdown(): void {
    if (!this.selectedSite) return;
    this.isLocalDropdownOpen = !this.isLocalDropdownOpen;
    this.isClientDropdownOpen = false;
    this.isSiteDropdownOpen = false;
  }

  toggleControlerDropdown(): void {
    if (!this.selectedLocal) return;
    this.isControlerDropdownOpen = !this.isControlerDropdownOpen;
    this.isClientDropdownOpen = false;
    this.isSiteDropdownOpen = false;
    this.isLocalDropdownOpen = false;
  }

  closeAllDropdowns(): void {
    this.isClientDropdownOpen = false;
    this.isSiteDropdownOpen = false;
    this.isLocalDropdownOpen = false;
    this.isControlerDropdownOpen = false;
  }

  resetSelections(): void {
    this.selectedClient = null;
    this.selectedSite = null;
    this.selectedLocal = null;
    this.selectedControler = new ClientLicenceControllerView();
    this.clientSearchTerm = '';
    this.siteSearchTerm = '';
    this.localSearchTerm = '';
    this.ControlerSearchTerm = '';
    this.closeAllDropdowns();
  }

  // Méthodes de compatibilité
  selectCapteurToAdd(capteur: Capteur): void {
    this.selectedCapteur = capteur;
    this.isAddingCapteur = true;
    this.selectedTool = 'capteur';
    this.canvas.defaultCursor = 'crosshair';
    this.canvas.discardActiveObject();
    this.canvas.renderAll();
    // console.log('Capteur sélectionné pour ajout:', capteur);
  }

  @HostListener('document:click', ['$event'])
  onCanvasClick(event: MouseEvent): void {
    if (!this.isAddingCapteur || !this.selectedCapteur) return;

    const canvasElement = document.getElementById(
      'canvas'
    ) as HTMLCanvasElement;
    if (!canvasElement) return;

    const rect = canvasElement.getBoundingClientRect();
    const isClickOnCanvas = event.target === canvasElement;

    if (isClickOnCanvas) {
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      const pointer = this.canvas.getPointer(event as any);
      this.addCapteurToCanvas(pointer.x, pointer.y, this.selectedCapteur);
      this.resetCapteurSelection();
    }
  }

  addCapteurToCanvas(x: number, y: number, capteur: Capteur): void {
    const iconName = this.getIconForCapteur(
      capteur.TypeCapteur?.DisplayName || ''
    );
    const iconColor = this.getIconColorForCapteur(
      capteur.TypeCapteur?.DisplayName || ''
    );

    const capteurGroup = this.createCapteurIcon(
      x,
      y,
      capteur,
      iconName,
      iconColor
    );

    if (capteurGroup) {
      capteurGroup.set({
        capteurData: capteur,
        type: 'capteur',
        selectable: true,
        hasControls: true,
        hasBorders: true,
      });

      this.canvas.add(capteurGroup);
      this.canvas.setActiveObject(capteurGroup);
      this.canvas.renderAll();

      console.log('Capteur ajouté au canvas:', capteur.FriendlyName);
    }
  }

  resetCapteurSelection(): void {
    this.selectedCapteur = null;
    this.isAddingCapteur = false;
    this.selectedTool = '';
    this.canvas.defaultCursor = 'default';
  }

  getCapteurInfo(obj: fabric.Object): Capteur | null {
    return obj.get('capteurData') || null;
  }

  highlightCapteur(capteur: Capteur): void {
    this.canvas.getObjects().forEach((obj) => {
      const capteurData = this.getCapteurInfo(obj);
      if (capteurData && capteurData.Id === capteur.Id) {
        const originalScaleX = obj.scaleX || 1;
        const originalScaleY = obj.scaleY || 1;
        const originalStroke = obj.stroke;
        const originalStrokeWidth = obj.strokeWidth || 0;

        obj.animate(
          {
            scaleX: originalScaleX * 1.3,
            scaleY: originalScaleY * 1.3,
            strokeWidth: 4,
          },
          {
            duration: 400,
            easing: fabric.util.ease.easeOutBounce,
            onChange: () => this.canvas.renderAll(),
            onComplete: () => {
              obj.animate(
                {
                  scaleX: originalScaleX,
                  scaleY: originalScaleY,
                  strokeWidth: originalStrokeWidth,
                },
                {
                  duration: 400,
                  easing: fabric.util.ease.easeOutBounce,
                  onChange: () => this.canvas.renderAll(),
                  onComplete: () => {
                    obj.set('stroke', originalStroke);
                    this.canvas.renderAll();
                  },
                }
              );
            },
          }
        );

        obj.set({
          stroke: '#FFD700',
          strokeWidth: 4,
        });

        this.canvas.setActiveObject(obj);
        this.centerViewOnObject(obj);
        this.canvas.renderAll();
      }
    });
  }

  // Méthode pour gérer le drop sur le canvas
  onCanvasDropped(event: CdkDragDrop<any[], any[], any>) {
    const draggedCapteur = event.item.data;
    if (draggedCapteur) {
      // Obtenir les coordonnées du drop
      const canvasElement = document.getElementById('canvas');
      if (canvasElement) {
        const rect = canvasElement.getBoundingClientRect();
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;

        this.dropCapteurOnCanvas(draggedCapteur, centerX, centerY);
      }
    }
  }

  private centerViewOnObject(obj: fabric.Object): void {
    const canvasCenter = this.canvas.getCenter();
    const objCenter = obj.getCenterPoint();

    const deltaX = canvasCenter.left - objCenter.x;
    const deltaY = canvasCenter.top - objCenter.y;

    console.log(
      `Capteur ${
        obj.get('capteurData')?.FriendlyName
      } mis en évidence à la position:`,
      objCenter
    );
  }
  // Propriétés à ajouter dans la classe du composant
  showInstallationResult = false;
  installationResult: InstallationResult | null = null;

  // Fonction modifiée addTransaction()
  addTransaction() {
    const transactions: Transaction[] = [];
    const errors: any[] = [];
    console.log('Capteur add Transaction ', this.capteurCree);
    this.capteurCree.forEach((element) => {
      const transaction: Transaction = new Transaction();
      transaction.IdCapteur = element.Id;
      if (this.selectedControler.ControllerId !== null) {
        transaction.IdController = this.selectedControler.ControllerId;
      }
      transaction.IdLocal = this.selectedLocal.Id;
      transactions.push(transaction);
    });

    console.log('Transaction :', transactions);

    // Initialiser le résultat
    this.installationResult = {
      success: false,
      totalCapteurs: this.instaledCapteurs.length,
      successfulInstalls: 0,
      failedInstalls: 0,
      transactions: transactions,
      errors: [],
      timestamp: new Date(),
    };

    this.installService.addTransactions(transactions).subscribe({
      next: (response) => {
        console.log('Transactions créées:', response);

        // Mise à jour du résultat en cas de succès
        this.installationResult!.success = true;
        this.installationResult!.successfulInstalls = transactions.length;
        this.installationResult!.failedInstalls = 0;

        // Afficher la popup
        this.showInstallationResult = true;
      },
      error: (error) => {
        console.error('Erreur lors de la création:', error);

        // Mise à jour du résultat en cas d'erreur
        this.installationResult!.success = false;
        this.installationResult!.successfulInstalls = 0;
        this.installationResult!.failedInstalls = transactions.length;
        this.installationResult!.errors.push(error);

        // Afficher la popup même en cas d'erreur
        this.showInstallationResult = true;
      },
    });
  }

  // Méthodes pour gérer la popup
  closeInstallationResult() {
    this.showInstallationResult = false;
    this.installationResult = null;
    this.selectedClient = null;
    this.selectedLocal = null;
    this.selectedControler = new ClientLicenceControllerView();
    this.selectedSite = null;
    this.Capteurs = [];
    this.instaledCapteurs = [];
    this.peredCapteurs = [];
    this.peredCapteurs2 = [];
    this.capteurSourceMap.clear();
    this.canvas.clear();
  }

  getInstallationStatusIcon(): string {
    if (!this.installationResult) return 'info';
    return this.installationResult.success ? 'check_circle' : 'error';
  }

  getInstallationStatusColor(): string {
    if (!this.installationResult) return 'var(--secondary-color)';
    return this.installationResult.success
      ? 'var(--success-color)'
      : 'var(--danger-color)';
  }

  getInstallationStatusText(): string {
    if (!this.installationResult) return 'Installation en cours...';
    return this.installationResult.success
      ? 'Installation réussie !'
      : "Erreur d'installation";
  }

  // Méthode pour relancer l'installation en cas d'échec
  retryInstallation() {
    this.closeInstallationResult();
    this.addTransaction();
  }

  // Méthode pour nettoyer et recommencer
  resetInstallation() {
    this.closeInstallationResult();
    // Optionnel: réinitialiser les capteurs installés
    // this.instaledCapteurs = [];
    // this.placedCapteurs = [];
  }

  // Ajout dans le composant TypeScript

  // Propriétés pour la popup de confirmation
  showConfirmationPopup = false;
  isProcessingInstallation = false;

  // Fonction modifiée addTransaction() - devient maintenant showConfirmationDialog()
  showConfirmationDialog() {
    // Vérifications avant d'afficher la confirmation
    if (!this.selectedControler) {
      this.showErrorNotification(
        "Veuillez sélectionner un contrôleur avant de procéder à l'installation."
      );
      return;
    }

    if (!this.selectedLocal) {
      this.showErrorNotification(
        "Veuillez sélectionner un local avant de procéder à l'installation."
      );
      return;
    }

    if (this.instaledCapteurs.length === 0) {
      this.showErrorNotification(
        "Aucun capteur n'a été placé sur le plan. Veuillez placer au moins un capteur avant l'installation."
      );
      return;
    }

    // Afficher la popup de confirmation
    this.showConfirmationPopup = true;
  }

  // Nouvelle fonction pour traiter l'installation après confirmation
  confirmInstallation() {
    this.showConfirmationPopup = false;
    this.isProcessingInstallation = true;

    const transactions: Transaction[] = [];

    this.instaledCapteurs.forEach((element) => {
      const transaction: Transaction = new Transaction();
      transaction.IdCapteur = element.Id;
      if (this.selectedControler.ControllerId !== null) {
        transaction.IdController = this.selectedControler.ControllerId;
      }
      transaction.IdLocal = this.selectedLocal.Id;
      transaction.InControl = true;

      transactions.push(transaction);
    });

    console.log('Transaction :', transactions);

    // Initialiser le résultat
    this.installationResult = {
      success: false,
      totalCapteurs: this.instaledCapteurs.length,
      successfulInstalls: 0,
      failedInstalls: 0,
      transactions: transactions,
      errors: [],
      timestamp: new Date(),
    };

    // Simulation d'un délai de traitement (optionnel)

    this.processInstallationTransactions(transactions);
    this.stopPairingWithAnimation();
  }

  // Fonction pour traiter les transactions
  processInstallationTransactions(transactions: Transaction[]) {
    this.installService.addTransactions(transactions).subscribe({
      next: (response) => {
        console.log('Transactions créées:', response);
        this.addPlanToLocal();
        this.isProcessingInstallation = false;

        // Succès
        this.installationResult!.success = true;
        this.installationResult!.successfulInstalls = transactions.length;
        this.installationResult!.failedInstalls = 0;

        // Afficher la popup de résultat
        this.showInstallationResult = true;

        // Auto-fermeture après 15 secondes pour les succès
      },
      error: (error) => {
        console.error('Erreur lors de la création:', error);

        this.isProcessingInstallation = false;

        // Échec
        this.installationResult!.success = false;
        this.installationResult!.successfulInstalls = 0;
        this.installationResult!.failedInstalls = transactions.length;
        this.installationResult!.errors.push(error);

        // Afficher la popup de résultat
        this.showInstallationResult = true;
      },
    });
  }

  // Annuler l'installation
  cancelInstallation() {
    this.showConfirmationPopup = false;
  }

  // Méthodes utilitaires pour les notifications
  showErrorNotification(message: string) {
    const notification = document.createElement('div');
    notification.className = 'error-notification';
    notification.innerHTML = `
    <div class="notification-content">
      <mat-icon>error</mat-icon>
      <span>${message}</span>
    </div>
  `;
    document.body.appendChild(notification);

    setTimeout(() => {
      notification.classList.add('show');
    }, 100);

    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => {
        if (document.body.contains(notification)) {
          document.body.removeChild(notification);
        }
      }, 300);
    }, 4000);
  }

  // Méthode pour calculer l'estimation du temps d'installation
  getEstimatedInstallationTime(): string {
    const baseTimePerCapteur = 2; // 2 secondes par capteur
    const totalTime = this.instaledCapteurs.length * baseTimePerCapteur;

    if (totalTime < 60) {
      return `~${totalTime} secondes`;
    } else {
      const minutes = Math.ceil(totalTime / 60);
      return `~${minutes} minute${minutes > 1 ? 's' : ''}`;
    }
  }

  // Méthode pour obtenir la liste des types de capteurs
  getCapteurTypesSummary(): string[] {
    const types = new Set(
      this.instaledCapteurs.map(
        (c) => c.TypeCapteur?.DisplayName || 'Type inconnu'
      )
    );
    return Array.from(types);
  }

  // Vérifier si l'installation peut être effectuée
  canInstall(): boolean {
    return (
      this.selectedControler &&
      this.selectedLocal &&
      this.instaledCapteurs.length > 0 &&
      !this.isProcessingInstallation
    );
  }

  addPlanToLocal(): void {
    const json = JSON.stringify(this.canvas.toJSON());
    this.selectedLocal.Architecture2DImage = json;

    this.selectedLocal.Site = null;
    this.selectedLocal.Transactions = null;
    //this.selectedLocal.Site.ClientId="null";

    this.selectedLocal.TypeLocal = null;
    this.installService.updateLocal(this.selectedLocal).subscribe(
      (res) => {
        console.log('Mise à jour réussie', res);
      },
      (err) => {
        console.error('Erreur mise à jour', err);
      }
    );
  }
}
