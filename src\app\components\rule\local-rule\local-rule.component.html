<div class="dialog-container">

    <!-- Dialog Header -->
    <div class="dialog-header">
        <h1 class="header-title">
            <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z">
                </path>
            </svg>
            <span>Assistant de Règles Intelligentes</span>
        </h1>

        <button mat-icon-button (click)="onCancel()" class="close-button" [disabled]="isBusy"
            [attr.aria-label]="'<PERSON><PERSON><PERSON> la boîte de dialogue'">
            <mat-icon>close</mat-icon>
        </button>
    </div>

    <!-- Dialog Content -->
    <div class="dialog-content content-main" [class.pointer-events-none]="isBusy" role="main">

        <!-- Loading Indicator -->
        <ngx-ui-loader *ngIf="isLoading"></ngx-ui-loader>

        <!-- Step Progress System -->
        <div class="step-progress-container" *ngIf="!isLoading && !loadingError" role="progressbar"
            [attr.aria-valuenow]="currentStep" [attr.aria-valuemin]="1" [attr.aria-valuemax]="5"
            [attr.aria-label]="'Progression des étapes: étape ' + currentStep + ' sur 5'">

            <!-- Progress Bar -->
            <div class="step-progress-bar">
                <div class="step-progress-fill" [style.width.%]="(currentStep / 5) * 100"></div>
            </div>

            <!-- Step Indicators -->
            <div class="step-indicators" role="tablist">
                <div *ngFor="let step of [1,2,3,4,5]; let i = index" class="step-indicator"
                    [class.active]="currentStep >= step" [class.completed]="isStepCompleted(step)"
                    [class.current]="currentStep === step" [class.clickable]="isStepAccessible(step) && !isBusy"
                    (click)="isStepAccessible(step) && !isBusy ? goToStep(step) : null"
                    [attr.tabindex]="isStepAccessible(step) && !isBusy ? 0 : -1" [attr.role]="'tab'"
                    [attr.aria-selected]="currentStep === step" [attr.aria-expanded]="!isStepCollapsed(step)"
                    [attr.aria-label]="'Aller à l\'étape ' + step + ': ' + getStepLabel(step)">

                    <!-- Step Number -->
                    <div class="step-number">
                        <mat-icon *ngIf="isStepCompleted(step)" class="step-check" aria-hidden="true">check</mat-icon>
                        <span *ngIf="!isStepCompleted(step)">{{ step }}</span>
                    </div>

                    <!-- Step Label -->
                    <div class="step-label">
                        <span *ngIf="step === 1">Règle</span>
                        <span *ngIf="step === 2">Contrôleur</span>
                        <span *ngIf="step === 3">Capteurs</span>
                        <span *ngIf="step === 4">Actionneurs</span>
                        <span *ngIf="step === 5">Aperçu</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Display -->
        <div *ngIf="loadingError && !isLoading" class="error-container" role="alert" aria-live="assertive">
            <div class="error-content">
                <svg class="error-icon" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clip-rule="evenodd"></path>
                </svg>
                <div>
                    <h3 class="error-title">Erreur de chargement</h3>
                    <p class="error-message">{{ loadingError }}</p>
                </div>
            </div>
            <div class="error-actions">
                <button (click)="retryLoadData()" class="error-retry-btn">
                    Réessayer
                </button>
            </div>
        </div>

        <!-- Steps Container -->
        <div *ngIf="!isLoading && !loadingError" class="steps-container">

            <!-- Step 1: Rule Selection -->
            <div class="step-section" [class.step-completed]="isStepCompleted(1)"
                [class.step-current]="currentStep === 1" [class.step-collapsed]="isStepCollapsed(1)"
                [class.step-accessible]="isStepAccessible(1)" [attr.data-step]="1"
                [attr.aria-hidden]="isStepCollapsed(1)">

                <!-- Step Header -->
                <div class="step-header" [class.clickable]="isStepAccessible(1) && currentStep !== 1"
                    (click)="isStepAccessible(1) && currentStep !== 1 ? goToStep(1) : null"
                    [attr.tabindex]="isStepAccessible(1) && currentStep !== 1 ? 0 : -1"
                    [attr.role]="isStepAccessible(1) && currentStep !== 1 ? 'button' : null"
                    [attr.aria-label]="'Aller à l\'étape 1: Sélection de la règle'">

                    <h2 class="step-title">
                        <span class="step-number-badge" [class.completed]="isStepCompleted(1)"
                            [class.active]="currentStep === 1">
                            <mat-icon *ngIf="isStepCompleted(1)" class="step-check-small"
                                aria-hidden="true">check</mat-icon>
                            <span *ngIf="!isStepCompleted(1)">1</span>
                        </span>
                        <span>Sélection de la règle de base</span>
                    </h2>
                </div>

                <!-- Step Content -->
                <div class="step-content" [class.collapsed]="isStepCollapsed(1)"
                    [class.disabled]="!isStepAccessible(1)">

                    <div>
                        <label for="ruleDropdown" class="form-label">
                            Choisir une règle existante:
                        </label>
                        <select id="ruleDropdown" [(ngModel)]="selectedRuleId" (ngModelChange)="onRuleSelectionChange()"
                            [disabled]="isFieldDisabled()" class="form-select">
                            <option value="">-- Sélectionner une règle --</option>
                            <option *ngFor="let rule of ruleList" [value]="rule.Id">
                                {{ getRuleNameFromRawData(rule.RawData) }}
                            </option>
                        </select>
                    </div>

                    <div *ngIf="baseRule" class="rule-preview-card preview-spacing" role="region"
                        aria-label="Aperçu de la règle sélectionnée">
                        <h4 class="preview-title">
                            <mat-icon class="preview-icon" aria-hidden="true">preview</mat-icon>
                            Aperçu de la règle sélectionnée:
                        </h4>
                        <div class="preview-content">
                            <div class="preview-item">
                                <mat-icon class="preview-small-icon" aria-hidden="true">label</mat-icon>
                                <strong>Nom:</strong> {{ baseRule.rule_name }}
                            </div>
                            <div class="preview-item">
                                <mat-icon class="preview-small-icon" aria-hidden="true">rule_folder</mat-icon>
                                <strong>Conditions:</strong> {{ baseRule.conditions.groups.length }} groupe(s)
                            </div>
                            <div class="preview-item">
                                <mat-icon class="preview-small-icon" aria-hidden="true">flash_on</mat-icon>
                                <strong>Actions:</strong> {{ baseRule.actions.length }} action(s)
                            </div>
                            <div *ngIf="selectedRuleSummary" class="preview-item-vertical">
                                <mat-icon class="preview-small-icon-top" aria-hidden="true">description</mat-icon>
                                <strong>Résumé:</strong>
                                <p class="preview-summary">{{ selectedRuleSummary }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Controller Selection -->
            <div class="step-section" [class.step-completed]="isStepCompleted(2)"
                [class.step-current]="currentStep === 2" [class.step-collapsed]="isStepCollapsed(2)"
                [class.step-accessible]="isStepAccessible(2)" [class.step-disabled]="!isStepAccessible(2)"
                [attr.data-step]="2" [attr.aria-hidden]="isStepCollapsed(2)">

                <div class="step-header" [class.clickable]="isStepAccessible(2) && currentStep !== 2"
                    (click)="isStepAccessible(2) && currentStep !== 2 ? goToStep(2) : null"
                    [attr.tabindex]="isStepAccessible(2) && currentStep !== 2 ? 0 : -1"
                    [attr.role]="isStepAccessible(2) && currentStep !== 2 ? 'button' : null"
                    [attr.aria-label]="'Aller à l\'étape 2: Sélection du contrôleur'">

                    <h2 class="step-title">
                        <span class="step-number-badge" [class.completed]="isStepCompleted(2)"
                            [class.active]="currentStep === 2" [class.disabled]="!isStepAccessible(2)">
                            <mat-icon *ngIf="isStepCompleted(2)" class="step-check-small"
                                aria-hidden="true">check</mat-icon>
                            <span *ngIf="!isStepCompleted(2)">2</span>
                        </span>
                        <span>Sélection du contrôleur</span>
                    </h2>
                </div>

                <div class="step-content" [class.collapsed]="isStepCollapsed(2)"
                    [class.disabled]="!isStepAccessible(2)">

                    <div class="form-grid">
                        <div>
                            <label for="controllerDropdown" class="form-label">
                                Contrôleur:
                            </label>
                            <select id="controllerDropdown" [(ngModel)]="selectedControllerId"
                                (ngModelChange)="onControllerSelectionChange()"
                                [disabled]="isFieldDisabled() || !isStepAccessible(2)" class="form-select">
                                <option value="">-- Sélectionner un contrôleur --</option>
                                <option *ngFor="let controller of controllers" [value]="controller.Id">
                                    {{ controller.Model }} ({{ controller.HostName }})
                                </option>
                            </select>
                        </div>

                        <div *ngIf="selectedControllerId && availableCapteursForMapping.length === 0"
                            class="warning-container warning-spacing" role="status" aria-live="polite">
                            <div class="warning-content">
                                <mat-icon class="warning-icon" aria-hidden="true">warning</mat-icon>
                                <p class="warning-message">
                                    Aucun capteur associé trouvé pour ce contrôleur via les transactions disponibles.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Device Mapping -->
            <div class="step-section" [class.step-completed]="isStepCompleted(3)"
                [class.step-current]="currentStep === 3" [class.step-collapsed]="isStepCollapsed(3)"
                [class.step-accessible]="isStepAccessible(3)" [class.step-disabled]="!isStepAccessible(3)"
                [attr.data-step]="3" [attr.aria-hidden]="isStepCollapsed(3)"
                *ngIf="isRuleSelected && deviceMappings.length > 0">

                <div class="step-header" [class.clickable]="isStepAccessible(3) && currentStep !== 3"
                    (click)="isStepAccessible(3) && currentStep !== 3 ? goToStep(3) : null"
                    [attr.tabindex]="isStepAccessible(3) && currentStep !== 3 ? 0 : -1"
                    [attr.role]="isStepAccessible(3) && currentStep !== 3 ? 'button' : null"
                    [attr.aria-label]="'Aller à l\'étape 3: Mappage des capteurs'">

                    <h2 class="step-title">
                        <span class="step-number-badge" [class.completed]="isStepCompleted(3)"
                            [class.active]="currentStep === 3" [class.disabled]="!isStepAccessible(3)">
                            <mat-icon *ngIf="isStepCompleted(3)" class="step-check-small"
                                aria-hidden="true">check</mat-icon>
                            <span *ngIf="!isStepCompleted(3)">3</span>
                        </span>
                        <span>Mappage des capteurs</span>
                        <div class="step-progress-indicator">
                            <mat-icon class="progress-icon" aria-hidden="true">sensors</mat-icon>
                            <span class="progress-text">
                                {{ mappedDeviceCount }}/{{ deviceMappings.length }}
                            </span>
                        </div>
                    </h2>
                </div>

                <div class="step-content" [class.collapsed]="isStepCollapsed(3)"
                    [class.disabled]="!isStepAccessible(3)">

                    <div class="info-container info-spacing" role="note"
                        aria-label="Instructions pour le mappage des capteurs">
                        <div class="info-content">
                            <mat-icon class="info-icon" aria-hidden="true">info</mat-icon>
                            <div>
                                <p class="info-text">
                                    <strong>Instructions:</strong> Associez chaque dispositif de la règle de base avec
                                    un capteur réel de vos transactions.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="mapping-list" role="list" [attr.aria-label]="'Liste des mappages de capteurs'">
                        <div *ngFor="let mapping of deviceMappings; let i = index" class="device-mapping-card"
                            [class.mapping-completed]="mapping.selectedCapteurId"
                            [attr.aria-label]="'Mappage du capteur ' + (i + 1) + ': ' + mapping.originalDevice"
                            role="listitem">

                            <div class="form-grid">
                                <div>
                                    <label class="form-label">
                                        Dispositif dans la règle:
                                    </label>
                                    <div class="readonly-field">
                                        <mat-icon class="field-icon" aria-hidden="true">device_hub</mat-icon>
                                        {{ mapping.originalDevice }}
                                    </div>
                                </div>

                                <div>
                                    <label class="form-label">
                                        Capteur réel à associer:
                                    </label>
                                    <select [(ngModel)]="mapping.selectedCapteurId"
                                        (ngModelChange)="onDeviceMappingChange(mapping)"
                                        [disabled]="isFieldDisabled() || !isStepAccessible(3)" class="form-select"
                                        [attr.aria-label]="'Sélectionner un capteur pour ' + mapping.originalDevice">
                                        <option value="">-- Sélectionner un capteur --</option>
                                        <option *ngFor="let capteur of getFilteredCapteursForDeviceMapping(mapping)"
                                            [value]="capteur.Id">
                                            {{ capteur.FriendlyName }} ({{ capteur.Topic }})
                                        </option>
                                    </select>
                                </div>
                            </div>

                            <div *ngIf="mapping.selectedTypeCapteur" class="mapping-success success-spacing"
                                role="status" aria-live="polite">
                                <div class="success-content">
                                    <div class="success-item">
                                        <mat-icon class="success-icon" aria-hidden="true">check_circle</mat-icon>
                                        <strong>Associé à:</strong>
                                        {{ mapping.selectedTypeCapteur.DisplayName || mapping.selectedTypeCapteur.Nom }}
                                    </div>
                                    <div class="success-item">
                                        <mat-icon class="success-small-icon" aria-hidden="true">topic</mat-icon>
                                        <strong>Topic:</strong> {{ mapping.selectedTypeCapteur.Topic }}
                                    </div>
                                    <div *ngIf="mapping.availableProperties && mapping.availableProperties.length > 0"
                                        class="success-item-vertical">
                                        <mat-icon class="success-small-icon-top" aria-hidden="true">settings</mat-icon>
                                        <div>
                                            <strong>Propriétés disponibles:</strong>
                                            <div class="property-tags">
                                                <span *ngFor="let prop of mapping.availableProperties"
                                                    class="property-tag">
                                                    {{ prop.key }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 4: Action Mapping -->
            <div class="step-section" [class.step-completed]="isStepCompleted(4)"
                [class.step-current]="currentStep === 4" [class.step-collapsed]="isStepCollapsed(4)"
                [class.step-accessible]="isStepAccessible(4)" [class.step-disabled]="!isStepAccessible(4)"
                [attr.data-step]="4" [attr.aria-hidden]="isStepCollapsed(4)"
                *ngIf="isRuleSelected && actionMappings.length > 0">

                <div class="step-header" [class.clickable]="isStepAccessible(4) && currentStep !== 4"
                    (click)="isStepAccessible(4) && currentStep !== 4 ? goToStep(4) : null"
                    [attr.tabindex]="isStepAccessible(4) && currentStep !== 4 ? 0 : -1"
                    [attr.role]="isStepAccessible(4) && currentStep !== 4 ? 'button' : null"
                    [attr.aria-label]="'Aller à l\'étape 4: Mappage des actionneurs'">

                    <h2 class="step-title">
                        <span class="step-number-badge" [class.completed]="isStepCompleted(4)"
                            [class.active]="currentStep === 4" [class.disabled]="!isStepAccessible(4)">
                            <mat-icon *ngIf="isStepCompleted(4)" class="step-check-small"
                                aria-hidden="true">check</mat-icon>
                            <span *ngIf="!isStepCompleted(4)">4</span>
                        </span>
                        <span>Mappage des actionneurs</span>
                        <div class="step-progress-indicator">
                            <mat-icon class="progress-icon" aria-hidden="true">settings_remote</mat-icon>
                            <span class="progress-text">
                                {{ mappedActionCount }}/{{ actionMappings.length }}
                            </span>
                        </div>
                    </h2>
                </div>

                <div class="step-content" [class.collapsed]="isStepCollapsed(4)"
                    [class.disabled]="!isStepAccessible(4)">

                    <div class="mapping-list" role="list" [attr.aria-label]="'Liste des mappages d\'actionneurs'">
                        <div *ngFor="let mapping of actionMappings; let i = index" class="device-mapping-card"
                            [class.mapping-completed]="mapping.selectedCapteurId"
                            [attr.aria-label]="'Mappage de l\'actionneur ' + (i + 1) + ': ' + mapping.originalTopic"
                            role="listitem">

                            <div class="form-grid">
                                <div>
                                    <label class="form-label">
                                        Topic dans la règle:
                                    </label>
                                    <div class="readonly-field">
                                        <mat-icon class="field-icon" aria-hidden="true">topic</mat-icon>
                                        {{ mapping.originalTopic }}
                                    </div>
                                </div>

                                <div>
                                    <label class="form-label">
                                        Actionneur réel à associer:
                                    </label>
                                    <select [(ngModel)]="mapping.selectedCapteurId"
                                        (ngModelChange)="onActionMappingChange(mapping)"
                                        [disabled]="isFieldDisabled() || !isStepAccessible(4)" class="form-select"
                                        [attr.aria-label]="'Sélectionner un actionneur pour ' + mapping.originalTopic">
                                        <option value="">-- Sélectionner un actionneur --</option>
                                        <option *ngFor="let capteur of getFilteredCapteursForActionMapping(mapping)"
                                            [value]="capteur.Id">
                                            {{ capteur.FriendlyName }} ({{ capteur.Topic }})
                                        </option>
                                    </select>
                                </div>
                            </div>

                            <div *ngIf="mapping.selectedTypeCapteur" class="mapping-success success-spacing"
                                role="status" aria-live="polite">
                                <div class="success-content">
                                    <div class="success-item">
                                        <mat-icon class="success-icon" aria-hidden="true">check_circle</mat-icon>
                                        <strong>Associé à:</strong>
                                        {{ mapping.selectedTypeCapteur.DisplayName || mapping.selectedTypeCapteur.Nom }}
                                    </div>
                                    <div class="success-item">
                                        <mat-icon class="success-small-icon" aria-hidden="true">topic</mat-icon>
                                        <strong>Topic:</strong> {{ mapping.selectedTypeCapteur.Topic }}
                                    </div>
                                    <div *ngIf="mapping.availableActions && mapping.availableActions.length > 0"
                                        class="success-item-vertical">
                                        <mat-icon class="success-small-icon-top" aria-hidden="true">flash_on</mat-icon>
                                        <div>
                                            <strong>Actions disponibles:</strong>
                                            <div class="action-tags">
                                                <span *ngFor="let action of mapping.availableActions"
                                                    class="action-tag">
                                                    {{ action.type }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 5: Preview -->
            <div class="step-section" [class.step-completed]="isStepCompleted(5)"
                [class.step-current]="currentStep === 5" [class.step-collapsed]="isStepCollapsed(5)"
                [class.step-accessible]="isStepAccessible(5)" [class.step-disabled]="!isStepAccessible(5)"
                [attr.data-step]="5" [attr.aria-hidden]="isStepCollapsed(5)" *ngIf="isRuleSelected">

                <div class="step-header" [class.clickable]="isStepAccessible(5) && currentStep !== 5"
                    (click)="isStepAccessible(5) && currentStep !== 5 ? goToStep(5) : null"
                    [attr.tabindex]="isStepAccessible(5) && currentStep !== 5 ? 0 : -1"
                    [attr.role]="isStepAccessible(5) && currentStep !== 5 ? 'button' : null"
                    [attr.aria-label]="'Aller à l\'étape 5: Aperçu de la règle transformée'">

                    <h2 class="step-title">
                        <span class="step-number-badge" [class.completed]="isStepCompleted(5)"
                            [class.active]="currentStep === 5" [class.disabled]="!isStepAccessible(5)">
                            <mat-icon *ngIf="isStepCompleted(5)" class="step-check-small"
                                aria-hidden="true">check</mat-icon>
                            <span *ngIf="!isStepCompleted(5)">5</span>
                        </span>
                        <span>Aperçu de la règle transformée</span>
                    </h2>
                </div>

                <div class="step-content" [class.collapsed]="isStepCollapsed(5)"
                    [class.disabled]="!isStepAccessible(5)">

                    <div class="preview-card preview-spacing" role="region"
                        aria-label="Détails de la règle transformée">
                        <div class="preview-form-container">
                            <div class="preview-form-item">
                                <label class="form-label label-with-icon">
                                    <mat-icon class="label-icon" aria-hidden="true">edit</mat-icon>
                                    Nom de la règle transformée:
                                </label>
                                <input type="text" [(ngModel)]="transformedRule.rule_name"
                                    (ngModelChange)="checkPreviewCompletion()" [disabled]="isFieldDisabled()"
                                    placeholder="Nom de la règle" class="form-input" />
                            </div>

                            <div class="preview-form-item">
                                <label class="form-label label-with-icon">
                                    <mat-icon class="label-icon" aria-hidden="true">priority_high</mat-icon>
                                    Priorité:
                                </label>
                                <select [(ngModel)]="transformedRule.priority" [disabled]="isFieldDisabled()"
                                    class="form-select">
                                    <option *ngFor="let p of priorities" [value]="p">
                                        Priorité {{ p }}
                                    </option>
                                </select>
                            </div>
                        </div>

                        <div class="topic-section">
                            <label class="form-label label-with-icon">
                                <mat-icon class="label-icon" aria-hidden="true">topic</mat-icon>
                                Topics utilisés:
                            </label>
                            <div class="topics-display"
                                [attr.aria-label]="'Liste des topics: ' + (transformedRule.topic_pattern.length ? transformedRule.topic_pattern.join(', ') : 'Aucun topic défini')">
                                <div *ngIf="transformedRule.topic_pattern.length === 0" class="no-topics">
                                    <mat-icon class="no-topics-icon" aria-hidden="true">info</mat-icon>
                                    Aucun topic défini
                                </div>
                                <div *ngFor="let topic of transformedRule.topic_pattern" class="topic-tag">
                                    <mat-icon class="topic-icon" aria-hidden="true">topic</mat-icon>
                                    {{ topic }}
                                </div>
                            </div>
                        </div>

                        <div class="checkbox-section">
                            <label class="checkbox-container">
                                <input type="checkbox" [(ngModel)]="transformedRule.enabled"
                                    [disabled]="isFieldDisabled()" class="custom-checkbox" />
                                <span class="checkbox-label">
                                    <mat-icon class="checkbox-icon" aria-hidden="true">power_settings_new</mat-icon>
                                    Règle activée
                                </span>
                            </label>
                        </div>
                    </div>

                    <div class="json-section">
                        <h3 class="json-title">
                            <mat-icon class="json-icon" aria-hidden="true">code</mat-icon>
                            Aperçu JSON de la règle transformée:
                        </h3>
                        <pre class="json-preview"
                            [attr.aria-label]="'Code JSON de la règle transformée'">{{ generateJSON() }}</pre>
                    </div>
                </div>
            </div>

            <!-- Summary Section -->
            <div *ngIf="isRuleSelected" class="summary-section" role="region" aria-label="Résumé de l'opération">
                <div class="summary-card">
                    <h3 class="summary-title">
                        <mat-icon class="summary-icon" aria-hidden="true">summarize</mat-icon>
                        Résumé de l'opération:
                    </h3>
                    <div class="summary-content">
                        <div class="summary-item">
                            <mat-icon class="summary-item-icon" aria-hidden="true">rule</mat-icon>
                            <strong>Règle de base:</strong>
                            <span class="summary-value">{{ baseRule?.rule_name }}</span>
                        </div>
                        <div class="summary-item">
                            <mat-icon class="summary-item-icon" aria-hidden="true">memory</mat-icon>
                            <strong>Contrôleur sélectionné:</strong>
                            <span class="summary-value">{{ getSelectedControllerModel() }}</span>
                        </div>
                        <div class="summary-item">
                            <mat-icon class="summary-item-icon" aria-hidden="true">sensors</mat-icon>
                            <strong>Capteurs mappés:</strong>
                            <span class="mapping-progress summary-progress"
                                [class.progress-complete]="mappedDeviceCount === deviceMappings.length && deviceMappings.length > 0"
                                [class.progress-partial]="mappedDeviceCount > 0 && mappedDeviceCount < deviceMappings.length"
                                [class.progress-none]="mappedDeviceCount === 0">
                                {{ mappedDeviceCount }}/{{ deviceMappings.length }}
                            </span>
                        </div>
                        <div class="summary-item">
                            <mat-icon class="summary-item-icon" aria-hidden="true">settings_remote</mat-icon>
                            <strong>Actionneurs mappés:</strong>
                            <span class="mapping-progress summary-progress"
                                [class.progress-complete]="mappedActionCount === actionMappings.length && actionMappings.length > 0"
                                [class.progress-partial]="mappedActionCount > 0 && mappedActionCount < actionMappings.length"
                                [class.progress-none]="mappedActionCount === 0">
                                {{ mappedActionCount }}/{{ actionMappings.length }}
                            </span>
                        </div>
                    </div>
                    <div *ngIf="selectedControllerId" class="rule-transaction-note" role="note"
                        aria-label="Informations sur les RuleTransactions">
                        <p class="note-text">
                            <mat-icon class="note-icon" aria-hidden="true">info</mat-icon>
                            Des RuleTransactions seront créées pour les transactions associées au contrôleur
                            sélectionné.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dialog Footer -->
    <div class="dialog-footer" role="contentinfo">
        <!-- Center buttons -->
        <div class="footer-center">
            <button mat-button (click)="onCancel()" class="cancel-button" [disabled]="isBusy"
                [attr.aria-label]="'Annuler et fermer la boîte de dialogue'">
                <mat-icon aria-hidden="true">cancel</mat-icon>
                <span>Annuler</span>
            </button>

            <button mat-button (click)="downloadJSON()" class="download-button" [disabled]="isBusy || !isRuleSelected"
                [attr.aria-label]="'Télécharger la règle transformée au format JSON'">
                <mat-icon aria-hidden="true">download</mat-icon>
                <span>Télécharger JSON</span>
            </button>

            <button mat-raised-button (click)="onSave()" class="save-button" [disabled]="!isStepCompleted(5) || isBusy"
                [class.saving]="isSaving"
                [attr.aria-label]="isSaving ? 'Sauvegarde en cours' : 'Sauvegarder la règle transformée'">
                <div class="button-content">
                    <mat-icon *ngIf="!isBusy" aria-hidden="true">save</mat-icon>
                    <div *ngIf="isBusy" class="loading-spinner" aria-hidden="true"></div>
                    <span>{{ isSaving ? 'Sauvegarde...' : 'Sauvegarder' }}</span>
                </div>
            </button>
        </div>
    </div>

    <ngx-ui-loader *ngIf="isBusy && isSaving && !isTransitioning"></ngx-ui-loader>
</div>