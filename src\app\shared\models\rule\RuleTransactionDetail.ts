export interface RuleTransactionDetail {
  RuleId: string;
  RuleEnabled: boolean;
  TransactionId: string;
  ControllerInControl: boolean;
  ControllerId: string | null;
  ControllerIdController: string | null;
  ControllerLocalId: string;
  TransactionCreatedAt: string;
  TransactionCreatedBy: string;
  TransactionLastUpdatedAt: string | null;
  TransactionLastUpdatedBy: string | null;
  RuleTransactionId: string;
  LocalName: string;
  SiteName: string;
  SiteAddress: string;
}
