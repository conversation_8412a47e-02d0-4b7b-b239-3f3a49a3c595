import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { HttpClient } from '@angular/common/http';
import { Facture } from '@app/core/models/facture';
import { environment } from '@app/environments/environment';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class FactureApiService extends ApiService<Facture> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('facture');
  }

  getTotalFacture() {
    return this.http.get<{ Paid: number; Pending: number; Total: number }>(
      `${environment.host}/api/facture/total-per-status`
    );
  }

  genFactureCode(): Observable<string> {
    return this.http.get<string>(
      `${this.baseUrl}facture/generate-code`
    );
  }
}
