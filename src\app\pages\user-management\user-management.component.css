/* User Management Component Styles */

.user-management-container {
  min-height: 100vh;
  max-width: 98%;
  margin-left: 50px; /* Add this line to push content right of the sidebar */
  padding: 24px ; /* Optional: add some padding for top/right/bottom */
  box-sizing: border-box;
}

/* Header Card Container */
.header-card-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
  animation: slideIn 0.5s ease-out;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: #2E7D32;
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
}

.title-icon {
  font-size: 28px !important;
  width: 28px !important;
  height: 28px !important;
  color: #2E7D32;
}

.actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.create-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.create-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
  background: linear-gradient(45deg, #81C784, var(--primary));
}

.action-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
}

/* Users Section */
.users-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 20px;
}

/* Search Section */
.search-section {
  margin: 20px 0;
  padding: 15px;
  border-radius: 8px;
}

.search-container {
  display: flex;
  gap: 10px;
  align-items: center;
  max-width: 500px;
}

.search-input {
  flex: 1;
  max-width: 450px;
  padding: 12px 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f8fafc;
  color: #4a5568;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.search-input::placeholder {
  color: #a0aec0;
}

.search-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.search-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
  background: linear-gradient(45deg, #81C784, var(--primary));
}

.search-button mat-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
}

/* Section Header */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.add-user-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(45deg, #2E7D32, #81C784);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.add-user-btn:hover {
  background: linear-gradient(45deg, #81C784, #2E7D32);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.add-user-btn i {
  font-size: 18px;
}

/* Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #2E7D32;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Table Container */
.table-container {
  margin-bottom: 24px;
}

/* Pagination */
.card-pagination-container, 
.pagination-container {
  margin-top: 24px;
  margin-bottom: 24px;
}

.pagination-container {
  padding: 24px;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.card-pagination-container {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 16px 24px;
  border: 1px solid #f1f5f9;
}

/* Force override Material paginator background - Multiple selectors for specificity */
.pagination-container mat-paginator,
.card-pagination-container mat-paginator,
mat-paginator.mat-mdc-paginator,
.mat-mdc-paginator {
  background: transparent !important;
  background-color: transparent !important;
}

/* Target the specific Material classes with higher specificity */
.pagination-container .mat-mdc-paginator,
.card-pagination-container .mat-mdc-paginator {
  background: none !important;
  background-color: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Style the paginator wrapper div */
.pagination-container .mat-mdc-paginator > div,
.card-pagination-container .mat-mdc-paginator > div {
  background: transparent !important;
}

/* Navigation buttons styling */
.pagination-container .mat-mdc-icon-button,
.card-pagination-container .mat-mdc-icon-button {
  background: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 6px !important;
  margin: 0 4px !important;
  width: 36px !important;
  height: 36px !important;
  transition: all 0.2s ease !important;
}

.pagination-container .mat-mdc-icon-button:hover,
.card-pagination-container .mat-mdc-icon-button:hover {
  background: #e2e8f0 !important;
  border-color: #cbd5e1 !important;
  transform: translateY(-1px);
}

.pagination-container .mat-mdc-icon-button:disabled,
.card-pagination-container .mat-mdc-icon-button:disabled {
  background: #f1f5f9 !important;
  border-color: #e5e7eb !important;
  opacity: 0.5;
  transform: none;
}

/* Page size dropdown */
.pagination-container .mat-mdc-select,
.card-pagination-container .mat-mdc-select {
  background: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 6px !important;
  margin: 0 8px !important;
}

.pagination-container .mat-mdc-select-trigger,
.card-pagination-container .mat-mdc-select-trigger {
  background: transparent !important;
}

/* Range label text */
.pagination-container .mat-mdc-paginator-range-label,
.card-pagination-container .mat-mdc-paginator-range-label {
  color: #475569 !important;
  font-weight: 500 !important;
  margin: 0 16px !important;
}

/* Page size label */
.pagination-container .mat-mdc-paginator-page-size-label,
.card-pagination-container .mat-mdc-paginator-page-size-label {
  color: #64748b !important;
  font-weight: 400 !important;
}

/* Hide form field underlines */
.pagination-container .mat-mdc-form-field-subscript-wrapper,
.card-pagination-container .mat-mdc-form-field-subscript-wrapper {
  display: none !important;
}

/* Alternative: If the above doesn't work, use this global override */
mat-paginator {
  background: transparent !important;
}

mat-paginator .mat-mdc-paginator {
  background: transparent !important;
  background-color: transparent !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pagination-container {
    padding: 16px;
  }
  
  .card-pagination-container {
    padding: 12px 16px;
  }
  
  .pagination-container .mat-mdc-paginator,
  .card-pagination-container .mat-mdc-paginator {
    flex-wrap: wrap;
    gap: 8px;
  }
}

/* No Users Message */
.no-users-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
  text-align: center;
}

.no-users-message i {
  font-size: 64px;
  color: #ccc;
  margin-bottom: 16px;
}

.no-users-message p {
  font-size: 16px;
  margin: 0;
}

/* Popup Overlay */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

/* Popup Form */
.popup-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
  margin-top: 60px; /* Add space from the top/header */
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Popup Header */
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.popup-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2E7D32;
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.close-btn:hover {
  background: #f0f0f0;
}

.close-btn mat-icon {
  color: #666;
  font-size: 24px;
}

/* Validation Errors */
.validation-errors {
  background-color: #fee2e2;
  border-left: 4px solid #ef4444;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.validation-errors-title {
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.validation-errors-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.validation-errors-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 14px;
  color: #dc2626;
}

.error-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #c62828;
}

.validation-errors-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.validation-errors-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #d32f2f;
  font-size: 14px;
}

.validation-errors-list li:last-child {
  margin-bottom: 0;
}

.validation-errors-list mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Site Form Styles */
.site-form {
  padding: 25px;
}

.site-form .form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.site-form .form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.site-form .form-group.full-width {
  grid-column: 1 / -1;
}

.site-form label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.form-group input[readonly] {
  cursor: not-allowed;
  background-color: #f5f5f5;
}

.required {
  color: #e53e3e;
}

.site-form input,
.site-form select,
.site-form textarea {
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.site-form input:focus,
.site-form select:focus,
.site-form textarea:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  outline: none;
}

.error-message {
  font-size: 12px;
  color: #e53e3e;
  margin-top: 4px;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.btn-cancel {
  padding: 10px 20px;
  background: transparent;
  border: 1px solid #cbd5e0;
  color: #718096;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.btn-cancel:hover {
  background-color: #f7fafc;
}

.btn-submit {
  padding: 10px 20px;
  background: linear-gradient(45deg, var(--primary), #81C784);
  color: white;
  border: none;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.btn-submit:hover {
  background: linear-gradient(45deg, #81C784, var(--primary));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.btn-submit:disabled {
  background: #cbd5e0;
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-management-container {
    padding: 16px;
  }

  .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .search-container {
    max-width: none;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .popup-form {
    width: 95%;
    margin: 20px;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-cancel,
  .btn-submit {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .breadcrumb-text {
    font-size: 1.25rem;
  }

  .section-title {
    font-size: 1.25rem;
  }

  .popup-header h3 {
    font-size: 1.1rem;
  }
}

/* Material Design Overrides */
::ng-deep .mat-mdc-paginator {
  background: transparent !important;
}

::ng-deep .mat-mdc-paginator .mat-mdc-paginator-range-label {
  margin: 0 16px !important;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 6px;
}

.btn-primary {
  background: linear-gradient(45deg, #2E7D32, #81C784);
  color: white;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(45deg, #81C784, #2E7D32);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
}
/* Material Design Form Field Styles */
.form-field {
  width: 100%;
}

.form-field.full-width {
  grid-column: 1 / -1;
}

/* Role option styling */
.role-option {
  display: flex;
  flex-direction: column;
  padding: 4px 0;
}

.role-label {
  font-weight: 500;
  color: #333;
}

.role-description {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

/* Material Design Form Field Overrides */
::ng-deep .mat-mdc-form-field {
  width: 100%;
}

::ng-deep .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {
  margin-top: 4px;
}

::ng-deep .mat-mdc-select-panel {
  max-height: 300px;
}

::ng-deep .mat-mdc-option {
  min-height: 60px !important;
  height: auto !important;
  padding: 8px 16px !important;
}

::ng-deep .mat-mdc-option .role-option {
  width: 100%;
}

/* Update form grid for Material Design */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}
