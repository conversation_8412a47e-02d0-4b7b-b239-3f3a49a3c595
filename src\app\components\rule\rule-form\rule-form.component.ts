import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { DragDropModule, CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { forkJoin } from 'rxjs';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';
import { NgToastModule, NgToastService, TOAST_POSITIONS,  } from 'ng-angular-popup';

// Import the services
import { TypeCapteurApiService } from '@app/core/services/administrative/typecapteur.service';
import { VariablesApiService } from '@app/core/services/administrative/variables.service';
import { RulesApiService } from '@app/core/services/administrative/rules.service';

// Import the backend models
import { TypeCapteur } from '@app/shared/models/typeCapteur';
import { Variables } from '@app/core/models/variables';
import { PayloadOption } from '../../../shared/models/PayloadOption';
import { DeviceProperty } from '../../../shared/models/DeviceProperty';
import { DeviceAction } from '../../../shared/models/DeviceAction';
import { DeviceTypes } from '../../../shared/models/DeviceTypes';
import { SensorDataCondition } from '../../../shared/models/SensorDataCondition';
import { TimeCondition } from '../../../shared/models/TimeCondition';
import { ConditionGroup } from '../../../shared/models/ConditionGroup';
import { PublishAction } from '../../../shared/models/PublishAction';
import { RuleDto } from '../../../shared/models/RuleDto';

import { ConfirmationDialogComponent} from '../../confirmation-dialog/confirmation-dialog.component';

// Define operator interface for better type safety
interface OperatorOption {
  label: string;
  value: string;
  types: string[]; // Which variable types support this operator
}
interface ValidationResult {
  errors: string[];
  warnings: string[];
  isValid: boolean;
}
@Component({
  selector: 'app-rule-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    DragDropModule,
    NgxUiLoaderModule, 
    NgToastModule
  ],
  templateUrl: './rule-form.component.html',
  styleUrls: ['./rule-form.component.css']
})
export class RuleFormComponent implements OnInit {
  rule: RuleDto = {
    rule_name: '',
    topic_pattern: [],
    conditions: {
      operator: 'AND',
      groups: [
        {
          operator: 'AND',
          conditions: []
        }
      ]
    },
    actions: [],
    schedule_config: { enabled: false },
    enabled: true,
    priority: 1
  };

  priorities = [1, 2, 3, 4, 5];

  // Operators filtered by variable type
  allOperators: OperatorOption[] = [
    { label: '=', value: '==', types: ['string', 'number', 'boolean'] },
    { label: '≠', value: '!=', types: ['string', 'number', 'boolean'] },
    { label: '>', value: '>', types: ['number'] },
    { label: '<', value: '<', types: ['number'] },
    { label: '≥', value: '>=', types: ['number'] },
    { label: '≤', value: '<=', types: ['number'] }
  ];

  operator = ['AND', 'OR'];
  selectedActionTypes: string[] = [];
  selectedDevices: string[] = [];

  // Backend data
  typeCapteurs: TypeCapteur[] = [];
  variables: Variables[] = [];
  
  // Processed device data structure
  deviceTypes: DeviceTypes = {
    sensors: {},
    actuators: {}
  };
  TOAST_POSITIONS = TOAST_POSITIONS;
  // Loading states
  isLoading = true;
  loadingError: string | null = null;
  isSaving = false;
  isGeneratingSummary = false;

  jsonPreview: string = '';

  constructor(
    public dialogRef: MatDialogRef<RuleFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { rule: RuleDto | null },
    private typeCapteurService: TypeCapteurApiService,
    private variablesService: VariablesApiService,
    private rulesService: RulesApiService,
    private dialog: MatDialog,
    private ngxUiLoaderService: NgxUiLoaderService,
    private toast: NgToastService // <-- add toast service
  ) {}

  ngOnInit(): void {
    this.loadBackendData();
    if (!this.rule.conditions.operator) {
      this.rule.conditions.operator = 'AND';
    }
    this.updateJsonPreview();
  }

  // === ENHANCED OPERATOR LOGIC BY VARIABLE TYPE ===

  /**
   * Get available operators for a specific condition based on the selected property type
   */
  getAvailableOperators(condition: SensorDataCondition): OperatorOption[] {
    const selectedProperty = this.getSelectedProperty(condition);
    
    if (!selectedProperty) {
      return this.allOperators;
    }

    const propertyType = selectedProperty.type.toLowerCase();
    
    switch (propertyType) {
      case 'boolean':
        return this.allOperators.filter(op => ['==', '!='].includes(op.value));
      case 'number':
      case 'float':
      case 'double':
        return this.allOperators;
      case 'string':
      default:
        return this.allOperators.filter(op => ['==', '!='].includes(op.value));
    }
  }

  /**
   * Get suggested values for a condition based on the Actions array from backend
   * Filters out placeholder values for clean UI
   */
  getSuggestedValues(condition: SensorDataCondition): string[] {
    const selectedProperty = this.getSelectedProperty(condition);
    
    if (!selectedProperty || !selectedProperty.values || selectedProperty.values.length === 0) {
      return [];
    }

    // Convert all values to strings and filter out placeholder values
    return selectedProperty.values
      .map(val => String(val))
      .filter(val => val && val.trim() !== '' && val !== 'placeholder' && val !== 'select' && val !== 'valeur');
  }

  /**
   * Get the first suggested value for initializing the dropdown
   */
  getFirstSuggestedValue(condition: SensorDataCondition): string {
    const suggestions = this.getSuggestedValues(condition);
    return suggestions.length > 0 ? suggestions[0] : '';
  }

  /**
   * Check if a condition has suggested values available
   */
  hasSuggestedValues(condition: SensorDataCondition): boolean {
    return this.getSuggestedValues(condition).length > 0;
  }

  /**
   * Get the display value for the select dropdown with auto-initialization
   */
  getSelectDisplayValue(condition: SensorDataCondition): string {
    if (condition.value) {
      return String(condition.value);
    }
    
    // Auto-initialize with first suggested value if available
    const firstSuggested = this.getFirstSuggestedValue(condition);
    if (firstSuggested) {
      condition.value = this.parseValueForCondition(condition, firstSuggested);
      return firstSuggested;
    }
    
    return '';
  }

  /**
   * Get placeholder text for value input based on property type
   */
  getValuePlaceholder(condition: SensorDataCondition): string {
    const selectedProperty = this.getSelectedProperty(condition);
    
    if (!selectedProperty) {
      return 'Sélectionner d\'abord une propriété';
    }

    const propertyType = selectedProperty.type.toLowerCase();
    
    switch (propertyType) {
      case 'boolean':
        return 'true ou false';
      case 'number':
      case 'integer':
      case 'float':
      case 'double':
        return 'Entrer une valeur numérique';
      case 'string':
      default:
        return 'Entrer une valeur texte';
    }
  }

  /**
   * Validate value input based on property type
   */
  isValidValue(condition: SensorDataCondition, value: any): boolean {
    const selectedProperty = this.getSelectedProperty(condition);
    
    if (!selectedProperty) {
      return false;
    }

    if (value === null || value === undefined || value === '') {
      return false;
    }

    const propertyType = selectedProperty.type.toLowerCase();
    
    switch (propertyType) {
      case 'boolean':
        return value === true || value === false || value === 'true' || value === 'false';
      case 'number':
      case 'integer':
      case 'float':
      case 'double':
        return !isNaN(Number(value)) && isFinite(Number(value));
      case 'string':
      default:
        return true;
    }
  }

  /**
   * Parse value from input to the correct type for the backend
   */
  parseValueForCondition(condition: SensorDataCondition, inputValue: string): any {
    const selectedProperty = this.getSelectedProperty(condition);
    
    if (!selectedProperty || !inputValue) {
      return inputValue;
    }

    const propertyType = selectedProperty.type.toLowerCase();

    switch (propertyType) {
      case 'boolean':
        if (inputValue === 'true') return true;
        if (inputValue === 'false') return false;
        return inputValue;
      case 'number':
      case 'integer':
        const intValue = parseInt(inputValue, 10);
        return isNaN(intValue) ? inputValue : intValue;
      case 'float':
      case 'double':
        const floatValue = parseFloat(inputValue);
        return isNaN(floatValue) ? inputValue : floatValue;
      case 'string':
      default:
        return String(inputValue);
    }
  }

  // === BACKEND DATA LOADING ===

  private loadBackendData(): void {
    this.isLoading = true;
    this.loadingError = null;

    forkJoin({
      typeCapteurs: this.typeCapteurService.getAll(),
      variables: this.variablesService.getAll()
    }).subscribe({
      next: (data) => {
        this.typeCapteurs = data.typeCapteurs;
        this.variables = data.variables;
        this.processBackendData();
        this.initializeRule();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading backend data:', error);
        this.loadingError = 'Erreur lors du chargement des données. Veuillez réessayer.';
        this.isLoading = false;
        this.initializeRule();
      }
    });
  }

  private processBackendData(): void {
    this.deviceTypes = { sensors: {}, actuators: {} };

    this.typeCapteurs.forEach(typeCapteur => {
      const relatedVariables = this.variables.filter(v => v.IdTypeCapteur === typeCapteur.Id);
      
      const deviceCategory: 'sensors' | 'actuators' = 
        typeCapteur.DeviceType?.toLowerCase() === 'actuator' ? 'actuators' : 'sensors';
      
      const deviceKey = this.normalizeDeviceKey(typeCapteur.Nom);
      
      if (deviceCategory === 'sensors') {
        this.deviceTypes.sensors[deviceKey] = {
          topic: typeCapteur.Topic,
          device_name: typeCapteur.Nom,
          DisplayName: typeCapteur.DisplayName || typeCapteur.Nom,
          properties: relatedVariables.map(variable => {
            const declaredType = this.mapVariableType(variable.Type);
            const actualType = this.detectActualType(variable.Actions, declaredType);
            return {
              key: variable.Key,
              type: actualType,
              values: this.parseVariableValues(variable.Actions)
            };
          })
        };
      } else {
        this.deviceTypes.actuators[deviceKey] = {
          topic: typeCapteur.Topic,
          device_name: typeCapteur.Nom,
          DisplayName: typeCapteur.DisplayName || typeCapteur.Nom,
          actions: relatedVariables.map(variable => ({
            type: variable.Key,
            payload: {},
            options: this.parseActionOptions(variable.Actions)
          }))
        };
      }
    });
    
  }

  private normalizeDeviceKey(name: string): string {
    return name.toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^a-z0-9_]/g, '')
      .replace(/^sensor_|^actuator_/, '');
  }

  private mapVariableType(backendType: string): string {
    const typeMapping: { [key: string]: string } = {
      'String': 'string',
      'Integer': 'number',
      'Float': 'number',
      'Double': 'number',
      'Boolean': 'boolean',
      'Bool': 'boolean',
      'Number': 'number'
    };
    return typeMapping[backendType] || 'string';
  }

  private parseVariableValues(actions: string[] | null): string[] {
    if (!actions || !Array.isArray(actions)) return [];
    return actions.map(item => String(item));
  }

  private detectActualType(actions: string[] | null, declaredType: string): string {
    if (!actions || actions.length === 0) {
      return declaredType;
    }

    const allBooleanLike = actions.every(action => 
      action === 'true' || action === 'false'
    );
    
    if (allBooleanLike) {
      return 'boolean';
    }

    const allNumeric = actions.every(action => {
      const num = Number(action);
      return !isNaN(num) && isFinite(num);
    });
    
    if (allNumeric) {
      return 'number';
    }

    return declaredType;
  }

  private parseActionOptions(actions: string[] | null): PayloadOption[] {
    if (!actions || !Array.isArray(actions)) return [];
    return actions.map((item: string) => ({
      display: item,
      value: item
    }));
  }

  private initializeRule(): void {
    if (this.data.rule) {
      if (typeof this.data.rule === 'object' && 'RawData' in this.data.rule) {
        try {
          this.rule = JSON.parse((this.data.rule as any).RawData);
        } catch (error) {
          console.error('Error parsing rule RawData:', error);
          this.rule = JSON.parse(JSON.stringify(this.data.rule));
        }
      } else {
        this.rule = JSON.parse(JSON.stringify(this.data.rule));
      }
    } else {
      if (this.rule.conditions.groups.length === 0) {
        this.addConditionGroup();
      }
      if (this.rule.conditions.groups[0].conditions.length === 0) {
        this.addCondition(0);
      }
    }
    
    if (!this.rule.conditions.operator) {
      this.rule.conditions.operator = 'AND';
    }
    
    // Set initial schedule config based on existing time conditions
    this.updateScheduleConfig();
    this.updateTopicPattern();
  }

  // === CONDITION CHANGE HANDLERS ===

  onConditionDeviceChange(condition: SensorDataCondition, deviceType: 'sensors' | 'actuators', deviceName: string): void {
    const oldDevice = condition.device;
    condition.device = deviceName;
    condition.key = '';
    condition.operator = '==';
    condition.value = '';
    
    this.updateTopicPattern();
    
    // Provide guidance about property selection
    if (deviceName && deviceName !== oldDevice) {
      const properties = this.getPropertiesForDevice('sensors', deviceName);
      if (properties.length === 0) {
        this.toast.warning(`Le capteur "${deviceName}" n'a pas de propriétés configurées.`, 'Attention', 3000);
      } else {
        this.toast.info(`Capteur sélectionné: ${properties.length} propriété(s) disponible(s).`, 'Information', 2000);
      }
    }
  }

  onConditionKeyChange(condition: SensorDataCondition): void {
    const oldKey = condition.key;
    condition.value = '';
    
    const availableOperators = this.getAvailableOperators(condition);
    if (availableOperators.length > 0 && !availableOperators.some(op => op.value === condition.operator)) {
      condition.operator = availableOperators[0].value;
    }
    
    // Auto-select first suggested value if available
    const firstSuggestedValue = this.getFirstSuggestedValue(condition);
    if (firstSuggestedValue) {
      condition.value = this.parseValueForCondition(condition, firstSuggestedValue);
    }
    
    this.updateTopicPattern();
    
    // Provide feedback about value options
    if (condition.key && condition.key !== oldKey) {
      const suggestedValues = this.getSuggestedValues(condition);
      const propertyType = this.getSelectedProperty(condition)?.type;
      
      if (suggestedValues.length > 0) {
        this.toast.info(`Propriété sélectionnée (${propertyType}): ${suggestedValues.length} valeur(s) prédéfinie(s) disponible(s).`, 'Information', 2500);
      } else {
        this.toast.info(`Propriété sélectionnée (${propertyType}): Saisissez une valeur manuellement.`, 'Information', 2500);
      }
    }
  }

  onConditionOperatorChange(condition: SensorDataCondition): void {
    const availableOperators = this.getAvailableOperators(condition);
    if (!availableOperators.some(op => op.value === condition.operator)) {
      if (availableOperators.length > 0) {
        condition.operator = availableOperators[0].value;
      }
    }
    this.updateTopicPattern();
  }

  onConditionValueChange(condition: SensorDataCondition, newValue?: string): void {
    if (newValue !== undefined) {
      condition.value = this.parseValueForCondition(condition, newValue);
    }
    this.updateTopicPattern();
  }

  onTimeConditionChange(condition: TimeCondition): void {
    // Update schedule config whenever time condition changes
    this.updateScheduleConfig();
    this.updateTopicPattern();
  }

  onPropertySelected(condition: SensorDataCondition): void {
    const firstSuggestedValue = this.getFirstSuggestedValue(condition);
    if (firstSuggestedValue && !condition.value) {
      condition.value = this.parseValueForCondition(condition, firstSuggestedValue);
      this.updateTopicPattern();
    }
  }

  updateConditionValue(condition: SensorDataCondition, event: Event): void {
    const target = event.target as HTMLInputElement;
    const rawValue = target.value;
    const parsedValue = this.parseValueForCondition(condition, rawValue);
    condition.value = parsedValue;
    this.updateTopicPattern();
  }

  updateConditionValueFromSelect(condition: SensorDataCondition, event: Event): void {
    const target = event.target as HTMLSelectElement;
    const selectedValue = target.value;
    condition.value = this.parseValueForCondition(condition, selectedValue);
    this.updateTopicPattern();
  }

  onConditionValueBlur(condition: SensorDataCondition, event: Event): void {
    const target = event.target as HTMLInputElement;
    const value = target.value;
    
    if (value && !this.isValidValue(condition, value)) {
      console.warn('Invalid value entered:', value);
    }
  }

  // === CONDITION MANAGEMENT (IMPROVED UX - FEWER DIALOGS) ===

  addConditionGroup(): void {
    this.rule.conditions.groups.push({
      operator: 'AND',
      conditions: [{ type: 'payload', device: '', key: '', operator: '==', value: '' } as SensorDataCondition]
    });
    this.updateTopicPattern();
  }
  get hasTimeConditions(): boolean {
    return this.rule.conditions.groups.some(group => 
      group.conditions.some(condition => condition.type === 'time')
    );
  }
  get scheduleStatusText(): string {
    if (this.hasTimeConditions) {
      return 'Planification automatiquement activée (conditions horaires détectées)';
    } else {
      return 'Aucune condition horaire - planification désactivée';
    }
  }

  private isConditionComplete(condition: SensorDataCondition): boolean {
    return !!(
      condition.device && 
      condition.key && 
      condition.operator && 
      condition.value !== null && 
      condition.value !== undefined && 
      condition.value !== ''
    );
  }

  private isTimeConditionComplete(condition: TimeCondition): boolean {
    return !!(condition.start_time && condition.end_time);
  }

  private isAnyConditionComplete(condition: Condition): boolean {
    if (condition.type === 'payload') {
      return this.isConditionComplete(condition as SensorDataCondition);
    } else if (condition.type === 'time') {
      return this.isTimeConditionComplete(condition as TimeCondition);
    }
    return false;
  }
  private isActionComplete(action: PublishAction): boolean {
    if (action.type === 'log') {
      return !!(action.message && action.message.trim() !== '');
    } else {
      // For device actions, check topic, type, and payload
      const hasBasicInfo = !!(action.topic && action.type);
      if (!hasBasicInfo) return false;
      
      // Check if payload has the required key with a value
      if (!action.payload || typeof action.payload !== 'object') return false;
      
      const payloadValue = action.payload[action.type];
      return payloadValue !== null && payloadValue !== undefined && payloadValue !== '';
    }
  }
  private getConditionSignature(condition: SensorDataCondition): string {
    return `${condition.device}|${condition.key}|${condition.operator}|${JSON.stringify(condition.value)}`;
  }
  private getActionSignature(action: PublishAction): string {
    if (action.type === 'log') {
      return `log|${action.message}`;
    }
    return `${action.topic}|${action.type}|${JSON.stringify(action.payload)}`;
  }
  private validateRuleLogic(): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
  
    // 1. Check if rule has meaningful content
    if (!this.rule.rule_name || this.rule.rule_name.trim() === '') {
      errors.push('Le nom de la règle est obligatoire.');
    }
  
    // 2. Validate conditions
    let hasValidConditions = false;
    const allConditionSignatures = new Set<string>();
  
    for (let groupIndex = 0; groupIndex < this.rule.conditions.groups.length; groupIndex++) {
      const group = this.rule.conditions.groups[groupIndex];
      const groupConditionSignatures = new Set<string>();
      let validConditionsInGroup = 0;
  
      for (let condIndex = 0; condIndex < group.conditions.length; condIndex++) {
        const condition = group.conditions[condIndex];
  
        // Check if condition is complete
        if (!this.isAnyConditionComplete(condition)) {
          errors.push(`Groupe ${groupIndex + 1}, Condition ${condIndex + 1}: Tous les champs doivent être remplis.`);
          continue;
        }
  
        validConditionsInGroup++;
        hasValidConditions = true;
  
        // For sensor conditions, check for duplicates based on group operator
        if (condition.type === 'payload') {
          const sensorCondition = condition as SensorDataCondition;
          const signature = this.getConditionSignature(sensorCondition);
  
          // Global duplicate check (across all groups)
          if (allConditionSignatures.has(signature)) {
            errors.push(`Condition dupliquée détectée: "${sensorCondition.device} ${sensorCondition.key} ${sensorCondition.operator} ${sensorCondition.value}"`);
          }
          allConditionSignatures.add(signature);
  
          // Group-level duplicate check with smart logic
          if (groupConditionSignatures.has(signature)) {
            if (group.operator === 'AND') {
              errors.push(`Groupe ${groupIndex + 1}: Condition dupliquée avec opérateur AND (contradictoire). La même condition ne peut pas être vraie ET vraie.`);
            } else if (group.operator === 'OR') {
              warnings.push(`Groupe ${groupIndex + 1}: Condition dupliquée avec opérateur OR (redondante). Cette condition est répétée inutilement.`);
            }
          }
          groupConditionSignatures.add(signature);
  
          // Check for logical conflicts within the same group with AND operator
          if (group.operator === 'AND') {
            const conflictingConditions = Array.from(groupConditionSignatures).filter(sig => {
              const [device, key] = sig.split('|');
              const [currentDevice, currentKey] = signature.split('|');
              return device === currentDevice && key === currentKey && sig !== signature;
            });
  
            if (conflictingConditions.length > 0) {
              errors.push(`Groupe ${groupIndex + 1}: Conflit logique détecté. Avec l'opérateur AND, "${sensorCondition.device}.${sensorCondition.key}" ne peut pas avoir plusieurs valeurs simultanément.`);
            }
          }
  
          // Validate condition value type
          if (!this.isValidValue(sensorCondition, sensorCondition.value)) {
            errors.push(`Groupe ${groupIndex + 1}, Condition ${condIndex + 1}: Valeur "${sensorCondition.value}" invalide pour le type de propriété.`);
          }
        }
      }
  
      // Check if group has at least one valid condition
      if (validConditionsInGroup === 0) {
        errors.push(`Groupe ${groupIndex + 1}: Aucune condition valide. Chaque groupe doit contenir au moins une condition complète.`);
      }
    }
  
    if (!hasValidConditions) {
      errors.push('La règle doit contenir au moins une condition valide.');
    }
  
    // 3. Validate actions
    let hasValidActions = false;
    const actionSignatures = new Set<string>();
  
    for (let actionIndex = 0; actionIndex < this.rule.actions.length; actionIndex++) {
      const action = this.rule.actions[actionIndex];
  
      // Check if action is complete
      if (!this.isActionComplete(action)) {
        if (action.type === 'log') {
          errors.push(`Action ${actionIndex + 1}: Le message de log est obligatoire.`);
        } else {
          errors.push(`Action ${actionIndex + 1}: L'actionneur, le type d'action et la valeur sont obligatoires.`);
        }
        continue;
      }
  
      hasValidActions = true;
  
      // Check for duplicate actions
      const signature = this.getActionSignature(action);
      if (actionSignatures.has(signature)) {
        warnings.push(`Action dupliquée détectée: "${action.type}" sur "${action.topic || 'log'}"`);
      }
      actionSignatures.add(signature);
    }
  
    if (!hasValidActions) {
      errors.push('La règle doit contenir au moins une action valide.');
    }
  
    // 4. Advanced logical validation
    this.validateAdvancedLogic(errors, warnings);
  
    return { errors, warnings, isValid: errors.length === 0 };
  }

  private validateAdvancedLogic(errors: string[], warnings: string[]): void {
    // Check for contradictory conditions across groups with global AND
    if (this.rule.conditions.operator === 'AND') {
      const deviceKeyConditions = new Map<string, SensorDataCondition[]>();
      
      // Collect all sensor conditions by device.key
      this.rule.conditions.groups.forEach(group => {
        group.conditions.forEach(condition => {
          if (condition.type === 'payload') {
            const sensorCondition = condition as SensorDataCondition;
            if (this.isConditionComplete(sensorCondition)) {
              const deviceKey = `${sensorCondition.device}.${sensorCondition.key}`;
              if (!deviceKeyConditions.has(deviceKey)) {
                deviceKeyConditions.set(deviceKey, []);
              }
              deviceKeyConditions.get(deviceKey)!.push(sensorCondition);
            }
          }
        });
      });
  
      // Check for impossible conditions
      deviceKeyConditions.forEach((conditions, deviceKey) => {
        if (conditions.length > 1) {
          const hasConflict = this.checkForValueConflicts(conditions);
          if (hasConflict) {
            errors.push(`Conflit logique global: "${deviceKey}" a des conditions contradictoires avec l'opérateur global AND.`);
          }
        }
      });
    }
  
    // Check for unreachable actions (actions that can never be triggered)
    this.validateActionReachability(warnings);
  
    // Check for time condition conflicts
    this.validateTimeConditions(errors, warnings);
  }

  private checkForValueConflicts(conditions: SensorDataCondition[]): boolean {
    // For equality operators, different values create conflicts
    const equalityConditions = conditions.filter(c => c.operator === '==');
    if (equalityConditions.length > 1) {
      const values = new Set(equalityConditions.map(c => JSON.stringify(c.value)));
      if (values.size > 1) {
        return true; // Same property cannot equal different values simultaneously
      }
    }
  
    // Check for range conflicts (e.g., > 10 AND < 5)
    const numericConditions = conditions.filter(c => 
      ['>', '<', '>=', '<='].includes(c.operator) && 
      typeof c.value === 'number'
    );
  
    if (numericConditions.length > 1) {
      // Simple conflict detection for numeric ranges
      const gtConditions = numericConditions.filter(c => c.operator === '>' || c.operator === '>=');
      const ltConditions = numericConditions.filter(c => c.operator === '<' || c.operator === '<=');
  
      for (const gtCond of gtConditions) {
        for (const ltCond of ltConditions) {
          const gtValue = gtCond.operator === '>=' ? gtCond.value : (gtCond.value as number) + 0.1;
          const ltValue = ltCond.operator === '<=' ? ltCond.value : (ltCond.value as number) - 0.1;
          
          if (gtValue > ltValue) {
            return true; // Impossible range (e.g., > 10 AND < 5)
          }
        }
      }
    }
  
    return false;
  }

  private validateActionReachability(warnings: string[]): void {
    // This is a simplified check - you can enhance based on your specific logic
    const usedDevices = new Set<string>();
    
    // Collect devices used in conditions
    this.rule.conditions.groups.forEach(group => {
      group.conditions.forEach(condition => {
        if (condition.type === 'payload') {
          const sensorCondition = condition as SensorDataCondition;
          if (this.isConditionComplete(sensorCondition)) {
            usedDevices.add(sensorCondition.device);
          }
        }
      });
    });
  
    // Check if actions reference devices that are never mentioned in conditions
    this.rule.actions.forEach((action, index) => {
      if (this.isActionComplete(action) && action.type !== 'log') {
        const actionDeviceKey = this.getDeviceNameKeyByTopic(action.topic, 'actuators');
        if (actionDeviceKey && usedDevices.size > 0 && !usedDevices.has(actionDeviceKey)) {
          warnings.push(`Action ${index + 1}: Cet actionneur n'est pas référencé dans les conditions. L'action pourrait être déclenchée de manière inattendue.`);
        }
      }
    });
  }
  
  private validateTimeConditions(errors: string[], warnings: string[]): void {
    const timeConditions: TimeCondition[] = [];
    
    // Collect all time conditions
    this.rule.conditions.groups.forEach((group, groupIndex) => {
      group.conditions.forEach((condition, condIndex) => {
        if (condition.type === 'time' && this.isTimeConditionComplete(condition as TimeCondition)) {
          timeConditions.push(condition as TimeCondition);
        } else if (condition.type === 'time') {
          errors.push(`Groupe ${groupIndex + 1}, Condition ${condIndex + 1}: Les heures de début et fin sont obligatoires.`);
        }
      });
    });
  
    // Check for invalid time ranges
    timeConditions.forEach((condition, index) => {
      const startTime = condition.start_time;
      const endTime = condition.end_time;
      
      if (startTime && endTime) {
        const start = new Date(`2000-01-01T${startTime}`);
        const end = new Date(`2000-01-01T${endTime}`);
        
        if (start >= end) {
          errors.push(`Condition horaire ${index + 1}: L'heure de fin doit être postérieure à l'heure de début.`);
        }
        
        // Check for very short time windows (potential warning)
        const diffMinutes = (end.getTime() - start.getTime()) / (1000 * 60);
        if (diffMinutes < 5) {
          warnings.push(`Condition horaire ${index + 1}: Fenêtre de temps très courte (${diffMinutes} minutes). Vérifiez si c'est intentionnel.`);
        }
      }
    });
  }
  private updateScheduleConfig(): void {
    const hasTimeConditions = this.rule.conditions.groups.some(group => 
      group.conditions.some(condition => condition.type === 'time')
    );
    
    // Auto-enable schedule when time conditions exist, disable when none exist
    this.rule.schedule_config.enabled = hasTimeConditions;
    
    // If no time conditions, also clear any schedule configuration
    if (!hasTimeConditions) {
      this.rule.schedule_config = { enabled: false };
    }
  }
  addCondition(groupIndex: number): void {
    const newCondition: SensorDataCondition = {
      type: 'payload',
      device: '',
      key: '',
      operator: '==',
      value: ''
    };
    
    this.rule.conditions.groups[groupIndex].conditions.push(newCondition);
    this.updateScheduleConfig();
    this.updateTopicPattern();
    
    // Provide helpful guidance for the user
    this.showConditionGuidance(groupIndex);
  }
  private showConditionGuidance(groupIndex: number): void {
    const group = this.rule.conditions.groups[groupIndex];
    const conditionCount = group.conditions.length;
    
    if (conditionCount === 1) {
      this.toast.info('Première condition ajoutée. Configurez le capteur, la propriété et la valeur.', 'Guide', 3000);
    } else if (conditionCount > 1) {
      const operatorText = group.operator === 'AND' ? 'toutes les conditions' : 'au moins une condition';
      this.toast.info(`Nouvelle condition ajoutée. Avec l'opérateur ${group.operator}, ${operatorText} du groupe doivent être vraies.`, 'Guide', 3000);
    }
  }
  addTimeCondition(groupIndex: number): void {
    this.rule.conditions.groups[groupIndex].conditions.push({
      type: 'time',
      start_time: '',
      end_time: ''
    } as TimeCondition);
    
    // Update schedule config when time condition is added
    this.updateScheduleConfig();
    this.updateTopicPattern();
  }

  // ENHANCED: Only show dialog for groups with significant content
  removeConditionGroup(index: number): void {
    const group = this.rule.conditions.groups[index];
    
    // Only show confirmation for groups with meaningful content
    const hasMeaningfulContent = this.hasMeaningfulGroupContent(group);
    
    if (hasMeaningfulContent) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Supprimer le groupe',
          message: `Ce groupe contient des conditions avec des données. Voulez-vous le supprimer ?`,
          icon: 'delete_forever'
        },
        disableClose: true,
        panelClass: 'confirmation-dialog-panel'
      });
  
      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.rule.conditions.groups.splice(index, 1);
          
          // Update schedule config when group is removed (might contain time conditions)
          this.updateScheduleConfig();
          this.updateTopicPattern();
        }
      });
    } else {
      // Directly remove empty or meaningless groups
      this.rule.conditions.groups.splice(index, 1);
      
      // Update schedule config when group is removed
      this.updateScheduleConfig();
      this.updateTopicPattern();
    }
  }

  /**
   * Check if a condition group has meaningful content worth confirming deletion
   */
  private hasMeaningfulGroupContent(group: ConditionGroup): boolean {
    return group.conditions.some(condition => this.isAnyConditionComplete(condition));
  }

  // IMPROVED: No dialog for single condition removal - better UX
  removeCondition(groupIndex: number, conditionIndex: number): void {
    const condition = this.rule.conditions.groups[groupIndex].conditions[conditionIndex];
    const wasComplete = this.isAnyConditionComplete(condition);
    
    this.rule.conditions.groups[groupIndex].conditions.splice(conditionIndex, 1);
    this.updateScheduleConfig();
    this.updateTopicPattern();
    
    // Show warning if removing a complete condition
    if (wasComplete) {
      this.toast.info('Condition complète supprimée.', 'Information', 2000);
    }
    
    // Check if group is now empty and provide guidance
    const remainingConditions = this.rule.conditions.groups[groupIndex].conditions.length;
    if (remainingConditions === 0) {
      this.toast.warning('Ce groupe n\'a plus de conditions. Ajoutez une condition ou supprimez le groupe.', 'Attention', 3000);
    }
  }

  // === DRAG & DROP METHODS ===

  onGroupDrop(event: CdkDragDrop<ConditionGroup[]>): void {
    if (event.previousContainer === event.container) {
      if (event.previousIndex !== event.currentIndex) {
        moveItemInArray(
          this.rule.conditions.groups,
          event.previousIndex,
          event.currentIndex
        );
        this.updateTopicPattern();
      }
    }
  }

  onConditionDrop(event: CdkDragDrop<Condition[]>, targetGroupIndex: number): void {
    if (event.previousContainer === event.container) {
      if (event.previousIndex !== event.currentIndex) {
        moveItemInArray(
          this.rule.conditions.groups[targetGroupIndex].conditions,
          event.previousIndex,
          event.currentIndex
        );
        this.updateTopicPattern();
      }
    } else {
      const sourceGroupIndex = this.findGroupIndexByContainer(event.previousContainer.data);
      if (sourceGroupIndex !== -1 && sourceGroupIndex !== targetGroupIndex) {
        transferArrayItem(
          this.rule.conditions.groups[sourceGroupIndex].conditions,
          this.rule.conditions.groups[targetGroupIndex].conditions,
          event.previousIndex,
          event.currentIndex
        );
        
        // Update schedule config when conditions are moved between groups
        this.updateScheduleConfig();
        this.updateTopicPattern();
      }
    }
  }

  onActionDrop(event: CdkDragDrop<PublishAction[]>): void {
    if (event.previousContainer === event.container) {
      if (event.previousIndex !== event.currentIndex) {
        moveItemInArray(
          this.rule.actions,
          event.previousIndex,
          event.currentIndex
        );
        this.updateTopicPattern();
      }
    }
  }

  private findGroupIndexByContainer(containerData: Condition[]): number {
    for (let i = 0; i < this.rule.conditions.groups.length; i++) {
      if (this.rule.conditions.groups[i].conditions === containerData) {
        return i;
      }
    }
    return -1;
  }

  // === TRACKING METHODS ===

  trackByGroupIndex(index: number, group: ConditionGroup): string {
    const conditionCount = group.conditions.length;
    const hasTimeCondition = group.conditions.some(c => c.type === 'time');
    const hasSensorCondition = group.conditions.some(c => c.type === 'payload');
    
    return `group-${index}-${group.operator}-${conditionCount}-${hasTimeCondition ? 'time' : ''}-${hasSensorCondition ? 'sensor' : ''}`;
  }

  trackByConditionIndex(index: number, condition: Condition): string {
    if (condition.type === 'payload') {
      const sensorCondition = condition as SensorDataCondition;
      return `condition-${index}-${sensorCondition.type}-${sensorCondition.device || 'no-device'}-${sensorCondition.key || 'no-key'}`;
    } else if (condition.type === 'time') {
      return `condition-${index}-time`;
    }
    return `condition-${index}-unknown`;
  }

  trackByActionIndex(index: number, action: PublishAction): string {
    if (action.type === 'log') {
      return `action-${index}-log`;
    } else {
      return `action-${index}-${action.type || 'unknown'}`;
    }
  }

  trackBySuggestion(index: number, suggestion: string): string {
    return suggestion;
  }

  // === ACTION MANAGEMENT (IMPROVED UX) ===

  addAction(): void {
    // Check if there are available actuators
    const availableActuators = this.getActuatorDeviceNames();
    if (availableActuators.length === 0) {
      this.toast.warning('Aucun actionneur disponible. Vérifiez la configuration des appareils.', 'Attention', 3000);
      return;
    }
    
    this.rule.actions.push({
      type: '',
      payload: {},
      topic: ''
    } as PublishAction);
    
    this.updateTopicPattern();
    
    // Provide guidance
    this.toast.info('Nouvelle action ajoutée. Sélectionnez un actionneur et configurez l\'action.', 'Guide', 3000);
  }

  addLogAction(): void {
    this.rule.actions.push({
      type: 'log',
      topic: '',
      payload: {},
      message: ''
    } as PublishAction);
    
    this.updateTopicPattern();
    this.toast.info('Action de log ajoutée. Saisissez le message à enregistrer.', 'Guide', 3000);
  }

  onActionDeviceChange(action: PublishAction, selectedTopic: string): void {
    let deviceNameKey: string | undefined;
    for (const key in this.deviceTypes.actuators) {
      if (this.deviceTypes.actuators[key].topic === selectedTopic) {
        deviceNameKey = key;
        break;
      }
    }
  
    if (deviceNameKey && selectedTopic) {
      const selectedDevice = this.deviceTypes.actuators[deviceNameKey];
      if (selectedDevice) {
        action.topic = selectedDevice.topic;
        action.type = '';
        action.payload = {};
        
        // Provide feedback about available actions
        const availableActions = selectedDevice.actions || [];
        if (availableActions.length === 0) {
          this.toast.warning(`L'actionneur "${selectedDevice.DisplayName || selectedDevice.device_name}" n'a pas d'actions configurées.`, 'Attention', 3000);
        } else {
          this.toast.info(`Actionneur sélectionné: ${availableActions.length} action(s) disponible(s).`, 'Information', 2000);
        }
      }
    } else {
      action.topic = '';
      action.type = '';
      action.payload = {};
    }
    
    this.updateTopicPattern();
  }

  onActionTypeChange(action: PublishAction, selectedActionType: string): void {
    action.type = selectedActionType;
    
    if (selectedActionType && selectedActionType !== 'log') {
      action.payload = action.payload || {};
      if (!(selectedActionType in action.payload)) {
        action.payload[selectedActionType] = '';
      }
      
      // Provide feedback about payload options
      const selectedActionConfig = this.getSelectedActionType(action);
      if (selectedActionConfig?.options && selectedActionConfig.options.length > 0) {
        this.toast.info(`Action sélectionnée: ${selectedActionConfig.options.length} option(s) de valeur disponible(s).`, 'Information', 2000);
      } else {
        this.toast.info('Action sélectionnée: Saisissez une valeur personnalisée pour le payload.', 'Information', 2000);
      }
    } else if (selectedActionType === 'log') {
      action.payload = {};
      if (!action.message) {
        action.message = '';
      }
      this.toast.info('Action de log sélectionnée: Saisissez le message à enregistrer.', 'Information', 2000);
    }
    
    this.updateTopicPattern();
  }

  onActionPayloadChange(action: PublishAction): void {
    this.updateTopicPattern();
  }

  onActionMessageChange(action: PublishAction): void {
    this.updateTopicPattern();
  }

  // IMPROVED: No dialog for action removal - better UX
  removeAction(index: number): void {
    const action = this.rule.actions[index];
    const wasComplete = this.isActionComplete(action);
    
    this.rule.actions.splice(index, 1);
    this.updateTopicPattern();
    
    if (wasComplete) {
      this.toast.info('Action complète supprimée.', 'Information', 2000);
    }
    
    // Warn if no actions remain
    if (this.rule.actions.length === 0) {
      this.toast.warning('Aucune action définie. La règle doit contenir au moins une action.', 'Attention', 3000);
    }
  }
  // === DIALOG MANAGEMENT (IMPROVED UX) ===
  onCancel(): void {
    // Always show toast for forced cancel during saving (data protection)
    if (this.isSaving) {
      this.toast.warning('Une sauvegarde est en cours. Annulation non recommandée.', 'Attention', 3000, false);
      return;
    }

    // No dialog for AI generation cancellation - just cancel it
    if (this.isGeneratingSummary) {
      this.dialogRef.close();
      return;
    }

    // Only show confirmation for truly significant changes
    const hasSignificantChanges = this.hasSignificantUnsavedChanges();
    if (hasSignificantChanges) {
      const dialogConfig = this.getCancelConfirmationDialog();
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, dialogConfig);
      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.dialogRef.close();
        }
      });
    } else {
      // Close directly for minor or no changes
      this.dialogRef.close();
    }
  }

  /**
   * Enhanced logic to determine when to show cancel confirmation
   */
  private shouldShowCancelConfirmation(): boolean {
    // For new rules, only show confirmation if there's meaningful content
    if (!this.data.rule) {
      return this.hasMeaningfulNewRuleContent();
    }
    
    // For existing rules, only show confirmation for significant changes
    return this.hasSignificantUnsavedChanges();
  }

  /**
   * Check if a new rule has meaningful content worth confirming
   */
  private hasMeaningfulNewRuleContent(): boolean {
    // Has a rule name
    const hasName = this.rule.rule_name.trim() !== '';
    
    // Has at least one meaningful condition
    const hasConditions = this.rule.conditions.groups.some(group => 
      group.conditions.some(condition => {
        if (condition.type === 'payload') {
          const sc = condition as SensorDataCondition;
          return sc.device && sc.key && sc.value;
        } else if (condition.type === 'time') {
          const tc = condition as TimeCondition;
          return tc.start_time && tc.end_time;
        }
        return false;
      })
    );
    
    // Has at least one meaningful action
    const hasActions = this.rule.actions.some(action => 
      (action.topic && action.type) || 
      (action.type === 'log' && action.message && action.message.trim() !== '')
    );
    
    // Only confirm if there's meaningful content (name + at least one condition or action)
    return hasName && (hasConditions || hasActions);
  }

  /**
   * Get appropriate cancel confirmation dialog configuration
   */
  private getCancelConfirmationDialog(): any {
    if (!this.data.rule) {
      return {
        data: {
          title: 'Annuler la création',
          message: 'Vous avez commencé à créer une règle. Êtes-vous sûr de vouloir annuler ?',
          icon: 'warning'
        },
        disableClose: true,
        panelClass: 'confirmation-dialog-panel'
      };
    }
    
    return {
      data: {
        title: 'Annuler les modifications',
        message: 'Vous avez des modifications importantes non sauvegardées. Êtes-vous sûr de vouloir fermer ?',
        icon: 'warning'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    };
  }

  private forceCancel(): void {
    this.isSaving = false;
    this.isGeneratingSummary = false;
    this.dialogRef.close({ action: 'cancelled', forced: true });
  }

  // === SAVE AND SUMMARY GENERATION (ENHANCED UX) ===

  onSave(): void {
    // Perform comprehensive validation
    const validation = this.validateRuleLogic();
  
    // Show errors if any
    if (!validation.isValid) {
      const errorMessage = validation.errors.join('\n• ');
      this.toast.warning(`Erreurs de validation:\n• ${errorMessage}`, 'Validation échouée', 6000);
      return;
    }
  
    // Show warnings but allow save
    if (validation.warnings.length > 0) {
      const warningMessage = validation.warnings.join('\n• ');
      this.toast.warning(`Avertissements:\n• ${warningMessage}`, 'Attention', 4000);
    }
  
    // For modification: if nothing changed, close directly without confirmation
    const isEdit = this.data.rule && this.data.rule.id != null;
    if (isEdit && !this.hasSignificantUnsavedChanges()) {
      this.dialogRef.close();
      return;
    }
  
    // Proceed with save
    this.performSaveWithSummary();
  }
  


  

  private performSaveWithSummary(): void {
    this.isSaving = true;
    this.isGeneratingSummary = false;

    const cleanRule = this.generateCleanRule();
    
    this.isGeneratingSummary = true;
    this.rulesService.summarizeRule(cleanRule).subscribe({
      next: (summaryResponse: any) => {
        this.isGeneratingSummary = false;
        
        let summaryText: string;
        if (summaryResponse && typeof summaryResponse === 'object' && summaryResponse.summary) {
          summaryText = summaryResponse.summary;
        } else {
          summaryText = String(summaryResponse);
        }
        
        this.saveRuleWithSummary(cleanRule, summaryText);
      },
      error: (error) => {
        this.isGeneratingSummary = false;
        console.error('Error generating summary:', error);
        this.saveRuleWithSummary(cleanRule, null);
      }
    });
  }

  private saveRuleWithSummary(cleanRule: RuleDto, summary: string | null): void {
    if (!this.isSaving) {
      console.warn('Save operation was cancelled or interrupted');
      return;
    }

    const ruleToSave: { RawData: string; summary?: string; id?: string; Enabled: boolean; Priority: number } = {
      RawData: JSON.stringify(cleanRule), 
      Enabled: cleanRule.enabled,
      Priority: cleanRule.priority,
    };

    if (summary) {
      ruleToSave.summary = summary;
    }

    // Check if this is an edit operation by looking for the rule ID
    const isEdit = this.data.rule && this.data.rule.id != null;
    
    if (isEdit && this.data.rule) {
      ruleToSave.id = this.data.rule.id;
      console.log('RawData to update:', ruleToSave.RawData);
      console.log('Rule ID:', ruleToSave.id);
      this.rulesService.update(ruleToSave).subscribe({
        next: (savedRule) => {
          this.handleSaveSuccess(savedRule, cleanRule, summary, 'updated');
        },
        error: (error) => {
          this.handleSaveError(error, 'Erreur lors de la mise à jour de la règle. Veuillez réessayer.');
        }
      });
    } else {
      this.rulesService.create(ruleToSave).subscribe({
        next: (savedRule) => {
          this.handleSaveSuccess(savedRule, cleanRule, summary, 'created');
        },
        error: (error) => {
          this.handleSaveError(error, 'Erreur lors de la création de la règle. Veuillez réessayer.');
        }
      });
    }
  }

  private handleSaveSuccess(savedRule: any, cleanRule: RuleDto, summary: string | null, action: 'created' | 'updated'): void {
    this.isSaving = false;
    
    const actionText = action === 'created' ? 'créée' : 'mise à jour';
    console.log(`Rule ${actionText} successfully:`, savedRule);
    
    this.dialogRef.close({ 
      action: 'saved', 
      rule: {
        ...savedRule,
        ...cleanRule,
        summary: summary
      },
      operation: action
    });
  }

  private handleSaveError(error: any, message: string): void {
    this.isSaving = false;
    this.isGeneratingSummary = false;
    console.error('Save error:', error);
    let errorMessage = message;
    if (error?.error?.message) {
      errorMessage += `\n\nDétails: ${error.error.message}`;
    } else if (error?.message) {
      errorMessage += `\n\nDétails: ${error.message}`;
    }
    this.toast.warning(errorMessage, 'Erreur', 4000, false);
  }

  // IMPROVED: More intelligent detection of significant changes
  private hasSignificantUnsavedChanges(): boolean {
    if (!this.data.rule) {
      // For new rules, consider significant if there's a name AND at least one condition or action
      return this.rule.rule_name.trim() !== '' && (
        this.rule.conditions.groups.some(group => 
          group.conditions.some(condition => {
            if (condition.type === 'payload') {
              const sc = condition as SensorDataCondition;
              return sc.device && sc.key && sc.value;
            } else if (condition.type === 'time') {
              const tc = condition as TimeCondition;
              return tc.start_time && tc.end_time;
            }
            return false;
          })
        ) ||
        this.rule.actions.some(action => 
          (action.topic && action.type) || 
          (action.type === 'log' && action.message && action.message.trim() !== '')
        )
      );
    }
    
    // For existing rules, compare with original
    const originalRule = this.data.rule;
    return JSON.stringify(this.rule) !== JSON.stringify(originalRule);
  }

  private showErrorDialog(message: string, showRetry: boolean = false): void {
    // Use toast instead of dialog for errors
    this.toast.warning(message, 'Erreur', 4000, false);
    if (showRetry && this.loadingError) {
      this.toast.info('Cliquez pour réessayer.', 'Info', 3000, false);
      // Optionally, you could add a retry button somewhere in the UI
    }
  }

  // === UTILITY METHODS ===

  getSensorDeviceNames(): string[] {
    return Object.keys(this.deviceTypes.sensors);
  }

  getActuatorDeviceNames(): string[] {
    return Object.keys(this.deviceTypes.actuators);
  }

  getPropertiesForDevice(deviceType: 'sensors' | 'actuators', deviceName: string): DeviceProperty[] {
    if (!deviceName) return [];
    const device = this.deviceTypes[deviceType][deviceName];
    return device ? device.properties || [] : [];
  }

  getActionsForDevice(deviceType: 'sensors' | 'actuators', deviceName: string): DeviceAction[] {
    if (!deviceName) return [];
    const device = this.deviceTypes[deviceType][deviceName];
    return device ? device.actions || [] : [];
  }

  getDeviceNameKeyByTopic(topic: string, deviceType: 'sensors' | 'actuators'): string | undefined {
    if (!topic) return undefined;
    for (const key in this.deviceTypes[deviceType]) {
      if (this.deviceTypes[deviceType][key].topic === topic) {
        return key;
      }
    }
    return undefined;
  }

  getSelectedProperty(condition: SensorDataCondition): DeviceProperty | undefined {
    if (!condition.device || !condition.key) {
      return undefined;
    }
    const device = this.deviceTypes.sensors[condition.device];
    if (device && device.properties) {
      return device.properties.find(p => p.key === condition.key);
    }
    return undefined;
  }

  getSelectedActionType(action: PublishAction): DeviceAction | undefined {
    if (!action.topic || !action.type) {
      return undefined;
    }
    const deviceNameKey = this.getDeviceNameKeyByTopic(action.topic, 'actuators');
    if (deviceNameKey) {
      const selectedActuator = this.deviceTypes.actuators[deviceNameKey];
      if (selectedActuator && selectedActuator.actions) {
        return selectedActuator.actions.find(a => a.type === action.type);
      }
    }
    return undefined;
  }

  capitalizeFirst(str: string): string {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  retryLoadData(): void {
    if (this.isBusy) {
      console.warn('Cannot retry while another operation is in progress');
      return;
    }
    
    this.loadingError = null;
    this.loadBackendData();
  }

  getDataListId(groupIndex: number, conditionIndex: number): string {
    return `suggestions-${groupIndex}-${conditionIndex}`;
  }

  // === JSON GENERATION AND DOWNLOAD ===

  generateJSON(): string {
    try {
      const ruleToExport = this.generateCleanRule(); 
      return JSON.stringify(ruleToExport, null, 2);
    } catch (error) {
      console.error('Error generating JSON:', error);
      return '{\n  "error": "Unable to generate JSON preview"\n}';
    }
  }

  downloadJSON(): void {
    if (this.isBusy) {
      this.toast.warning('Impossible de télécharger pendant une opération de sauvegarde. Veuillez patienter.', 'Erreur', 3000, false);
      return;
    }
    
    try {
      const filename = `${this.rule.rule_name || 'rule'}.json`;
      const json = this.generateJSON();
      const blob = new Blob([json], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading JSON:', error);
      this.toast.warning('Erreur lors du téléchargement du fichier JSON.', 'Erreur', 3000, false);
    }
  }

  private generateCleanRule(): RuleDto {
    const cleanRule = JSON.parse(JSON.stringify(this.rule));

    // Remove empty payload properties from actions
    cleanRule.actions.forEach((action: PublishAction) => {
      if (action.payload && typeof action.payload === 'object') {
        for (const key in action.payload) {
          if (action.payload.hasOwnProperty(key) && 
              (action.payload[key] === null || action.payload[key] === undefined || action.payload[key] === '')) {
            delete action.payload[key];
          }
        }
      }
    });

    // Remove actions that are completely empty
    cleanRule.actions = cleanRule.actions.filter((action: PublishAction) => {
      const hasMessage = action.type === 'log' && action.message && action.message.trim() !== '';
      const hasTopicAndType = action.type !== 'log' && action.topic && action.type;
      const hasPayloadData = action.payload && Object.keys(action.payload).length > 0;
      
      return hasMessage || (hasTopicAndType && hasPayloadData);
    });

    return cleanRule;
  }

  updateTopicPattern(): void {
    const topics: string[] = [];

    this.rule.conditions.groups.forEach(group => {
      group.conditions.forEach(condition => {
        if (condition.type === 'payload') {
          const sensorCondition = condition as SensorDataCondition;
          if (sensorCondition.device) {
            const device = this.deviceTypes.sensors[sensorCondition.device];
            if (device && device.topic && !topics.includes(device.topic)) {
              topics.push(device.topic);
            }
          }
        }
      });
    });

    this.rule.topic_pattern = topics;
    this.updateJsonPreview();
  }

  public updateJsonPreview(): void {
    try {
      this.jsonPreview = JSON.stringify(this.generateCleanRule(), null, 2);
    } catch (error) {
      this.jsonPreview = '{\n  "error": "Unable to generate JSON preview"\n}';
    }
  }

  // === GETTER METHODS FOR UI STATE ===

  get isBusy(): boolean {
    return this.isSaving || this.isGeneratingSummary;
  }

  get statusMessage(): string {
    if (this.isGeneratingSummary && this.isSaving) {
      return 'Sauvegarde terminée, génération du résumé IA...';
    }
    if (this.isGeneratingSummary) {
      return 'Génération du résumé IA en cours...';
    }
    if (this.isSaving) {
      return 'Sauvegarde de la règle en cours...';
    }
    return '';
  }

  get operationProgress(): number {
    if (this.isSaving && !this.isGeneratingSummary) {
      return 50;
    }
    if (this.isGeneratingSummary) {
      return 80;
    }
    if (!this.isSaving && !this.isGeneratingSummary) {
      return 100;
    }
    return 0;
  }

  // === SMART VALIDATION ===
  private hasConflictsOrDuplicates(): string | null {
    // Check for repeated conditions (same device, key, operator, value)
    const seenConditions = new Set<string>();
    for (const group of this.rule.conditions.groups) {
      for (const condition of group.conditions) {
        if (condition.type === 'payload') {
          const sc = condition as any;
          const key = `${sc.device}|${sc.key}|${sc.operator}|${sc.value}`;
          if (seenConditions.has(key)) {
            return 'Condition répétée détectée (même capteur, propriété, opérateur et valeur).';
          }
          seenConditions.add(key);
        }
      }
    }
    // Check for repeated actions (same topic, type, payload)
    const seenActions = new Set<string>();
    for (const action of this.rule.actions) {
      const key = `${action.topic}|${action.type}|${JSON.stringify(action.payload)}`;
      if (seenActions.has(key)) {
        return 'Action répétée détectée (même actionneur, type et payload).';
      }
      seenActions.add(key);
    }
    // Check for structural conflicts (e.g., same device/key with conflicting operators/values)
    // Example: two conditions on the same device/key with conflicting values
    const deviceKeyMap = new Map<string, Set<string>>();
    for (const group of this.rule.conditions.groups) {
      for (const condition of group.conditions) {
        if (condition.type === 'payload') {
          const sc = condition as any;
          const dk = `${sc.device}|${sc.key}`;
          if (!deviceKeyMap.has(dk)) deviceKeyMap.set(dk, new Set());
          deviceKeyMap.get(dk)!.add(`${sc.operator}|${sc.value}`);
        }
      }
    }
    for (const [dk, opvals] of deviceKeyMap.entries()) {
      if (opvals.size > 1) {
        // Could be a conflict if same device/key has multiple different operator/value pairs
        // (This is a simple heuristic; you can make it stricter if needed)
        return 'Conflit détecté : plusieurs conditions différentes sur le même capteur et propriété.';
      }
    }
    return null;
  }
}

export type Condition = SensorDataCondition | TimeCondition;