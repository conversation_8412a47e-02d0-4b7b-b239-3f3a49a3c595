import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RuleContent } from '@app/shared/models/rule/RuleContent';
import { RuleExecutionChartData, RuleExecutionSimple } from '@app/core/services/administrative/rules.service';
import { NgChartsModule } from 'ng2-charts';
import { ChartConfiguration, ChartType } from 'chart.js';


@Component({
  selector: 'app-rule-list',
  templateUrl: './rule-list.component.html',
  styleUrls: ['./rule-list.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    NgChartsModule // <-- Add this
  ]
})
export class RuleListComponent {
  @Input() rules: RuleContent[] = [];
  @Input() expandedRuleId: string | null = null;
  @Input() selectedControllerId: string | null = null;
  @Input() isLoading: boolean | null = false;
  @Input() isDeleting = new Set<string>();
  @Input() activeTab: 'hierarchy' | 'rawdata' | 'summary' = 'summary';
  @Input() pagedExecutions: { [ruleId: string]: import('@app/shared/models/rule/PagedResponse').PagedResponse<RuleExecutionSimple[]> } = {};
  @Input() pagedExecutionsPageSize: number = 10;
  @Output() executionsPageChange = new EventEmitter<{ ruleId: string, page: number }>();
  @Output() controllerExecutionsPageChange = new EventEmitter<{ ruleId: string, controllerId: string, page: number }>();

  @Output() toggleExpandRule = new EventEmitter<string>();
  @Output() viewControllerDetails = new EventEmitter<string>();
  @Output() editRule = new EventEmitter<string>();
  @Output() duplicateRule = new EventEmitter<string>();
  @Output() deleteRule = new EventEmitter<string>();
  @Output() toggleRuleStatus = new EventEmitter<RuleContent>();
  @Output() openTransactionHistory = new EventEmitter<string>();
  @Output() setActiveTab = new EventEmitter<'hierarchy' | 'rawdata' | 'summary'>();
  @Output() selectController = new EventEmitter<string>();
  @Output() copyRawData = new EventEmitter<string>();
  @Output() downloadRawData = new EventEmitter<RuleContent>();

  // Helper functions that need to be passed as inputs or implemented here
  @Input() getRuleSummary!: (ruleId: string) => string;
  @Input() hasRuleSummary!: (ruleId: string) => boolean;
  @Input() formatRawData!: (ruleId: string) => string;
  @Input() formatTimestamp!: (timestamp: string) => string;
  @Input() getControllerStatusIcon!: (controller: any) => string;
  @Input() getControllerStatusText!: (controller: any) => string;
  @Input() getControllerStatusClass!: (controller: any) => string;

  isRuleActionPending(ruleId: string): boolean {
    return this.isDeleting.has(ruleId);
  }

  isSimpleRule(rule: RuleContent): boolean {
    const hasMinimalData = (
      rule.clients.length === 0 &&
      rule.tags.length === 0 &&
      rule.totalSites === 0 &&
      (!rule.conditions || rule.conditions.length <= 1) &&
      (!rule.actions || rule.actions.length <= 1)
    );

    const isNewRule = (
      !rule.lastTriggered || rule.lastTriggered === '' || rule.lastTriggered === 'Jamais'
    );

    return hasMinimalData && isNewRule;
  }

  getTotalClientsForRule(rule: RuleContent): number {
    return rule.clients.length;
  }

  getTotalControllersForRule(rule: RuleContent): number {
    return rule.clients.reduce((total, client) => {
      return total + client.sites.reduce((siteAcc, site) => {
        return siteAcc + site.locations.reduce((locAcc, location) => {
          return locAcc + location.controllers.length;
        }, 0);
      }, 0);
    }, 0);
  }

  // NEW: Helper methods for execution data display
  formatDateForChart(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', { 
      day: '2-digit', 
      month: '2-digit' 
    });
  }

  getChartTooltip(chartData: RuleExecutionChartData): string {
    const date = new Date(chartData.ExecutionDate).toLocaleDateString('fr-FR');
    return `${date}: ${chartData.SuccessfulExecutions} succès, ${chartData.FailedExecutions} échecs sur ${chartData.TotalExecutions} total`;
  }

  getSuccessPercentage(chartData: RuleExecutionChartData): number {
    if (chartData.TotalExecutions === 0) return 0;
    return (chartData.SuccessfulExecutions / chartData.TotalExecutions) * 100;
  }

  getFailurePercentage(chartData: RuleExecutionChartData): number {
    if (chartData.TotalExecutions === 0) return 0;
    return (chartData.FailedExecutions / chartData.TotalExecutions) * 100;
  }

  getTotalExecutions(chartData: RuleExecutionChartData[]): number {
    return chartData.reduce((total, day) => total + day.TotalExecutions, 0);
  }

  getSuccessRate(chartData: RuleExecutionChartData[]): number {
    const totalExecutions = this.getTotalExecutions(chartData);
    if (totalExecutions === 0) return 0;
    
    const totalSuccessful = chartData.reduce((total, day) => total + day.SuccessfulExecutions, 0);
    return Math.round((totalSuccessful / totalExecutions) * 100);
  }

  getExecutionsForRule(ruleId: string): RuleExecutionSimple[] {
    const paged = this.pagedExecutions[ruleId];
    return paged ? paged.Content || [] : [];
  }

  getExecutionsPageCount(ruleId: string): number {
    const paged = this.pagedExecutions[ruleId];
    if (!paged) return 1;
    return paged.Lister?.Pagination?.PageCount || 1;
  }

  getExecutionsCurrentPage(ruleId: string): number {
    const paged = this.pagedExecutions[ruleId];
    if (!paged) return 1;
    return paged.Lister?.Pagination?.CurrentPage || 1;
  }

  onExecutionsPageChange(ruleId: string, page: number): void {
    this.executionsPageChange.emit({ ruleId, page });
  }

  getLastExecutionDate(executions: RuleExecutionSimple[]): string {
    if (!executions || executions.length === 0) {
      return 'Aucune';
    }
    
    // Find the most recent execution
    const sortedExecutions = [...executions].sort((a, b) => 
      new Date(b.ExecutionTimestamp).getTime() - new Date(a.ExecutionTimestamp).getTime()
    );
    
    return this.formatTimestamp(sortedExecutions[0].ExecutionTimestamp);
  }

  getControllerExecutionsPageCount(ruleId: string, controllerId: string): number {
    const paged = this.pagedExecutions[`${ruleId}_${controllerId}`];
    if (!paged) return 1;
    return paged.Lister?.Pagination?.PageCount || 1;
  }

  getControllerExecutionsCurrentPage(ruleId: string, controllerId: string): number {
    const paged = this.pagedExecutions[`${ruleId}_${controllerId}`];
    if (!paged) return 1;
    return paged.Lister?.Pagination?.CurrentPage || 1;
  }

  onControllerExecutionsPageChange(ruleId: string, controllerId: string, page: number): void {
    this.controllerExecutionsPageChange.emit({ ruleId, controllerId, page });
  }

  // Event handlers
  handleToggleExpandRule(ruleId: string): void {
    this.toggleExpandRule.emit(ruleId);
  }

  handleEditRule(ruleId: string): void {
    this.editRule.emit(ruleId);
  }

  handleDuplicateRule(ruleId: string): void {
    this.duplicateRule.emit(ruleId);
  }

  handleDeleteRule(ruleId: string): void {
    this.deleteRule.emit(ruleId);
  }

  handleToggleRuleStatus(rule: RuleContent): void {
    this.toggleRuleStatus.emit(rule);
  }

  handleOpenTransactionHistory(ruleId: string): void {
    this.openTransactionHistory.emit(ruleId);
  }

  handleSetActiveTab(tab: 'hierarchy' | 'rawdata' | 'summary'): void {
    this.setActiveTab.emit(tab);
  }

  handleSelectController(controllerId: string): void {
    this.selectController.emit(controllerId);
  }

  handleCopyRawData(ruleId: string): void {
    this.copyRawData.emit(ruleId);
  }

  handleDownloadRawData(rule: RuleContent): void {
    this.downloadRawData.emit(rule);
  }

  handleViewControllerDetails(controllerId: string): void {
    this.viewControllerDetails.emit(controllerId);
  }

  // Returns the max total executions in the data (for scaling)
  getMaxExecutions(chartData: RuleExecutionChartData[]): number {
    if (!chartData || chartData.length === 0) return 100;
    return Math.max(...chartData.map(day => day.TotalExecutions), 100);
  }

  // Returns Y-axis ticks (e.g., [100, 80, 60, 40, 20, 0])
  getYAxisTicks(chartData: RuleExecutionChartData[], steps: number = 5): number[] {
    const max = this.getMaxExecutions(chartData);
    const step = Math.ceil(max / steps);
    const ticks = [];
    for (let i = max; i >= 0; i -= step) {
      ticks.push(i);
    }
    if (ticks[ticks.length - 1] !== 0) ticks.push(0);
    return ticks;
  }

  // Returns the percentage height for a bar, relative to the max
  getBarHeight(value: number, chartData: RuleExecutionChartData[]): number {
    const max = this.getMaxExecutions(chartData);
    return max === 0 ? 0 : (value / max) * 100;
  }

  // Chart.js integration
// Updated Chart.js 4.x configuration with correct properties
public barChartOptions: ChartConfiguration['options'] = {
  responsive: true,
  maintainAspectRatio: false,
  layout: {
    padding: {
      top: 20,
      bottom: 10,
      left: 10,
      right: 10
    }
  },
  plugins: {
    legend: {
      display: true,
      position: 'top',
      align: 'end',
      labels: {
        color: '#475569',
        font: { 
          size: 12, 
        },
        padding: 20,
        usePointStyle: true,
        pointStyle: 'rect',
        boxWidth: 12,
        boxHeight: 12
      }
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.98)',
      titleColor: '#1e293b',
      bodyColor: '#475569',
      borderColor: '#e2e8f0',
      borderWidth: 1,
      titleFont: { 
        size: 13 
      },
      bodyFont: { 
        size: 12 
      },
      cornerRadius: 8,
      padding: 12,
      displayColors: true,
      callbacks: {
        title: function(context: any) {
          return `${context[0].label}`;
        },
        label: function(context: any) {
          const label = context.dataset.label || '';
          return `${label}: ${context.parsed.y} exécution${context.parsed.y !== 1 ? 's' : ''}`;
        }
      }
    }
  },
  scales: {
    x: {
      type: 'category',
      grid: { 
        display: false
      },
      ticks: { 
        color: '#64748b', 
        font: { 
          size: 12, 
        },
        padding: 8,
        maxRotation: 0,
        minRotation: 0
      },
      border: {
        color: '#e2e8f0',
        width: 1
      },
      offset: false
    },
    y: {
      type: 'linear',
      title: { 
        display: true, 
        text: 'Exécutions', 
        color: '#64748b', 
        font: { 
          size: 12, 
        }
      },
      grid: { 
        color: 'rgba(226, 232, 240, 0.4)',
      },
      ticks: { 
        color: '#64748b', 
        font: { 
          size: 11, 
        },
        padding: 8,
        precision: 0
      },
      beginAtZero: true,
      border: {
        color: '#e2e8f0',
        width: 1
      }
    }
  },
  interaction: {
    intersect: false,
    mode: 'index'
  }
};

public barChartType: ChartType = 'bar';
public controllerCharts: { [controllerId: string]: ChartConfiguration['data'] } = {};

// Helper method to generate last 7 days in French (chronological order)
private getLastSevenDays(): { label: string, date: string }[] {
  const days = [];
  const today = new Date();
  
  // Generate last 7 days in chronological order (oldest to newest)
  for (let i = 6; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(today.getDate() - i);
    
    // JavaScript getDay(): 0=dimanche, 1=lundi, 2=mardi, 3=mercredi, 4=jeudi, 5=vendredi, 6=samedi
    const dayNames = ['dim', 'lun', 'mar', 'mer', 'jeu', 'ven', 'sam'];
    const dayIndex = date.getDay(); 
    const label = dayNames[dayIndex];
    const dateString = date.toISOString().split('T')[0];
    
    days.push({ label, date: dateString });
  }
  
  return days;
}

// Call this when you have new data for a controller
updateControllerChartData(controllerId: string, chartData: RuleExecutionChartData[]) {
  const lastSevenDays = this.getLastSevenDays();
  
  // Create a map of execution data by date
  const dataMap = new Map<string, RuleExecutionChartData>();
  chartData.forEach(data => {
    // Extract date from "2025-07-14T00:00:00" format to "2025-07-14"
    const dateKey = data.ExecutionDate.split('T')[0];
    dataMap.set(dateKey, data);
  });
  
  // Build chart data ensuring all 7 days are shown
  const labels = lastSevenDays.map(day => day.label);
  const successData = lastSevenDays.map(day => {
    const data = dataMap.get(day.date);
    return data ? data.SuccessfulExecutions : 0;
  });
  const failureData = lastSevenDays.map(day => {
    const data = dataMap.get(day.date);
    return data ? data.FailedExecutions : 0;
  });

  // Debug logs to verify the mapping
  console.log('Last seven days:', lastSevenDays);
  console.log('Data map:', Array.from(dataMap.entries()));
  console.log('Success data:', successData);
  console.log('Labels:', labels);

  this.controllerCharts[controllerId] = {
    labels: labels,
    datasets: [
      {
        data: successData,
        label: 'Succès',
        backgroundColor: 'rgba(16, 185, 129, 0.75)',
        borderColor: '#10b981',
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false,
        barPercentage: 0.8,
        categoryPercentage: 0.9,
        hoverBackgroundColor: 'rgba(16, 185, 129, 0.9)',
        hoverBorderColor: '#059669'
      },
      {
        data: failureData,
        label: 'Échecs',
        backgroundColor: 'rgba(239, 68, 68, 0.75)',
        borderColor: '#ef4444',
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false,
        barPercentage: 0.8,
        categoryPercentage: 0.9,
        hoverBackgroundColor: 'rgba(239, 68, 68, 0.9)',
        hoverBorderColor: '#dc2626'
      }
    ]
  };
}

getControllerName(rule: RuleContent, controllerId: string): string {
  for (const client of rule.clients) {
    for (const site of client.sites) {
      for (const location of site.locations) {
        const controller = location.controllers.find(c => c.id === controllerId);
        if (controller) {
          return controller.name || (controller.id?.slice(0, 8) + '...');
        }
      }
    }
  }
  // Fallback if not found
  return controllerId?.slice(0, 8) + '...';
}
}