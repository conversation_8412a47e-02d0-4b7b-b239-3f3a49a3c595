import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Local } from '../models/local';

@Injectable({
  providedIn: 'root'
})
export class LocalService {
  readonly apiUrl = `http://146.59.198.243:84/api/locals`;

  constructor(readonly http: HttpClient) {}

  getLocals(): Observable<Local[]> {
    return this.http.get<Local[]>(this.apiUrl);
  }

  getLocalById(id: number): Observable<Local> {
    return this.http.get<Local>(`${this.apiUrl}/${id}`);
  }

  getLocalsBySite(siteId: number): Observable<Local[]> {
    return this.http.get<Local[]>(`${this.apiUrl}/site/${siteId}`);
  }

  createLocal(local: FormData): Observable<Local> {
    return this.http.post<Local>(this.apiUrl, local);
  }

  updateLocal(id: number, local: FormData): Observable<Local> {
    return this.http.put<Local>(`${this.apiUrl}/${id}`, local);
  }

  deleteLocal(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
  getLocalImage(id: number): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/image/${id}`, { responseType: 'blob' });
  }
      getLocalImageArchitecture(id: number): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/${id}/image-architecture`, {
      responseType: 'blob'
    });
  }

  getLocalImageLocale(id: number): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/${id}/image-locale`, {
      responseType: 'blob'
    });
  }

  // Add this new method (singular)
  getLocal(id: number): Observable<Local> {
    return this.http.get<Local>(`${this.apiUrl}/${id}`);
  }
}
