/* Enhanced Color Variables - Force Application */


/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #2c3e50;
  background-color: #fafafa;
}

/* Container */
.logs-container {
  /* max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #fafafa;
  min-height: 100vh; */
}

/* Page Header */
.page-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  background-color: #ffffff;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, var(--primary), #2ecc71);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.header-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
}

.header-icon mat-icon {
  font-size: 2rem;
  color: #ffffff;
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, var(--primary), #2ecc71);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: var(--primary); /* Fallback for browsers that don't support background-clip */
}

.page-subtitle {
  color: #555555;
  font-size: 1.1rem;
  margin: 0;
  font-weight: 500;
}

/* Filter Card */
.filter-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.filter-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.filter-header mat-icon {
  color: var(--primary);
  font-size: 1.5rem;
}

.filter-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
}

/* Filter Grid */
.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Form Group */
.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.75rem;
}

.form-label mat-icon {
  font-size: 1rem;
  color: var(--primary);
}

/* Form Controls */
.form-control {
  padding: 0.875rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.95rem;
  background-color: #ffffff;
  color: #111827;
  transition: all 0.2s ease;
  min-height: 48px;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15);
}

.form-control:hover {
  border-color: #d1d5db;
}

/* Select Wrapper */
.select-wrapper {
  position: relative;
}

.select-wrapper select {
  appearance: none;
  padding-right: 3rem;
  background-color: #ffffff;
  color: #111827;
  width: 100%;
}

.select-arrow {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9e9e9e;
  pointer-events: none;
}

/* Search Wrapper */
.search-wrapper {
  position: relative;
}

.search-wrapper input {
  padding-right: 3rem;
  width: 100%;
}

.search-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9e9e9e;
  pointer-events: none;
}

/* Filter Actions */
.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  min-height: 48px;
  text-decoration: none;
}

.btn mat-icon {
  font-size: 1rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(45deg,#81c784, var(--primary)) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #2d3748;
  border: 1px solid #e5e7eb;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #e5e7eb;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-accent {
  background-color: #2196f3;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.btn-accent:hover:not(:disabled) {
  background-color: #1976d2;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
}

.btn-danger {
  background-color: #e74c3c;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.btn-danger:hover:not(:disabled) {
  background-color: #e53935;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
}

/* Button Spinner */
.button-spinner {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.button-spinner:hover{
  background: linear-gradient(45deg,#81c784, var(--primary)) !important;
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  background-color: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.loading-content {
  text-align: center;
}

.main-spinner {
  width: 3rem;
  height: 3rem;
  border: 4px solid #e5e7eb;
  border-top-color: var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.loading-content h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.25rem;
  font-weight: 600;
}

.loading-content p {
  margin: 0;
  color: #555555;
}

/* Logs Section */
.logs-section {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.section-header mat-icon {
  color: var(--primary);
  font-size: 1.5rem;
}

.section-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
}

/* Log Grid */
.log-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

/* Log Card */
.log-card {
  background-color: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  height: 100%;
  display: flex;
  flex-direction: column;
  will-change: transform;
  backface-visibility: hidden;
  animation: fadeInUp 0.3s ease-out;
}

.log-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--primary);
}

.log-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 4px;
  background-color: #d1d5db;
  transition: background 0.2s ease;
}

.log-card.info::before { 
  background: linear-gradient(45deg, var(--primary), #2ecc71, var(--primary));
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

.log-card.warning::before { 
  background: linear-gradient(180deg, #ffc107, #ff9800); 
}

.log-card.error::before { 
  background: linear-gradient(180deg, #e74c3c, #e53935); 
}

.log-card.debug::before { 
  background: linear-gradient(180deg, #2ecc71, #2e7d32); 
}

/* Log Header */
.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.25rem 0.75rem 1.5rem;
  gap: 1rem;
}

.log-level-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.level-indicator {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background-color: #d1d5db;
  transition: all 0.2s ease;
}

.level-indicator.info { 
  background-color: var(--primary);
  animation: pulse 2s infinite;
}

.level-indicator.warning { 
  background-color: #ffc107; 
}

.level-indicator.error { 
  background-color: #e74c3c; 
}

.level-indicator.debug { 
  background-color: #2ecc71; 
}

.log-card:hover .level-indicator {
  box-shadow: 0 0 10px currentColor;
}

.log-level {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.log-level.info {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid var(--primary);
}

.log-level.warning {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffc107;
}

.log-level.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #dc3545;
}

.log-level.debug {
  background-color: #e8f5e9;
  color: #155724;
  border: 1px solid #28a745;
}

.log-timestamp {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #757575;
  font-weight: 500;
}

.log-timestamp mat-icon {
  font-size: 0.875rem !important;
  width: 0.875rem;
  height: 0.875rem;
  color: #757575;
}

/* Log Content */
.log-content {
  padding: 0 1.5rem 1rem;
  flex: 1;
}

.log-message {
  font-size: 0.875rem;
  color: #2d3748;
  line-height: 1.5;
  margin-bottom: 1rem;
  min-height: 2.5rem;
  font-weight: 500;
}

.log-message.empty {
  color: #9e9e9e;
  font-style: italic;
}

/* Payload Preview */
.payload-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.meta-chip {
  display: inline-flex;
  align-items: center;
  background-color: #e8f5e9;
  border: 1px solid var(--primary);
  border-radius: 6px;
  padding: 0.25rem 0.5rem;
  font-size: 0.7rem;
  max-width: 150px;
  transition: transform 0.2s ease;
}

.log-card:hover .meta-chip {
  transform: scale(1.05);
}

.meta-chip.battery {
  background-color: #e8f5e9;
  border-color: var(--primary);
}

.meta-chip.temperature {
  background-color: #fff3e0;
  border-color: #ff9800;
}

.meta-chip.motion {
  background-color: #e3f2fd;
  border-color: #2196f3;
}

.chip-key {
  color: #2e7d32;
  font-weight: 600;
  margin-right: 0.25rem;
}

.chip-value {
  color: #155724;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

/* Log Footer */
.log-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background-color: #fafafa;
  border-top: 1px solid #e5e7eb;
  margin-top: auto;
}

.log-topic {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #757575;
  font-weight: 500;
}

.log-topic mat-icon {
  font-size: 0.875rem !important;
  width: 0.875rem;
  height: 0.875rem;
  color: #757575;
}

.log-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.meta-indicator,
.view-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background-color: #e8f5e9;
  color: var(--primary);
  transition: all 0.2s ease;
}

.view-indicator {
  background-color: #f5f5f5;
  color: #757575;
}

.meta-indicator:hover,
.view-indicator:hover {
  transform: scale(1.1);
}

.meta-indicator mat-icon,
.view-indicator mat-icon {
  font-size: 0.875rem !important;
  width: 0.875rem;
  height: 0.875rem;
}

/* Empty State */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  background-color: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.empty-content {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  margin-bottom: 1.5rem;
}

.empty-icon mat-icon {
  font-size: 4rem;
  color: #d1d5db;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.75rem 0;
}

.empty-description {
  color: #555555;
  margin: 0 0 2rem 0;
  line-height: 1.6;
}

/* Modal Styles */
.log-modal-backdrop {
  position: fixed;
  z-index: 1000;
  inset: 0;
  background: rgba(44, 62, 80, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  backdrop-filter: blur(4px);
}

.log-modal {
  background-color: #ffffff;
  border-radius: 12px;
  max-width: 700px;
  width: 100%;
  max-height: 90vh;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: modalSlideIn 0.3s ease-out;
}

.log-modal-header {
  padding: 2rem;
  background: linear-gradient(135deg, var(--primary), #2ecc71);
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.modal-icon {
  font-size: 1.5rem !important;
  color: #ffffff;
}

.log-modal-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  color: #ffffff;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: #ffffff;
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.close-btn mat-icon {
  color: #ffffff;
}

.log-modal-body {
  padding: 2rem;
  overflow-y: auto;
  flex: 1;
  background-color: #ffffff;
}

/* Detail Grid */
.detail-grid {
  display: grid;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.detail-item:hover {
  background-color: #f0f8f0;
  border-color: var(--primary);
}

.detail-icon {
  color: var(--primary);
  font-size: 1.25rem !important;
  margin-top: 0.125rem;
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #555555;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.detail-value {
  font-size: 1rem;
  color: #2c3e50;
  font-weight: 500;
}

.level-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  width: fit-content;
}

.level-badge.info {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid var(--primary);
}

.level-badge.warning {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffc107;
}

.level-badge.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #dc3545;
}

.level-badge.debug {
  background-color: #e8f5e9;
  color: #155724;
  border: 1px solid #28a745;
}

.topic-value {
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  background-color: #e8f5e9;
  padding: 0.5rem;
  border-radius: 6px;
  border: 1px solid var(--primary);
  font-size: 0.875rem;
  word-break: break-all;
  color: #155724;
}

/* Summary Section */
.summary-section {
  margin-bottom: 2rem;
}

.summary-container {
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
  min-height: 120px;
  position: relative;
  overflow: hidden;
}

.summary-loading {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: var(--primary);
  font-weight: 500;
  justify-content: center;
  height: 100px;
  background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.1), transparent);
  animation: shimmer 1.5s infinite;
}

.loading-spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid #e8f5e9;
  border-top-color: var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.summary-content {
  font-size: 1rem;
  line-height: 1.6;
  color: #2d3748;
  white-space: pre-wrap;
}

/* Metadata Section */
.metadata-section {
  margin-bottom: 1rem;
}

.metadata-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.metadata-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 1rem;
  background-color: #e8f5e9;
  border-radius: 8px;
  border: 1px solid var(--primary);
  transition: all 0.2s ease;
}

.metadata-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.2);
}

.metadata-key {
  font-size: 0.75rem;
  font-weight: 600;
  color: #2e7d32;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metadata-value {
  font-size: 0.875rem;
  color: #155724;
  font-weight: 500;
  word-break: break-word;
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
}

/* Status Classes */
.status-actif {
  background-color: #d4edda;
  color: #155724;
}

.status-inactif {
  background-color: #f8d7da;
  color: #721c24;
}

.status-maintenance {
  background-color: #fff3cd;
  color: #856404;
}

/* Animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1); 
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.1); 
  }
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced meta chips with specific colors */
.meta-chip[data-type="battery"] {
  background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
  border-color: var(--primary);
}

.meta-chip[data-type="temperature"] {
  background: linear-gradient(135deg, #fff3e0, #ffe0b2);
  border-color: #ff9800;
}

.meta-chip[data-type="motion"] {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border-color: #2196f3;
}

.meta-chip[data-type="voltage"] {
  background: linear-gradient(135deg, #f3e5f5, #e1bee7);
  border-color: #9c27b0;
}

/* Enhanced button states */
.btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn-primary:active {
  background-color: #2e7d32;
}

.btn-secondary:active {
  background-color: #d1d5db;
}

.btn-accent:active {
  background-color: #1565c0;
}

.btn-danger:active {
  background-color: #c62828;
}

/* Responsive Design */
@media (max-width: 768px) {
  .logs-container {
    padding: 1rem;
  }

  .page-title {
    font-size: 2rem;
    color: var(--primary);
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .filter-card,
  .logs-section {
    padding: 1.5rem;
  }

  .filter-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .filter-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .filter-actions .btn {
    width: 100%;
    justify-content: center;
  }

  .log-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .log-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem 1rem 0.5rem 1.25rem;
  }

  .log-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem 1.25rem;
  }

  .log-modal {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
    border-radius: 12px;
  }

  .log-modal-header {
    padding: 1.5rem;
  }

  .log-modal-body {
    padding: 1.5rem;
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }

  .metadata-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 600px) {
  .log-grid {
    grid-template-columns: 1fr;
  }
  
  .log-footer {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }

  .payload-preview {
    flex-direction: column;
  }

  .meta-chip {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .logs-container {
    padding: 0.5rem;
  }

  .header-content,
  .filter-card,
  .logs-section {
    padding: 1rem;
    border-radius: 8px;
  }

  .page-title {
    font-size: 1.75rem;
    color: var(--primary);
  }

  .header-icon {
    width: 3rem;
    height: 3rem;
  }

  .header-icon mat-icon {
    font-size: 1.5rem;
  }

  .log-modal {
    margin: 0.5rem;
    max-width: calc(100vw - 1rem);
    max-height: calc(100vh - 1rem);
    border-radius: 8px;
  }

  .log-modal-header,
  .log-modal-body {
    padding: 1rem;
  }

  .log-card {
    border-radius: 8px;
  }

  .log-content {
    padding: 0 1rem 1rem;
  }

  .log-header {
    padding: 1rem 1rem 0.5rem;
  }

  .log-footer {
    padding: 0.75rem 1rem;
  }

  .filter-grid {
    gap: 0.75rem;
  }

  .form-control {
    min-height: 44px;
    font-size: 16px;
  }

  .btn {
    min-height: 44px;
    padding: 0.75rem 1rem;
  }
}

/* Focus and Accessibility */
.log-card:focus {
  outline: 3px solid var(--primary);
  outline-offset: 2px;
}

.btn:focus {
  outline: 3px solid var(--primary);
  outline-offset: 2px;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15);
}

.close-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-green: #0f5132 !important;
    --primary-green-light: #146c43 !important;
    --gray-200: #dee2e6 !important;
    --gray-300: #ced4da !important;
  }

  .log-card {
    border-width: 2px;
  }

  .log-level {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .log-card:hover {
    transform: none;
  }

  .btn:hover:not(:disabled) {
    transform: none;
  }

  .meta-chip,
  .level-indicator,
  .meta-indicator,
  .view-indicator {
    transition: none;
  }
}

/* Print Styles */
@media print {
  .logs-container {
    background: white;
    padding: 0;
  }

  .filter-card,
  .log-modal-backdrop {
    display: none;
  }

  .log-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #d1d5db;
    margin-bottom: 1rem;
  }

  .log-card:hover {
    transform: none;
    box-shadow: none;
  }

  .log-card::before {
    print-color-adjust: exact;
  }

  .page-title {
    color: #2c3e50 !important;
    -webkit-text-fill-color: #2c3e50 !important;
  }

  .btn,
  .filter-actions {
    display: none;
  }
}

/* Custom scrollbar for modal */
.log-modal-body::-webkit-scrollbar {
  width: 8px;
}

.log-modal-body::-webkit-scrollbar-track {
  background-color: #f5f5f5;
  border-radius: 4px;
}

.log-modal-body::-webkit-scrollbar-thumb {
  background-color: var(--primary);
  border-radius: 4px;
}

.log-modal-body::-webkit-scrollbar-thumb:hover {
  background-color: #43a047;
}

/* Utility classes */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Loading skeleton for better UX */
.skeleton {
  background: linear-gradient(90deg, #e5e7eb 25%, #f5f5f5 50%, #e5e7eb 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-text {
  height: 1em;
  border-radius: 4px;
  margin-bottom: 0.5em;
}

.skeleton-text:last-child {
  margin-bottom: 0;
}

/* Enhanced focus styles for better accessibility */
.log-card:focus-visible {
  outline: 3px solid var(--primary);
  outline-offset: 2px;
}

.btn:focus-visible {
  outline: 3px solid var(--primary);
  outline-offset: 2px;
}

.form-control:focus-visible {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15);
}

/* Force color application for Angular Material icons */
mat-icon {
  color: inherit !important;
}

.header-icon mat-icon {
  color: #ffffff !important;
}

.filter-header mat-icon {
  color: var(--primary) !important;
}

.section-header mat-icon {
  color: var(--primary) !important;
}

.form-label mat-icon {
  color: var(--primary) !important;
}

.select-arrow,
.search-icon {
  color: #9e9e9e !important;
}

.log-timestamp mat-icon {
  color: #757575 !important;
}

.log-topic mat-icon {
  color: #757575 !important;
}

.meta-indicator mat-icon {
  color: var(--primary) !important;
}

.view-indicator mat-icon {
  color: #757575 !important;
}

.detail-icon {
  color: var(--primary) !important;
}

.modal-icon {
  color: #ffffff !important;
}

.close-btn mat-icon {
  color: #ffffff !important;
}

.empty-icon mat-icon {
  color: #d1d5db !important;
}

/* Force background colors */
.logs-container {
  background-color: #fafafa !important;
}

.header-content {
  background-color: #ffffff !important;
}

.filter-card {
  background-color: #ffffff !important;
}

.logs-section {
  background-color: #ffffff !important;
}

.log-card {
  background-color: #ffffff !important;
}

.log-footer {
  background-color: #fafafa !important;
}

.log-modal {
  background-color: #ffffff !important;
}

.log-modal-body {
  background-color: #ffffff !important;
}

/* Force text colors */
.page-title {
  color: var(--primary) !important;
}

.page-subtitle {
  color: #555555 !important;
}

.filter-header h2 {
  color: #2c3e50 !important;
}

.section-header h2 {
  color: #2c3e50 !important;
}

.log-message {
  color: #2d3748 !important;
}

.log-timestamp {
  color: #757575 !important;
}

.log-topic {
  color: #757575 !important;
}

.chip-key {
  color: #2e7d32 !important;
}

.chip-value {
  color: #155724 !important;
}

.detail-label {
  color: #555555 !important;
}

.detail-value {
  color: #2c3e50 !important;
}

.metadata-key {
  color: #2e7d32 !important;
}

.metadata-value {
  color: #155724 !important;
}

.summary-content {
  color: #2d3748 !important;
}

.loading-content h3 {
  color: #2c3e50 !important;
}

.loading-content p {
  color: #555555 !important;
}

.empty-title {
  color: #2c3e50 !important;
}

.empty-description {
  color: #555555 !important;
}

/* Force border colors */
.header-content {
  border-color: #e5e7eb !important;
}

.filter-card {
  border-color: #e5e7eb !important;
}

.logs-section {
  border-color: #e5e7eb !important;
}

.log-card {
  border-color: #e5e7eb !important;
}

.form-control {
  border-color: #e5e7eb !important;
}

.log-level.info {
  border-color: var(--primary) !important;
}

.log-level.warning {
  border-color: #ffc107 !important;
}

.log-level.error {
  border-color: #dc3545 !important;
}

.log-level.debug {
  border-color: #28a745 !important;
}