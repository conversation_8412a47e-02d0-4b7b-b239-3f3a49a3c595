.site-management-container {
  width: 100%;
  margin: 30px auto;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

/* Header Section */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  animation: slideIn 0.5s ease-out;
  width: 95%;
}

.page-title {
  margin: 0;
}

.title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.title-icon {
  font-size: 30px;
  color: var(--primary);
  background: linear-gradient(45deg, var(--primary), #81c784);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.actions {
  display: flex;
  gap: 15px;
}

.create-button {
  background: linear-gradient(45deg, var(--primary), #81c784);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.create-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.5);
  background: linear-gradient(45deg, #81c784, var(--primary));
}

/* Create and Edit Forms */
.create-form-card,
.edit-form-container {
  /* margin-bottom: 30px; */
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.4s ease-in;
  background: white;
  padding: 25px;
  width: 95%;
}

.create-form-card form,
.edit-form-container form {
  width: 100%;
}

.edit-form-container h2 {
  font-size: 24px;
  color: #2d3748;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.edit-form-container h2 mat-icon {
  color: var(--primary);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
  align-items: end;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
  margin-bottom: 0;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
}

.form-group input,
.form-group select,
.form-group textarea {
  height: 42px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  color: #4a5568;
  background-color: white;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-group textarea {
  height: auto;
  min-height: 100px;
  resize: vertical;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  color: #1a202c;
  background-color: white;
  transition: border-color 0.2s;
}

.form-group select:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.form-group select:invalid {
  color: #718096;
}

.form-group select option {
  color: #1a202c;
}

.form-group input[type="file"] {
  padding: 8px;
  height: auto;
  border: 2px dashed #e2e8f0;
  background-color: #f8fafc;
  cursor: pointer;
}

.form-group input[type="file"]:hover {
  border-color: var(--primary);
  background-color: #f0fff4;
}

.form-group input[type="checkbox"] {
  width: auto;
  height: auto;
  margin-top: 5px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.form-actions button {
  padding: 10px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-actions button[type="button"] {
  background-color: #fff;
  border: 1px solid #e2e8f0;
  color: #4a5568;
}

.form-actions button[type="submit"] {
  background: linear-gradient(45deg, var(--primary), #81c784);
  border: none;
  color: white;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
}

.form-actions button[type="button"]:hover {
  background-color: #f8fafc;
  border-color: #cbd5e0;
}

.form-actions button[type="submit"]:hover {
  background: linear-gradient(45deg, #81c784, var(--primary));
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(76, 175, 80, 0.3);
}

.form-actions button[type="submit"]:disabled {
  background: #e2e8f0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.get-info-button{
  padding: 10px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: linear-gradient(45deg, var(--primary), #81c784);
  border: none;
  color: white;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
}

.get-info-button:hover{
  background: linear-gradient(45deg, #81c784, var(--primary));
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(76, 175, 80, 0.3);
}

.get-info-button:disabled{
  background: #e2e8f0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.view-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  background: #ffffff;
  border-radius: 12px;
  padding: 6px 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  transition: background 0.3s ease;
}

.view-toggle-btn {
  background: transparent;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #444;
  font-weight: 500;
  transition: all 0.25s ease;
  font-size: 14px;
}

.view-toggle-btn i {
  font-size: 20px;
}

.view-toggle-btn.active {
  background: #e8f5e9;
  color: #2e7d32;
  box-shadow: 0 2px 6px rgba(46, 125, 50, 0.2);
  transform: scale(1.02);
}

.view-toggle-btn:hover:not(.active) {
  background: rgba(0, 0, 0, 0.04);
}


/* Search Bar */
.search-bar {
  margin-bottom: 30px;
}

.search-bar input {
  width: 100%;
  max-width: 450px;
  padding: 12px 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f8fafc;
  color: #4a5568;
}

.search-bar input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.search-bar input::placeholder {
  color: #a0aec0;
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  font-size: 18px;
  color: var(--primary);
}

/* Table View */
.table-view {
  width: 100%;
}

/* Pagination */
.card-pagination-container, 
.pagination-container {
  margin-top: 24px;
  margin-bottom: 24px;
}

.pagination-container {
  padding: 24px;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.card-pagination-container {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 16px 24px;
  border: 1px solid #f1f5f9;
}

/* Force override Material paginator background - Multiple selectors for specificity */
.pagination-container mat-paginator,
.card-pagination-container mat-paginator,
mat-paginator.mat-mdc-paginator,
.mat-mdc-paginator {
  background: transparent !important;
  background-color: transparent !important;
}

/* Target the specific Material classes with higher specificity */
.pagination-container .mat-mdc-paginator,
.card-pagination-container .mat-mdc-paginator {
  background: none !important;
  background-color: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Style the paginator wrapper div */
.pagination-container .mat-mdc-paginator > div,
.card-pagination-container .mat-mdc-paginator > div {
  background: transparent !important;
}

/* Navigation buttons styling */
.pagination-container .mat-mdc-icon-button,
.card-pagination-container .mat-mdc-icon-button {
  background: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 6px !important;
  margin: 0 4px !important;
  width: 36px !important;
  height: 36px !important;
  transition: all 0.2s ease !important;
}

.pagination-container .mat-mdc-icon-button:hover,
.card-pagination-container .mat-mdc-icon-button:hover {
  background: #e2e8f0 !important;
  border-color: #cbd5e1 !important;
  transform: translateY(-1px);
}

.pagination-container .mat-mdc-icon-button:disabled,
.card-pagination-container .mat-mdc-icon-button:disabled {
  background: #f1f5f9 !important;
  border-color: #e5e7eb !important;
  opacity: 0.5;
  transform: none;
}

/* Page size dropdown */
.pagination-container .mat-mdc-select,
.card-pagination-container .mat-mdc-select {
  background: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 6px !important;
  margin: 0 8px !important;
}

.pagination-container .mat-mdc-select-trigger,
.card-pagination-container .mat-mdc-select-trigger {
  background: transparent !important;
}

/* Range label text */
.pagination-container .mat-mdc-paginator-range-label,
.card-pagination-container .mat-mdc-paginator-range-label {
  color: #475569 !important;
  font-weight: 500 !important;
  margin: 0 16px !important;
}

/* Page size label */
.pagination-container .mat-mdc-paginator-page-size-label,
.card-pagination-container .mat-mdc-paginator-page-size-label {
  color: #64748b !important;
  font-weight: 400 !important;
}

/* Hide form field underlines */
.pagination-container .mat-mdc-form-field-subscript-wrapper,
.card-pagination-container .mat-mdc-form-field-subscript-wrapper {
  display: none !important;
}

/* Alternative: If the above doesn't work, use this global override */
mat-paginator {
  background: transparent !important;
}

mat-paginator .mat-mdc-paginator {
  background: transparent !important;
  background-color: transparent !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pagination-container {
    padding: 16px;
  }
  
  .card-pagination-container {
    padding: 12px 16px;
  }
  
  .pagination-container .mat-mdc-paginator,
  .card-pagination-container .mat-mdc-paginator {
    flex-wrap: wrap;
    gap: 8px;
  }
}

/* Current Images Preview */
.current-images-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  margin-top: 8px;
}

.current-image-item {
  position: relative;
  aspect-ratio: 1;
}

.current-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.remove-image-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
}

/* Required field indicator */
.required {
  color: #ef4444;
  margin-left: 4px;
}

.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .actions {
    width: 100%;
    flex-direction: column;
    gap: 10px;
  }

  .create-button {
    width: 100%;
    padding: 12px;
    justify-content: center;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 24px;
  }

  .title-icon {
    font-size: 26px;
  }

  .search-bar input {
    max-width: 100%;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions button {
    width: 100%;
  }
}

/* Add to the existing CSS file */
.form-group .mat-error {
  font-size: 12px;
  color: #ef4444;
  margin-top: 4px;
}

.form-group .mat-form-field {
  width: 100%;
}

.form-group .mat-select {
  width: 100%;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 20px;
  margin-right: 10px;
}

.multi-select-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.multi-select {
  padding: 8px;
  border-radius: 8px;
  border: 1px solid #ccc;
  font-size: 14px;
  background-color: #fff;
  transition: border-color 0.2s;
  margin-bottom: 5px;
}

.multi-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.selected-items {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.selected-item {
  display: flex;
  align-items: center;
  background-color: #e6f0ff;
  border: 1px solid #007bff;
  border-radius: 20px;
  padding: 4px 10px;
  font-size: 13px;
  color: #007bff;
}

.remove-btn {
  background: transparent;
  border: none;
  color: #007bff;
  font-size: 16px;
  margin-left: 6px;
  cursor: pointer;
  line-height: 1;
}

.remove-btn:hover {
  color: #dc3545;
}

.info-message {
  font-size: 13px;
  color: #6c757d;
}

.error-message {
  font-size: 13px;
  color: #dc3545;
  margin-top: 4px;
}

/* Base container styles */
.mqtt-loading-container {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  transition: all 0.3s ease;
}

/* Success state */
.mqtt-loading-container.success {
  background-color: #d4edda;
  border-color: #c3e6cb;
}

/* Error state */
.mqtt-loading-container.error {
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

/* Progress bar styles */
.progress-bar-container {
  width: 100%;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #ffee00, #11ba38);
  transition: width 0.3s ease;
  border-radius: 4px;
}

/* Loading text styles */
.loading-text {
  font-size: 12px;
  color: #6c757d;
  text-align: center;
  font-weight: 500;
}

/* Message text styles */
.message-text {
  font-size: 14px;
  text-align: center;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* Success message text */
.mqtt-loading-container.success .message-text {
  color: #155724;
}

.mqtt-loading-container.success .material-icons {
  color: #28a745;
}

/* Error message text */
.mqtt-loading-container.error .message-text {
  color: #721c24;
}

.mqtt-loading-container.error .material-icons {
  color: #dc3545;
}