/* General container styling */
.dashboard-container {
  width: 100%;
  margin-left: 50px;
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-direction: column;
  z-index: 1;
}

.admin-card {
  background: var(--container-bg);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  width: 100%;
  animation: cardEntrance 0.5s ease-out;
}

mat-card-header {
  background: var(--green-main);
  padding: 1.5rem;
  color: var(--card-bg);
}

mat-card-title {
  font-size: 1.5rem !important;
  font-weight: 500 !important;
  margin: 0 !important;
}

mat-card-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Section Styling */
.table-section {
  margin: 0.5rem 0;
}

.table-section + .table-section {
  margin-top: 0.25rem; /* Add smaller margin for subsequent tables */
}

.section-title {
  color: var(--green-main);
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  padding-left: 0.75rem;
  border-left: 4px solid var(--green-main);
  animation: slideIn 0.5s ease-out;
}

.section-divider {
  height: 1px;
  background: linear-gradient(to right, transparent, #e0e0e0, transparent);
  margin: 0.75rem 0;
}

/* Button Group */
.button-group {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.add-button {
  background: var(--green-main) !important; /* Force green color */
  color: white !important;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.add-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.5);
  background: var(--green-main) !important; /* Keep same color on hover */
}
.action-icon {
  margin-right: 8px;
}

/* Pagination controls */
.pagination-controls {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
}

:host ::ng-deep .p-button-icon-only.p-button-text {
  color: var(--green-main);
  transition: all 0.3s ease;
}

:host ::ng-deep .p-button-icon-only.p-button-text:hover {
  background-color: var(--highlight-color);
  transform: scale(1.1);
}

:host ::ng-deep .p-button-icon-only.p-button-text:disabled {
  color: var(--text-secondary);
  cursor: not-allowed;
}

/* Table Styling */
:host ::ng-deep .user-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

:host ::ng-deep .user-table .p-datatable-header {
  background: linear-gradient(45deg, #f5f5f5, #eceff1);
  border-bottom: 2px solid #e2e8f0;
  padding: 15px;
}

:host ::ng-deep .user-table th {
  background: linear-gradient(45deg, #f5f5f5, #eceff1);
  color: var(--text-primary);
  font-weight: 600;
  padding: 15px;
  border-bottom: 2px solid #e2e8f0;
  text-align: left;
}

:host ::ng-deep .user-table .user-row {
  transition: all 0.3s ease;
}

:host ::ng-deep .user-table .user-row:hover {
  background-color: #f9f9f9;
  transform: translateY(-2px);
}

:host ::ng-deep .user-table td {
  padding: 15px;
  color: var(--text-primary);
  border-bottom: 1px solid #edf2f7;
}

.actions-column {
  width: 150px;
}

.actions-cell {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.action-edit {
  color: var(--green-main);
  transition: all 0.3s ease;
}

.action-edit:hover {
  background-color: var(--highlight-color);
  transform: scale(1.1);
}

.action-delete {
  color: var(--danger);
  transition: all 0.3s ease;
}

.action-delete:hover {
  background-color: #ffebee;
  transform: scale(1.1);
}

:host ::ng-deep .p-paginator {
  background: #ffffff;
  border-top: 1px solid #edf2f7;
  padding: 12px;
  border-radius: 0 0 12px 12px;
}

:host ::ng-deep .p-paginator .p-paginator-current {
  color: var(--text-secondary);
  font-style: italic;
}

/* Loading State */
.loading-shade {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

/* Empty State */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
  animation: fadeIn 0.5s ease-out;
}

.no-data-icon {
  font-size: 3rem;
  width: 3rem;
  height: 3rem;
  margin-bottom: 1rem;
  color: var(--green-main);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media screen and (max-width: 960px) {
  .dashboard-container {
    padding: 1rem;
    margin-left: 0;
  }

  .button-group {
    flex-direction: column;
    gap: 0.5rem;
  }

  .add-button {
    width: 100%;
  }

  :host ::ng-deep .user-table th,
  :host ::ng-deep .user-table td {
    padding: 10px;
  }

  .actions-column {
    width: 120px;
  }
}

@media (max-width: 480px) {
  .pagination-controls {
    flex-wrap: wrap;
  }
}