/* rule-generation.component.css */

.card {
  background: var(--white);
  border-radius: 12px;
  border: 1px solid var(--card-border);
  box-shadow: var(--card-shadow);
  overflow: hidden;

  max-width: 98%;
  margin-left: 50px; /* Add this line to push content right of the sidebar */
  padding: 24px ; /* Optional: add some padding for top/right/bottom */
  box-sizing: border-box;
}

/* === HEADER === */

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--card-border);
  background: var(--white);
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
}

/* === LOADING STATES === */

.loading-icon {
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
  color: var(--green-main);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === EMPTY STATES === */

.no-data-message {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary);
  font-size: 16px;
  background: var(--background);
  border-radius: 12px;
  border: 2px dashed var(--card-border);
  position: relative;
}

.no-data-message::before {
  content: '🔍';
  font-size: 64px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.2;
}

/* === SEARCH NO RESULTS === */

.search-no-results {
  background: linear-gradient(135deg, var(--background), #f8f9fa);
  border: 2px dashed var(--green-light);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.1);
}

.search-no-results .no-results-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.search-no-results h3 {
  color: var(--text-primary);
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
}

.search-no-results p {
  color: var(--text-secondary);
  font-size: 16px;
  margin-bottom: 24px;
}

.no-results-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.clear-search-button {
  color: white;
  border-radius: 8px;
  background-color: var(--primary);
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.clear-search-button:hover {
  background: var(--primary-dark) !important;
  transform: translateY(-1px);
}

.clear-filters-button {
  color: var(--green-main) !important;
  border-color: var(--green-main) !important;
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.clear-filters-button:hover {
  background: var(--green-light) !important;
  transform: translateY(-1px);
}

.search-suggestions {
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
  padding: 16px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border-left: 4px solid var(--green-main);
}

.search-suggestions p {
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--text-primary);
}

.search-suggestions ul {
  margin: 0;
  padding-left: 20px;
  color: var(--text-secondary);
}

.search-suggestions li {
  margin-bottom: 4px;
  font-size: 14px;
}

.create-first-rule-button {
  background: var(--green-main) !important;
  color: white !important;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.2s ease;
  margin-top: 16px;
}

.create-first-rule-button:hover {
  background: var(--primary-dark) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(34, 197, 94, 0.3);
}

.search-loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px 20px;
  color: var(--text-secondary);
  font-size: 16px;
  background: var(--background);
  border-radius: 8px;
  margin: 20px;
}

.search-loading-message .loading-icon {
  font-size: 24px !important;
  width: 24px !important;
  height: 24px !important;
  animation: spin 1s linear infinite;
}

.clear-search-button {
  margin-left: 8px;
  padding: 4px 8px;
  min-height: 32px;
  color: white !important;
  border-color: var(--green-main) !important;
  transition: all 0.2s ease;
}

.clear-search-button:hover {
  background: var(--green-light) !important;
  transform: translateY(-1px);
}

.clear-search-button mat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

/* === BUTTONS === */

button.mat-raised-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

button.mat-raised-button:not(.header button.mat-raised-button) {
  background: var(--green-main);
  color: var(--white);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

button.mat-raised-button:not(.header button.mat-raised-button):hover:not(:disabled) {
  background: var(--primary-dark);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

button.mat-raised-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  background: var(--grey-light) !important;
}

button[mat-icon-button] {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

button[mat-icon-button]:hover:not(:disabled) {
  background: var(--hover-bg-color);
  transform: scale(1.1);
}

button[mat-icon-button]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* === CONFIRMATION DIALOG OVERRIDE === */

::ng-deep .confirmation-dialog-panel {
  max-width: 500px !important;
  max-height: 90vh !important;
}

::ng-deep .confirmation-dialog-panel .mat-dialog-container {
  padding: 0 !important;
  background: transparent !important;
  box-shadow: none !important;
  border-radius: 16px !important;
  overflow: visible !important;
}

/* === ANIMATIONS === */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* === MATERIAL ICONS FIX === */

.mat-icon, mat-icon {
  font-family: 'Material Icons' !important;
  font-weight: normal !important;
  font-style: normal !important;
  font-size: 18px !important;
  line-height: 1 !important;
  letter-spacing: normal !important;
  text-transform: none !important;
  display: inline-block !important;
  white-space: nowrap !important;
  word-wrap: normal !important;
  direction: ltr !important;
  -webkit-font-feature-settings: 'liga' !important;
  -webkit-font-smoothing: antialiased !important;
  text-rendering: optimizeLegibility !important;
  -moz-osx-font-smoothing: grayscale !important;
  font-feature-settings: 'liga' !important;
}

button .mat-icon,
button mat-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 18px !important;
  height: 18px !important;
  font-size: 18px !important;
  line-height: 18px !important;
}

/* === RESPONSIVE DESIGN === */

@media (max-width: 768px) {
  .card {
    margin: 8px;
  }

  .no-data-message,
  .search-loading-message {
    margin: 12px;
    padding: 40px 16px;
  }

  .header {
    padding: 16px;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .title {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 18px;
  }

  button.mat-raised-button {
    padding: 6px 12px;
    font-size: 12px;
  }
}

/* === ACCESSIBILITY === */

@media (prefers-reduced-motion: reduce) {
  .loading-icon,
  button,
  .clear-search-button {
    animation: none !important;
    transition: none !important;
    color: white;
  }
  
  button:hover {
    transform: none !important;
  }
}

@media (prefers-contrast: high) {
  .card {
    border: 2px solid currentColor;
  }
}

/* === CONTROLLER STATUS === */
.controller-status-green {
  display: inline-block;
  padding: 2px 10px;
  border: 2px solid #22c55e;
  border-radius: 8px;
  color: #22c55e;
  background: #e6f9ee;
  font-weight: 600;
  font-size: 13px;
  margin-left: 6px;
  margin-right: 6px;
  vertical-align: middle;
}