<ngx-loading
  [show]="isLoading"
  [config]="{
    animationType: 'threeBounce',
    backdropBackgroundColour: 'rgba(0,0,0,0.2)',
    primaryColour: '#10b981',
    secondaryColour: '#10b981',
    tertiaryColour: '#10b981'
  }"
></ngx-loading>

<div class="invoice-container">
  <ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
  <div class="header">
    <h1><span class="material-icons">receipt</span>Gestion des factures</h1>
    <div class="header-actions">
      <button
        class="action-btn"
        (click)="downloadSelected()"
        [disabled]="selectedInvoices.size === 0"
      >
        <span class="material-icons">download</span>
        Télécharger
      </button>
      <button
        class="action-btn"
        (click)="previewSelected()"
        [disabled]="selectedInvoices.size === 0"
      >
        <span class="material-icons">visibility</span>
        Aperçu
      </button>
    </div>
  </div>

  <div class="summary-cards">
    <div class="card">
      <span class="material-icons card-icon">summarize</span>
      <h3>Total Facturé</h3>
      <p class="amount">
        <span class="currency-symbol">€</span>
        <span class="currency-amount">{{
          summary.Total || 0 | number : "1.2-2"
        }}</span>
      </p>
    </div>
    <div class="card">
      <span class="material-icons card-icon">paid</span>
      <h3>Factures Payées</h3>
      <p class="amount">
        <span class="currency-symbol">€</span>
        <span class="currency-amount">{{
          summary.Paid || 0 | number : "1.2-2"
        }}</span>
      </p>
    </div>
    <div class="card">
      <span class="material-icons card-icon">hourglass_empty</span>
      <h3>Factures Impayées</h3>
      <p class="amount">
        <span class="currency-symbol">€</span>
        <span class="currency-amount">{{
          summary.Pending || 0 | number : "1.2-2"
        }}</span>
      </p>
    </div>
  </div>

  <div class="controls">
    <div class="search">
      <input
        type="text"
        placeholder="Rechercher un client..."
        [(ngModel)]="searchQuery"
      />
      <button class="search-btn" (click)="updateFilteredInvoices()">
        <span class="material-icons">search</span>
      </button>
    </div>
    <div class="filter">
      <select [(ngModel)]="statusFilter" (change)="updateFilteredInvoices()">
        <option value="All">All</option>
        <option value="Payé">Payé</option>
        <option value="En attente">En attente</option>
        <option value="Résilié">Résilié</option>
      </select>
    </div>
  </div>

  <div class="table-strip"></div>
  <div class="invoice-table-container table-responsive">
    <!-- Added table-responsive class here -->
    <table class="invoice-table">
      <thead>
        <tr>
          <th class="checkbox-cell">
            <input
              type="checkbox"
              [checked]="allSelected"
              (change)="toggleSelectAll($event)"
            />
          </th>
          <th>Numéro de Facture</th>
          <th>Nom de Client</th>
          <th>Abonnement</th>
          <th>Prix</th>
          <th>Statut</th>
          <th>Date de création</th>
          <th>Date d'écheance</th>
          <th>Date de paiement</th>
          <th>Fréquence de Paiement</th>
          <th class="actions-cell">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr
          *ngFor="let invoice of filteredInvoices"
          [class.selected]="selectedInvoices.has(invoice.factureId!)"
        >
          <td class="checkbox-cell">
            <input
              type="checkbox"
              [checked]="selectedInvoices.has(invoice.factureId!)"
              (change)="toggleSelectInvoice(invoice.factureId!, $event)"
            />
          </td>
          <td>{{ invoice.number }}</td>
          <td>{{ invoice.clientName || "-" }}</td>
          <td>{{ invoice.licenceName }}</td>
          <td>€ {{ invoice.price | number : "1.2-2" }}</td>
          <td>
            <span
              class="status-badge"
              [class.paid]="invoice.status === 'Payé'"
              [class.unpaid]="invoice.status === 'En attente'"
              [class.cancelled]="invoice.status === 'Résilié'"
            >
              {{ invoice.status }}
            </span>
          </td>
          <td>{{ invoice.DateCreation | date : "dd/MM/yy" }}</td>
          <td>{{ invoice.DateEcheance | date : "dd/MM/yy" }}</td>
          <td>
            <ng-container *ngIf="invoice.DatePaiement">
              {{ invoice.DatePaiement | date : "dd/MM/yy" }}
            </ng-container>
            <ng-container *ngIf="!invoice.DatePaiement"> - </ng-container>
          </td>
          <td>{{ invoice.paymentFrequency }}</td>
          <td class="actions-cell">
            <div class="action-dropdown">
              <div
                class="dropdown-btn"
                [class.show]="dropdownOpen === invoice.factureId!"
              >
                <a class="dropdown-item" (click)="downloadInvoice(invoice)">
                  <span class="material-icons">download</span>
                </a>
                <a class="dropdown-btn" (click)="previewInvoice(invoice)">
                  <span class="material-icons">visibility</span>
                </a>
                <a
                  class="dropdown-btn"
                  (click)="activateInvoice(invoice)"
                  *ngIf="invoice.status === 'En attente'"
                >
                  <span class="material-icons">check_circle</span>
                </a>
              </div>
            </div>
          </td>
        </tr>
        <tr *ngIf="filteredInvoices.length === 0 && !isLoading">
          <td colspan="10" class="no-results">Aucune facture trouvée</td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- ADD PAGINATION HERE -->
  <div class="pagination-container">
    <mat-paginator
      [length]="totalInvoices"
      [pageSize]="pageSize"
      [pageIndex]="currentPage"
      [pageSizeOptions]="[5, 10, 25, 50]"
      (page)="onPageChange($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </div>

  <div
    class="invoice-detail"
    *ngIf="invoiceDetail as detail"
    @slideInRight
    style="z-index: 2000"
  >
    <div class="detail-header">
      <h3>DÉTAILS DE LA FACTURE</h3>
      <button class="close-btn" (click)="closeDetail()">×</button>
    </div>

    <div class="from-to">
      <div class="to">
        <h4>À</h4>
        <p>{{ detail.clientName }}</p>
      </div>
    </div>

    <div class="invoice-meta">
      <div>
        <h4>Date</h4>
        <p>{{ detail.date | date : "dd/MM/yy" }}</p>
      </div>
      <div>
        <h4>Sujet</h4>
        <p>{{ detail.subject }}</p>
      </div>
      <div>
        <h4>Facturé à</h4>
        <p>{{ detail.billTo }}</p>
      </div>
      <div>
        <h4>Devise</h4>
        <p>{{ detail.currency }}</p>
      </div>
      <div *ngIf="detail.number">
        <h4>Numéro</h4>
        <p>{{ detail.number }}</p>
      </div>
      <div *ngIf="detail.status">
        <h4>Statut</h4>
        <p>
          <span
            class="status-badge"
            [class.paid]="detail.status === 'Payé'"
            [class.unpaid]="detail.status === 'En attente'"
            [class.cancelled]="detail.status === 'Résilié'"
          >
            {{ detail.status }}
          </span>
        </p>
      </div>
      <div *ngIf="detail.DatePaiement">
        <!-- Conditional display for Date de Paiement in detail view -->
        <h4>Date de Paiement</h4>
        <p>{{ detail.DatePaiement | date : "dd/MM/yy" }}</p>
      </div>
    </div>

    <table class="items-table">
      <thead>
        <tr>
          <th>Description</th>
          <th>Prix</th>
          <th>Montant</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of detail.items">
          <td>{{ item.description }}</td>
          <td>€ {{ item.price | number : "1.2-2" }}</td>
          <td>€ {{ item.amount | number : "1.2-2" }}</td>
        </tr>
      </tbody>
    </table>

    <div class="summary">
      <div class="summary-row">
        <span>Sous-total</span>
        <span>€ {{ detail.subtotal | number : "1.2-2" }}</span>
      </div>
      <div class="summary-row total">
        <span>Total</span>
        <span>€ {{ detail.total | number : "1.2-2" }}</span>
      </div>
    </div>

    <button class="pay-now-btn" *ngIf="canShowPayButton()" (click)="payNow()">
      Payer maintenant
    </button>
  </div>
</div>
