// src/app/pages/devices/hierarchy/services/tree-builder.ts

export function buildTree(data: any[]): any {
  const root: any = {
    id: data[0]?.ClientId,
    name: data[0]?.ClientName || 'Client',
    type: 'client',
    children: [],
  };

  const siteMap = new Map();
  const localMap = new Map<string, Map<string, any>>();
  const controllerMap = new Map<string, Map<string, any>>();

  data.forEach((item) => {
    const siteKey = item.SiteId;
    const localKey = item.LocalId;
    const controllerKey = item.ControllerId;

    if (!siteMap.has(siteKey)) {
      const siteNode = {
        id: item.SiteId,
        name: item.SiteName,
        type: 'site',
        children: [],
      };
      siteMap.set(siteKey, siteNode);
      root.children.push(siteNode);
    }

    const siteNode = siteMap.get(siteKey);

    if (!localMap.has(siteKey)) localMap.set(siteKey, new Map());
    const siteLocals = localMap.get(siteKey)!;

    if (!siteLocals.has(localKey)) {
      const localNode = {
        id: item.LocalId,
        name: item.LocalName,
        type: 'local',
        children: [],
      };
      siteLocals.set(localKey, localNode);
      siteNode.children.push(localNode);
    }

    const localNode = siteLocals.get(localKey);

    if (!controllerMap.has(localKey)) controllerMap.set(localKey, new Map());
    const localControllers = controllerMap.get(localKey)!;

    if (!localControllers.has(controllerKey)) {
      const controllerNode = {
        id: item.ControllerId,
        name: item.HostName,
        type: 'controller',
        children: [],
      };
      localControllers.set(controllerKey, controllerNode);
      localNode.children.push(controllerNode);
    }

    const controllerNode = localControllers.get(controllerKey);
    controllerNode.children.push({
      id: item.Id,
      name: item.DisplayName || item.FriendlyName,
      type: 'sensor',
      data: item,
    });
  });

  return root;
}
